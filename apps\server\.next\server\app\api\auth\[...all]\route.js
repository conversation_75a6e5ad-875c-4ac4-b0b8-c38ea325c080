const CHUNK_PUBLIC_PATH = "server/app/api/auth/[...all]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_922efdd3._.js");
runtime.loadChunk("server/chunks/75d31_next_dist_759cc0ea._.js");
runtime.loadChunk("server/chunks/a7eea_better-call_dist_index_eb4f95ff.js");
runtime.loadChunk("server/chunks/98319_zod_v3_e3189fa0._.js");
runtime.loadChunk("server/chunks/82e04_better-auth_dist_3c0cbfcb._.js");
runtime.loadChunk("server/chunks/96a70_jose_dist_webapi_028820db._.js");
runtime.loadChunk("server/chunks/735df_kysely_dist_esm_operation-node_9908a770._.js");
runtime.loadChunk("server/chunks/735df_kysely_dist_esm_query-builder_715e382f._.js");
runtime.loadChunk("server/chunks/735df_kysely_dist_esm_parser_87a4036e._.js");
runtime.loadChunk("server/chunks/735df_kysely_dist_esm_schema_b7961811._.js");
runtime.loadChunk("server/chunks/735df_kysely_dist_esm_987590e5._.js");
runtime.loadChunk("server/chunks/4e20b_drizzle-orm_e7de45b7._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_9670f71b._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__6f6ee79c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/server/.next-internal/server/app/api/auth/[...all]/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/server/src/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/apps/server/src/app/api/auth/[...all]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
