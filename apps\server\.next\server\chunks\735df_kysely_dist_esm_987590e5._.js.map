{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/object-utils.js"], "sourcesContent": ["/// <reference types=\"./object-utils.d.ts\" />\nexport function isEmpty(obj) {\n    if (Array.isArray(obj) || isString(obj) || isBuffer(obj)) {\n        return obj.length === 0;\n    }\n    else if (obj) {\n        return Object.keys(obj).length === 0;\n    }\n    return false;\n}\nexport function isUndefined(obj) {\n    return typeof obj === 'undefined' || obj === undefined;\n}\nexport function isString(obj) {\n    return typeof obj === 'string';\n}\nexport function isNumber(obj) {\n    return typeof obj === 'number';\n}\nexport function isBoolean(obj) {\n    return typeof obj === 'boolean';\n}\nexport function isNull(obj) {\n    return obj === null;\n}\nexport function isDate(obj) {\n    return obj instanceof Date;\n}\nexport function isBigInt(obj) {\n    return typeof obj === 'bigint';\n}\n// Don't change the returnd type to `obj is <PERSON>uffer` to not create a\n// hard dependency to node.\nexport function isBuffer(obj) {\n    return typeof Buffer !== 'undefined' && Buffer.isBuffer(obj);\n}\nexport function isFunction(obj) {\n    return typeof obj === 'function';\n}\nexport function isObject(obj) {\n    return typeof obj === 'object' && obj !== null;\n}\nexport function isArrayBufferOrView(obj) {\n    return obj instanceof ArrayBuffer || ArrayBuffer.isView(obj);\n}\nexport function isPlainObject(obj) {\n    if (!isObject(obj) || getTag(obj) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(obj) === null) {\n        return true;\n    }\n    let proto = obj;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(obj) === proto;\n}\nexport function getLast(arr) {\n    return arr[arr.length - 1];\n}\nexport function freeze(obj) {\n    return Object.freeze(obj);\n}\nexport function asArray(arg) {\n    if (isReadonlyArray(arg)) {\n        return arg;\n    }\n    else {\n        return [arg];\n    }\n}\nexport function asReadonlyArray(arg) {\n    if (isReadonlyArray(arg)) {\n        return arg;\n    }\n    else {\n        return freeze([arg]);\n    }\n}\nexport function isReadonlyArray(arg) {\n    return Array.isArray(arg);\n}\nexport function noop(obj) {\n    return obj;\n}\nexport function compare(obj1, obj2) {\n    if (isReadonlyArray(obj1) && isReadonlyArray(obj2)) {\n        return compareArrays(obj1, obj2);\n    }\n    else if (isObject(obj1) && isObject(obj2)) {\n        return compareObjects(obj1, obj2);\n    }\n    return obj1 === obj2;\n}\nfunction compareArrays(arr1, arr2) {\n    if (arr1.length !== arr2.length) {\n        return false;\n    }\n    for (let i = 0; i < arr1.length; ++i) {\n        if (!compare(arr1[i], arr2[i])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction compareObjects(obj1, obj2) {\n    if (isBuffer(obj1) && isBuffer(obj2)) {\n        return compareBuffers(obj1, obj2);\n    }\n    else if (isDate(obj1) && isDate(obj2)) {\n        return compareDates(obj1, obj2);\n    }\n    return compareGenericObjects(obj1, obj2);\n}\nfunction compareBuffers(buf1, buf2) {\n    return Buffer.compare(buf1, buf2) === 0;\n}\nfunction compareDates(date1, date2) {\n    return date1.getTime() === date2.getTime();\n}\nfunction compareGenericObjects(obj1, obj2) {\n    const keys1 = Object.keys(obj1);\n    const keys2 = Object.keys(obj2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        if (!compare(obj1[key], obj2[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nconst toString = Object.prototype.toString;\nfunction getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return toString.call(value);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;AACtC,SAAS,QAAQ,GAAG;IACvB,IAAI,MAAM,OAAO,CAAC,QAAQ,SAAS,QAAQ,SAAS,MAAM;QACtD,OAAO,IAAI,MAAM,KAAK;IAC1B,OACK,IAAI,KAAK;QACV,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;IACvC;IACA,OAAO;AACX;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,OAAO,QAAQ,eAAe,QAAQ;AACjD;AACO,SAAS,SAAS,GAAG;IACxB,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,GAAG;IACxB,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,UAAU,GAAG;IACzB,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,OAAO,GAAG;IACtB,OAAO,QAAQ;AACnB;AACO,SAAS,OAAO,GAAG;IACtB,OAAO,eAAe;AAC1B;AACO,SAAS,SAAS,GAAG;IACxB,OAAO,OAAO,QAAQ;AAC1B;AAGO,SAAS,SAAS,GAAG;IACxB,OAAO,OAAO,WAAW,eAAe,OAAO,QAAQ,CAAC;AAC5D;AACO,SAAS,WAAW,GAAG;IAC1B,OAAO,OAAO,QAAQ;AAC1B;AACO,SAAS,SAAS,GAAG;IACxB,OAAO,OAAO,QAAQ,YAAY,QAAQ;AAC9C;AACO,SAAS,oBAAoB,GAAG;IACnC,OAAO,eAAe,eAAe,YAAY,MAAM,CAAC;AAC5D;AACO,SAAS,cAAc,GAAG;IAC7B,IAAI,CAAC,SAAS,QAAQ,OAAO,SAAS,mBAAmB;QACrD,OAAO;IACX;IACA,IAAI,OAAO,cAAc,CAAC,SAAS,MAAM;QACrC,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,MAAO,OAAO,cAAc,CAAC,WAAW,KAAM;QAC1C,QAAQ,OAAO,cAAc,CAAC;IAClC;IACA,OAAO,OAAO,cAAc,CAAC,SAAS;AAC1C;AACO,SAAS,QAAQ,GAAG;IACvB,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;AAC9B;AACO,SAAS,OAAO,GAAG;IACtB,OAAO,OAAO,MAAM,CAAC;AACzB;AACO,SAAS,QAAQ,GAAG;IACvB,IAAI,gBAAgB,MAAM;QACtB,OAAO;IACX,OACK;QACD,OAAO;YAAC;SAAI;IAChB;AACJ;AACO,SAAS,gBAAgB,GAAG;IAC/B,IAAI,gBAAgB,MAAM;QACtB,OAAO;IACX,OACK;QACD,OAAO,OAAO;YAAC;SAAI;IACvB;AACJ;AACO,SAAS,gBAAgB,GAAG;IAC/B,OAAO,MAAM,OAAO,CAAC;AACzB;AACO,SAAS,KAAK,GAAG;IACpB,OAAO;AACX;AACO,SAAS,QAAQ,IAAI,EAAE,IAAI;IAC9B,IAAI,gBAAgB,SAAS,gBAAgB,OAAO;QAChD,OAAO,cAAc,MAAM;IAC/B,OACK,IAAI,SAAS,SAAS,SAAS,OAAO;QACvC,OAAO,eAAe,MAAM;IAChC;IACA,OAAO,SAAS;AACpB;AACA,SAAS,cAAc,IAAI,EAAE,IAAI;IAC7B,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;QAC7B,OAAO;IACX;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAClC,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG;YAC5B,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,IAAI,EAAE,IAAI;IAC9B,IAAI,SAAS,SAAS,SAAS,OAAO;QAClC,OAAO,eAAe,MAAM;IAChC,OACK,IAAI,OAAO,SAAS,OAAO,OAAO;QACnC,OAAO,aAAa,MAAM;IAC9B;IACA,OAAO,sBAAsB,MAAM;AACvC;AACA,SAAS,eAAe,IAAI,EAAE,IAAI;IAC9B,OAAO,OAAO,OAAO,CAAC,MAAM,UAAU;AAC1C;AACA,SAAS,aAAa,KAAK,EAAE,KAAK;IAC9B,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO;AAC5C;AACA,SAAS,sBAAsB,IAAI,EAAE,IAAI;IACrC,MAAM,QAAQ,OAAO,IAAI,CAAC;IAC1B,MAAM,QAAQ,OAAO,IAAI,CAAC;IAC1B,IAAI,MAAM,MAAM,KAAK,MAAM,MAAM,EAAE;QAC/B,OAAO;IACX;IACA,KAAK,MAAM,OAAO,MAAO;QACrB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG;YAChC,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM,WAAW,OAAO,SAAS,CAAC,QAAQ;AAC1C,SAAS,OAAO,KAAK;IACjB,IAAI,SAAS,MAAM;QACf,OAAO,UAAU,YAAY,uBAAuB;IACxD;IACA,OAAO,SAAS,IAAI,CAAC;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/log-once.js"], "sourcesContent": ["/// <reference types=\"./log-once.d.ts\" />\nconst LOGGED_MESSAGES = new Set();\n/**\n * Use for system-level logging, such as deprecation messages.\n * Logs a message and ensures it won't be logged again.\n */\nexport function logOnce(message) {\n    if (LOGGED_MESSAGES.has(message)) {\n        return;\n    }\n    LOGGED_MESSAGES.add(message);\n    console.log(message);\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;AACzC,MAAM,kBAAkB,IAAI;AAKrB,SAAS,QAAQ,OAAO;IAC3B,IAAI,gBAAgB,GAAG,CAAC,UAAU;QAC9B;IACJ;IACA,gBAAgB,GAAG,CAAC;IACpB,QAAQ,GAAG,CAAC;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/random-string.js"], "sourcesContent": ["/// <reference types=\"./random-string.d.ts\" />\nconst CHARS = [\n    'A',\n    'B',\n    'C',\n    'D',\n    'E',\n    'F',\n    'G',\n    'H',\n    'I',\n    'J',\n    'K',\n    'L',\n    'M',\n    'N',\n    'O',\n    'P',\n    'Q',\n    'R',\n    'S',\n    'T',\n    'U',\n    'V',\n    'W',\n    'X',\n    'Y',\n    'Z',\n    'a',\n    'b',\n    'c',\n    'd',\n    'e',\n    'f',\n    'g',\n    'h',\n    'i',\n    'j',\n    'k',\n    'l',\n    'm',\n    'n',\n    'o',\n    'p',\n    'q',\n    'r',\n    's',\n    't',\n    'u',\n    'v',\n    'w',\n    'x',\n    'y',\n    'z',\n    '0',\n    '1',\n    '2',\n    '3',\n    '4',\n    '5',\n    '6',\n    '7',\n    '8',\n    '9',\n];\nexport function randomString(length) {\n    let chars = '';\n    for (let i = 0; i < length; ++i) {\n        chars += randomChar();\n    }\n    return chars;\n}\nfunction randomChar() {\n    return CHARS[~~(Math.random() * CHARS.length)];\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C,MAAM,QAAQ;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,SAAS,aAAa,MAAM;IAC/B,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;QAC7B,SAAS;IACb;IACA,OAAO;AACX;AACA,SAAS;IACL,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/query-id.js"], "sourcesContent": ["/// <reference types=\"./query-id.d.ts\" />\nimport { randomString } from './random-string.js';\nexport function createQueryId() {\n    return new LazyQueryId();\n}\nclass LazyQueryId {\n    #queryId;\n    get queryId() {\n        if (this.#queryId === undefined) {\n            this.#queryId = randomString(8);\n        }\n        return this.#queryId;\n    }\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;AACzC;;AACO,SAAS;IACZ,OAAO,IAAI;AACf;AACA,MAAM;IACF,CAAA,OAAQ,CAAC;IACT,IAAI,UAAU;QACV,IAAI,IAAI,CAAC,CAAA,OAAQ,KAAK,WAAW;YAC7B,IAAI,CAAC,CAAA,OAAQ,GAAG,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE;QACjC;QACA,OAAO,IAAI,CAAC,CAAA,OAAQ;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/require-all-props.js"], "sourcesContent": ["/// <reference types=\"./require-all-props.d.ts\" />\n/**\n * Helper function to check listed properties according to given type. Check if all properties has been used when object is initialised.\n *\n * Example use:\n *\n * ```ts\n * type SomeType = { propA: string; propB?: number; }\n *\n * // propB has to be mentioned even it is optional. It still should be initialized with undefined.\n * const a: SomeType = requireAllProps<SomeType>({ propA: \"value A\", propB: undefined });\n *\n * // checked type is implicit for variable.\n * const b = requireAllProps<SomeType>({ propA: \"value A\", propB: undefined });\n * ```\n *\n * Wrong use of this helper:\n *\n * 1. Omit checked type - all checked properties will be expect as of type never\n *\n * ```ts\n * type SomeType = { propA: string; propB?: number; }\n * // const z: SomeType = requireAllProps({ propC: \"no type will work\" }); // Property 'propA' is missing in type '{ propC: string; }' but required in type 'SomeType'.\n * ```\n *\n * 2. Apply to spreaded object - there is no way how to check in compile time if spreaded object contains all properties\n *\n * ```ts\n * type SomeType = { propA: string; propB?: number; }\n * const y: SomeType = { propA: \"\" }; // valid object according to SomeType declaration\n * // const x = requireAllProps<SomeType>({ ...y }); // Argument of type '{ propA: string; propB?: number; }' is not assignable to parameter of type 'AllProps<SomeType>'.\n * ```\n *\n * @param obj object to check if all properties has been used\n * @returns untouched obj parameter is returned\n */\nexport function requireAllProps(obj) {\n    return obj;\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC;;;AACM,SAAS,gBAAgB,GAAG;IAC/B,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/deferred.js"], "sourcesContent": ["/// <reference types=\"./deferred.d.ts\" />\nexport class Deferred {\n    #promise;\n    #resolve;\n    #reject;\n    constructor() {\n        this.#promise = new Promise((resolve, reject) => {\n            this.#reject = reject;\n            this.#resolve = resolve;\n        });\n    }\n    get promise() {\n        return this.#promise;\n    }\n    resolve = (value) => {\n        if (this.#resolve) {\n            this.#resolve(value);\n        }\n    };\n    reject = (reason) => {\n        if (this.#reject) {\n            this.#reject(reason);\n        }\n    };\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;AAClC,MAAM;IACT,CAAA,OAAQ,CAAC;IACT,CAAA,OAAQ,CAAC;IACT,CAAA,MAAO,CAAC;IACR,aAAc;QACV,IAAI,CAAC,CAAA,OAAQ,GAAG,IAAI,QAAQ,CAAC,SAAS;YAClC,IAAI,CAAC,CAAA,MAAO,GAAG;YACf,IAAI,CAAC,CAAA,OAAQ,GAAG;QACpB;IACJ;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,OAAQ;IACxB;IACA,UAAU,CAAC;QACP,IAAI,IAAI,CAAC,CAAA,OAAQ,EAAE;YACf,IAAI,CAAC,CAAA,OAAQ,CAAC;QAClB;IACJ,EAAE;IACF,SAAS,CAAC;QACN,IAAI,IAAI,CAAC,CAAA,MAAO,EAAE;YACd,IAAI,CAAC,CAAA,MAAO,CAAC;QACjB;IACJ,EAAE;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/provide-controlled-connection.js"], "sourcesContent": ["/// <reference types=\"./provide-controlled-connection.d.ts\" />\nimport { Deferred } from './deferred.js';\nimport { freeze } from './object-utils.js';\nexport async function provideControlledConnection(connectionProvider) {\n    const connectionDefer = new Deferred();\n    const connectionReleaseDefer = new Deferred();\n    connectionProvider\n        .provideConnection(async (connection) => {\n        connectionDefer.resolve(connection);\n        return await connectionReleaseDefer.promise;\n    })\n        .catch((ex) => connectionDefer.reject(ex));\n    // Create composite of the connection and the release method instead of\n    // modifying the connection or creating a new nesting `DatabaseConnection`.\n    // This way we don't accidentally override any methods of 3rd party\n    // connections and don't return wrapped connections to drivers that\n    // expect a certain specific connection class.\n    return freeze({\n        connection: await connectionDefer.promise,\n        release: connectionReleaseDefer.resolve,\n    });\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAC9D;AACA;;;AACO,eAAe,4BAA4B,kBAAkB;IAChE,MAAM,kBAAkB,IAAI,+MAAA,CAAA,WAAQ;IACpC,MAAM,yBAAyB,IAAI,+MAAA,CAAA,WAAQ;IAC3C,mBACK,iBAAiB,CAAC,OAAO;QAC1B,gBAAgB,OAAO,CAAC;QACxB,OAAO,MAAM,uBAAuB,OAAO;IAC/C,GACK,KAAK,CAAC,CAAC,KAAO,gBAAgB,MAAM,CAAC;IAC1C,uEAAuE;IACvE,2EAA2E;IAC3E,mEAAmE;IACnE,mEAAmE;IACnE,8CAA8C;IAC9C,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;QACV,YAAY,MAAM,gBAAgB,OAAO;QACzC,SAAS,uBAAuB,OAAO;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/performance-now.js"], "sourcesContent": ["/// <reference types=\"./performance-now.d.ts\" />\nimport { isFunction } from './object-utils.js';\nexport function performanceNow() {\n    if (typeof performance !== 'undefined' && isFunction(performance.now)) {\n        return performance.now();\n    }\n    else {\n        return Date.now();\n    }\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;;AACO,SAAS;IACZ,IAAI,OAAO,gBAAgB,eAAe,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,YAAY,GAAG,GAAG;QACnE,OAAO,YAAY,GAAG;IAC1B,OACK;QACD,OAAO,KAAK,GAAG;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/log.js"], "sourcesContent": ["/// <reference types=\"./log.d.ts\" />\nimport { freeze, isFunction } from './object-utils.js';\nexport const LOG_LEVELS = freeze(['query', 'error']);\nexport class Log {\n    #levels;\n    #logger;\n    constructor(config) {\n        if (isFunction(config)) {\n            this.#logger = config;\n            this.#levels = freeze({\n                query: true,\n                error: true,\n            });\n        }\n        else {\n            this.#logger = defaultLogger;\n            this.#levels = freeze({\n                query: config.includes('query'),\n                error: config.includes('error'),\n            });\n        }\n    }\n    isLevelEnabled(level) {\n        return this.#levels[level];\n    }\n    async query(getEvent) {\n        if (this.#levels.query) {\n            await this.#logger(getEvent());\n        }\n    }\n    async error(getEvent) {\n        if (this.#levels.error) {\n            await this.#logger(getEvent());\n        }\n    }\n}\nfunction defaultLogger(event) {\n    if (event.level === 'query') {\n        const prefix = `kysely:query:${event.isStream ? 'stream:' : ''}`;\n        console.log(`${prefix} ${event.query.sql}`);\n        console.log(`${prefix} duration: ${event.queryDurationMillis.toFixed(1)}ms`);\n    }\n    else if (event.level === 'error') {\n        if (event.error instanceof Error) {\n            console.error(`kysely:error: ${event.error.stack ?? event.error.message}`);\n        }\n        else {\n            console.error(`kysely:error: ${JSON.stringify({\n                error: event.error,\n                query: event.query.sql,\n                queryDurationMillis: event.queryDurationMillis,\n            })}`);\n        }\n    }\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;AACpC;;AACO,MAAM,aAAa,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAAC;IAAS;CAAQ;AAC5C,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,SAAS;YACpB,IAAI,CAAC,CAAA,MAAO,GAAG;YACf,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBAClB,OAAO;gBACP,OAAO;YACX;QACJ,OACK;YACD,IAAI,CAAC,CAAA,MAAO,GAAG;YACf,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBAClB,OAAO,OAAO,QAAQ,CAAC;gBACvB,OAAO,OAAO,QAAQ,CAAC;YAC3B;QACJ;IACJ;IACA,eAAe,KAAK,EAAE;QAClB,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,MAAM;IAC9B;IACA,MAAM,MAAM,QAAQ,EAAE;QAClB,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,KAAK,EAAE;YACpB,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC;QACvB;IACJ;IACA,MAAM,MAAM,QAAQ,EAAE;QAClB,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,KAAK,EAAE;YACpB,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC;QACvB;IACJ;AACJ;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,MAAM,KAAK,KAAK,SAAS;QACzB,MAAM,SAAS,CAAC,aAAa,EAAE,MAAM,QAAQ,GAAG,YAAY,IAAI;QAChE,QAAQ,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE;QAC1C,QAAQ,GAAG,CAAC,GAAG,OAAO,WAAW,EAAE,MAAM,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC/E,OACK,IAAI,MAAM,KAAK,KAAK,SAAS;QAC9B,IAAI,MAAM,KAAK,YAAY,OAAO;YAC9B,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,MAAM,KAAK,CAAC,KAAK,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;QAC7E,OACK;YACD,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,KAAK,SAAS,CAAC;gBAC1C,OAAO,MAAM,KAAK;gBAClB,OAAO,MAAM,KAAK,CAAC,GAAG;gBACtB,qBAAqB,MAAM,mBAAmB;YAClD,IAAI;QACR;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/compilable.js"], "sourcesContent": ["/// <reference types=\"./compilable.d.ts\" />\nimport { isFunction, isObject } from './object-utils.js';\nexport function isCompilable(value) {\n    return isObject(value) && isFunction(value.compile);\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;AAC3C;;AACO,SAAS,aAAa,KAAK;IAC9B,OAAO,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/util/stack-trace-utils.js"], "sourcesContent": ["/// <reference types=\"./stack-trace-utils.d.ts\" />\nimport { isObject, isString } from './object-utils.js';\nexport function extendStackTrace(err, stackError) {\n    if (isStackHolder(err) && stackError.stack) {\n        // Remove the first line that just says `Error`.\n        const stackExtension = stackError.stack.split('\\n').slice(1).join('\\n');\n        err.stack += `\\n${stackExtension}`;\n        return err;\n    }\n    return err;\n}\nfunction isStackHolder(obj) {\n    return isObject(obj) && isString(obj.stack);\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;AAClD;;AACO,SAAS,iBAAiB,GAAG,EAAE,UAAU;IAC5C,IAAI,cAAc,QAAQ,WAAW,KAAK,EAAE;QACxC,gDAAgD;QAChD,MAAM,iBAAiB,WAAW,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;QAClE,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,gBAAgB;QAClC,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,cAAc,GAAG;IACtB,OAAO,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/expression/expression.js"], "sourcesContent": ["/// <reference types=\"./expression.d.ts\" />\nimport { isOperationNodeSource, } from '../operation-node/operation-node-source.js';\nimport { isObject, isString } from '../util/object-utils.js';\nexport function isExpression(obj) {\n    return isObject(obj) && 'expressionType' in obj && isOperationNodeSource(obj);\n}\nexport function isAliasedExpression(obj) {\n    return (isObject(obj) &&\n        'expression' in obj &&\n        isString(obj.alias) &&\n        isOperationNodeSource(obj));\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;AAC3C;AACA;;;AACO,SAAS,aAAa,GAAG;IAC5B,OAAO,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,oBAAoB,OAAO,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE;AAC7E;AACO,SAAS,oBAAoB,GAAG;IACnC,OAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QACb,gBAAgB,OAChB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,KAClB,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/expression/expression-wrapper.js"], "sourcesContent": ["/// <reference types=\"./expression-wrapper.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { AndNode } from '../operation-node/and-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nimport { OrNode } from '../operation-node/or-node.js';\nimport { ParensNode } from '../operation-node/parens-node.js';\nimport { parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nexport class ExpressionWrapper {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /** @private */\n    get expressionType() {\n        return undefined;\n    }\n    as(alias) {\n        return new AliasedExpressionWrapper(this, alias);\n    }\n    or(...args) {\n        return new OrWrapper(OrNode.create(this.#node, parseValueBinaryOperationOrExpression(args)));\n    }\n    and(...args) {\n        return new AndWrapper(AndNode.create(this.#node, parseValueBinaryOperationOrExpression(args)));\n    }\n    /**\n     * Change the output type of the expression.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `ExpressionWrapper` with a new output type.\n     */\n    $castTo() {\n        return new ExpressionWrapper(this.#node);\n    }\n    /**\n     * Omit null from the expression's type.\n     *\n     * This function can be useful in cases where you know an expression can't be\n     * null, but Kysely is unable to infer it.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of `this` with a new output type.\n     */\n    $notNull() {\n        return new ExpressionWrapper(this.#node);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\nexport class AliasedExpressionWrapper {\n    #expr;\n    #alias;\n    constructor(expr, alias) {\n        this.#expr = expr;\n        this.#alias = alias;\n    }\n    /** @private */\n    get expression() {\n        return this.#expr;\n    }\n    /** @private */\n    get alias() {\n        return this.#alias;\n    }\n    toOperationNode() {\n        return AliasNode.create(this.#expr.toOperationNode(), isOperationNodeSource(this.#alias)\n            ? this.#alias.toOperationNode()\n            : IdentifierNode.create(this.#alias));\n    }\n}\nexport class OrWrapper {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /** @private */\n    get expressionType() {\n        return undefined;\n    }\n    as(alias) {\n        return new AliasedExpressionWrapper(this, alias);\n    }\n    or(...args) {\n        return new OrWrapper(OrNode.create(this.#node, parseValueBinaryOperationOrExpression(args)));\n    }\n    /**\n     * Change the output type of the expression.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `OrWrapper` with a new output type.\n     */\n    $castTo() {\n        return new OrWrapper(this.#node);\n    }\n    toOperationNode() {\n        return ParensNode.create(this.#node);\n    }\n}\nexport class AndWrapper {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /** @private */\n    get expressionType() {\n        return undefined;\n    }\n    as(alias) {\n        return new AliasedExpressionWrapper(this, alias);\n    }\n    and(...args) {\n        return new AndWrapper(AndNode.create(this.#node, parseValueBinaryOperationOrExpression(args)));\n    }\n    /**\n     * Change the output type of the expression.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `AndWrapper` with a new output type.\n     */\n    $castTo() {\n        return new AndWrapper(this.#node);\n    }\n    toOperationNode() {\n        return ParensNode.create(this.#node);\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;;;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,aAAa,GACb,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,yBAAyB,IAAI,EAAE;IAC9C;IACA,GAAG,GAAG,IAAI,EAAE;QACR,OAAO,IAAI,UAAU,8NAAA,CAAA,SAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;IACzF;IACA,IAAI,GAAG,IAAI,EAAE;QACT,OAAO,IAAI,WAAW,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;IAC3F;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,kBAAkB,IAAI,CAAC,CAAA,IAAK;IAC3C;IACA;;;;;;;;KAQC,GACD,WAAW;QACP,OAAO,IAAI,kBAAkB,IAAI,CAAC,CAAA,IAAK;IAC3C;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,CAAA,KAAM,CAAC;IACP,YAAY,IAAI,EAAE,KAAK,CAAE;QACrB,IAAI,CAAC,CAAA,IAAK,GAAG;QACb,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,aAAa,GACb,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;IACA,aAAa,GACb,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,eAAe,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,IACjF,IAAI,CAAC,CAAA,KAAM,CAAC,eAAe,KAC3B,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IAC3C;AACJ;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,aAAa,GACb,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,yBAAyB,IAAI,EAAE;IAC9C;IACA,GAAG,GAAG,IAAI,EAAE;QACR,OAAO,IAAI,UAAU,8NAAA,CAAA,SAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;IACzF;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,UAAU,IAAI,CAAC,CAAA,IAAK;IACnC;IACA,kBAAkB;QACd,OAAO,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK;IACvC;AACJ;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,aAAa,GACb,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,yBAAyB,IAAI,EAAE;IAC9C;IACA,IAAI,GAAG,IAAI,EAAE;QACT,OAAO,IAAI,WAAW,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;IAC3F;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,WAAW,IAAI,CAAC,CAAA,IAAK;IACpC;IACA,kBAAkB;QACd,OAAO,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/expression/expression-builder.js"], "sourcesContent": ["/// <reference types=\"./expression-builder.d.ts\" />\nimport { createSelectQueryBuilder, } from '../query-builder/select-query-builder.js';\nimport { SelectQueryNode } from '../operation-node/select-query-node.js';\nimport { parseTableExpressionOrList, parseTable, } from '../parser/table-parser.js';\nimport { WithSchemaPlugin } from '../plugin/with-schema/with-schema-plugin.js';\nimport { createQueryId } from '../util/query-id.js';\nimport { createFunctionModule, } from '../query-builder/function-module.js';\nimport { parseJSONReference, parseReferenceExpression, parseStringReference, } from '../parser/reference-parser.js';\nimport { parseFilterList, parseFilterObject, parseValueBinaryOperation, parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nimport { ParensNode } from '../operation-node/parens-node.js';\nimport { ExpressionWrapper } from './expression-wrapper.js';\nimport { OperatorNode, } from '../operation-node/operator-node.js';\nimport { parseUnaryOperation } from '../parser/unary-operation-parser.js';\nimport { parseSafeImmediateValue, parseValueExpression, } from '../parser/value-parser.js';\nimport { NOOP_QUERY_EXECUTOR } from '../query-executor/noop-query-executor.js';\nimport { CaseBuilder } from '../query-builder/case-builder.js';\nimport { CaseNode } from '../operation-node/case-node.js';\nimport { isReadonlyArray, isUndefined } from '../util/object-utils.js';\nimport { JSONPathBuilder } from '../query-builder/json-path-builder.js';\nimport { BinaryOperationNode } from '../operation-node/binary-operation-node.js';\nimport { AndNode } from '../operation-node/and-node.js';\nimport { TupleNode } from '../operation-node/tuple-node.js';\nimport { JSONPathNode } from '../operation-node/json-path-node.js';\nimport { parseDataTypeExpression, } from '../parser/data-type-parser.js';\nimport { CastNode } from '../operation-node/cast-node.js';\nexport function createExpressionBuilder(executor = NOOP_QUERY_EXECUTOR) {\n    function binary(lhs, op, rhs) {\n        return new ExpressionWrapper(parseValueBinaryOperation(lhs, op, rhs));\n    }\n    function unary(op, expr) {\n        return new ExpressionWrapper(parseUnaryOperation(op, expr));\n    }\n    const eb = Object.assign(binary, {\n        fn: undefined,\n        eb: undefined,\n        selectFrom(table) {\n            return createSelectQueryBuilder({\n                queryId: createQueryId(),\n                executor,\n                queryNode: SelectQueryNode.createFrom(parseTableExpressionOrList(table)),\n            });\n        },\n        case(reference) {\n            return new CaseBuilder({\n                node: CaseNode.create(isUndefined(reference)\n                    ? undefined\n                    : parseReferenceExpression(reference)),\n            });\n        },\n        ref(reference, op) {\n            if (isUndefined(op)) {\n                return new ExpressionWrapper(parseStringReference(reference));\n            }\n            return new JSONPathBuilder(parseJSONReference(reference, op));\n        },\n        jsonPath() {\n            return new JSONPathBuilder(JSONPathNode.create());\n        },\n        table(table) {\n            return new ExpressionWrapper(parseTable(table));\n        },\n        val(value) {\n            return new ExpressionWrapper(parseValueExpression(value));\n        },\n        refTuple(...values) {\n            return new ExpressionWrapper(TupleNode.create(values.map(parseReferenceExpression)));\n        },\n        tuple(...values) {\n            return new ExpressionWrapper(TupleNode.create(values.map(parseValueExpression)));\n        },\n        lit(value) {\n            return new ExpressionWrapper(parseSafeImmediateValue(value));\n        },\n        unary,\n        not(expr) {\n            return unary('not', expr);\n        },\n        exists(expr) {\n            return unary('exists', expr);\n        },\n        neg(expr) {\n            return unary('-', expr);\n        },\n        between(expr, start, end) {\n            return new ExpressionWrapper(BinaryOperationNode.create(parseReferenceExpression(expr), OperatorNode.create('between'), AndNode.create(parseValueExpression(start), parseValueExpression(end))));\n        },\n        betweenSymmetric(expr, start, end) {\n            return new ExpressionWrapper(BinaryOperationNode.create(parseReferenceExpression(expr), OperatorNode.create('between symmetric'), AndNode.create(parseValueExpression(start), parseValueExpression(end))));\n        },\n        and(exprs) {\n            if (isReadonlyArray(exprs)) {\n                return new ExpressionWrapper(parseFilterList(exprs, 'and'));\n            }\n            return new ExpressionWrapper(parseFilterObject(exprs, 'and'));\n        },\n        or(exprs) {\n            if (isReadonlyArray(exprs)) {\n                return new ExpressionWrapper(parseFilterList(exprs, 'or'));\n            }\n            return new ExpressionWrapper(parseFilterObject(exprs, 'or'));\n        },\n        parens(...args) {\n            const node = parseValueBinaryOperationOrExpression(args);\n            if (ParensNode.is(node)) {\n                // No double wrapping.\n                return new ExpressionWrapper(node);\n            }\n            else {\n                return new ExpressionWrapper(ParensNode.create(node));\n            }\n        },\n        cast(expr, dataType) {\n            return new ExpressionWrapper(CastNode.create(parseReferenceExpression(expr), parseDataTypeExpression(dataType)));\n        },\n        withSchema(schema) {\n            return createExpressionBuilder(executor.withPluginAtFront(new WithSchemaPlugin(schema)));\n        },\n    });\n    eb.fn = createFunctionModule();\n    eb.eb = eb;\n    return eb;\n}\nexport function expressionBuilder(_) {\n    return createExpressionBuilder();\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AACO,SAAS,wBAAwB,WAAW,6OAAA,CAAA,sBAAmB;IAClE,SAAS,OAAO,GAAG,EAAE,EAAE,EAAE,GAAG;QACxB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,sOAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,IAAI;IACpE;IACA,SAAS,MAAM,EAAE,EAAE,IAAI;QACnB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,qOAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI;IACzD;IACA,MAAM,KAAK,OAAO,MAAM,CAAC,QAAQ;QAC7B,IAAI;QACJ,IAAI;QACJ,YAAW,KAAK;YACZ,OAAO,CAAA,GAAA,6OAAA,CAAA,2BAAwB,AAAD,EAAE;gBAC5B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;gBACrB;gBACA,WAAW,2OAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE;YACrE;QACJ;QACA,MAAK,SAAS;YACV,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC;gBACnB,MAAM,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,aAC5B,YACA,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE;YACnC;QACJ;QACA,KAAI,SAAS,EAAE,EAAE;YACb,IAAI,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,KAAK;gBACjB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,4NAAA,CAAA,uBAAoB,AAAD,EAAE;YACtD;YACA,OAAO,IAAI,0OAAA,CAAA,kBAAe,CAAC,CAAA,GAAA,4NAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;QAC7D;QACA;YACI,OAAO,IAAI,0OAAA,CAAA,kBAAe,CAAC,wOAAA,CAAA,eAAY,CAAC,MAAM;QAClD;QACA,OAAM,KAAK;YACP,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;QAC5C;QACA,KAAI,KAAK;YACL,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QACtD;QACA,UAAS,GAAG,MAAM;YACd,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,4NAAA,CAAA,2BAAwB;QACrF;QACA,OAAM,GAAG,MAAM;YACX,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,wNAAA,CAAA,uBAAoB;QACjF;QACA,KAAI,KAAK;YACL,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,wNAAA,CAAA,0BAAuB,AAAD,EAAE;QACzD;QACA;QACA,KAAI,IAAI;YACJ,OAAO,MAAM,OAAO;QACxB;QACA,QAAO,IAAI;YACP,OAAO,MAAM,UAAU;QAC3B;QACA,KAAI,IAAI;YACJ,OAAO,MAAM,KAAK;QACtB;QACA,SAAQ,IAAI,EAAE,KAAK,EAAE,GAAG;YACpB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,YAAY,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC7L;QACA,kBAAiB,IAAI,EAAE,KAAK,EAAE,GAAG;YAC7B,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,sBAAsB,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QACvM;QACA,KAAI,KAAK;YACL,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;gBACxB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,sOAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YACxD;YACA,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,sOAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QAC1D;QACA,IAAG,KAAK;YACJ,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;gBACxB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,sOAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YACxD;YACA,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,CAAA,GAAA,sOAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;QAC1D;QACA,QAAO,GAAG,IAAI;YACV,MAAM,OAAO,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;YACnD,IAAI,kOAAA,CAAA,aAAU,CAAC,EAAE,CAAC,OAAO;gBACrB,sBAAsB;gBACtB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC;YACjC,OACK;gBACD,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YACnD;QACJ;QACA,MAAK,IAAI,EAAE,QAAQ;YACf,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QACzG;QACA,YAAW,MAAM;YACb,OAAO,wBAAwB,SAAS,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;QACnF;IACJ;IACA,GAAG,EAAE,GAAG,CAAA,GAAA,qOAAA,CAAA,uBAAoB,AAAD;IAC3B,GAAG,EAAE,GAAG;IACR,OAAO;AACX;AACO,SAAS,kBAAkB,CAAC;IAC/B,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dynamic/dynamic-reference-builder.js"], "sourcesContent": ["/// <reference types=\"./dynamic-reference-builder.d.ts\" />\nimport { isOperationNodeSource, } from '../operation-node/operation-node-source.js';\nimport { parseSimpleReferenceExpression } from '../parser/reference-parser.js';\nimport { isObject, isString } from '../util/object-utils.js';\nexport class DynamicReferenceBuilder {\n    #dynamicReference;\n    get dynamicReference() {\n        return this.#dynamicReference;\n    }\n    /**\n     * @private\n     *\n     * This needs to be here just so that the typings work. Without this\n     * the generated .d.ts file contains no reference to the type param R\n     * which causes this type to be equal to DynamicReferenceBuilder with\n     * any R.\n     */\n    get refType() {\n        return undefined;\n    }\n    constructor(reference) {\n        this.#dynamicReference = reference;\n    }\n    toOperationNode() {\n        return parseSimpleReferenceExpression(this.#dynamicReference);\n    }\n}\nexport function isDynamicReferenceBuilder(obj) {\n    return (isObject(obj) &&\n        isOperationNodeSource(obj) &&\n        isString(obj.dynamicReference));\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAC1D;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,gBAAiB,CAAC;IAClB,IAAI,mBAAmB;QACnB,OAAO,IAAI,CAAC,CAAA,gBAAiB;IACjC;IACA;;;;;;;KAOC,GACD,IAAI,UAAU;QACV,OAAO;IACX;IACA,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,CAAA,gBAAiB,GAAG;IAC7B;IACA,kBAAkB;QACd,OAAO,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE,IAAI,CAAC,CAAA,gBAAiB;IAChE;AACJ;AACO,SAAS,0BAA0B,GAAG;IACzC,OAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QACb,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,QACtB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,gBAAgB;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dynamic/dynamic-table-builder.js"], "sourcesContent": ["/// <reference types=\"./dynamic-table-builder.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isOperationNodeSource, } from '../operation-node/operation-node-source.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { isObject, isString } from '../util/object-utils.js';\nexport class DynamicTableBuilder {\n    #table;\n    get table() {\n        return this.#table;\n    }\n    constructor(table) {\n        this.#table = table;\n    }\n    as(alias) {\n        return new AliasedDynamicTableBuilder(this.#table, alias);\n    }\n}\nexport class AliasedDynamicTableBuilder {\n    #table;\n    #alias;\n    get table() {\n        return this.#table;\n    }\n    get alias() {\n        return this.#alias;\n    }\n    constructor(table, alias) {\n        this.#table = table;\n        this.#alias = alias;\n    }\n    toOperationNode() {\n        return AliasNode.create(parseTable(this.#table), IdentifierNode.create(this.#alias));\n    }\n}\nexport function isAliasedDynamicTableBuilder(obj) {\n    return (isObject(obj) &&\n        isOperationNodeSource(obj) &&\n        isString(obj.table) &&\n        isString(obj.alias));\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AACtD;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,2BAA2B,IAAI,CAAC,CAAA,KAAM,EAAE;IACvD;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,CAAA,KAAM,CAAC;IACP,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,YAAY,KAAK,EAAE,KAAK,CAAE;QACtB,IAAI,CAAC,CAAA,KAAM,GAAG;QACd,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,GAAG,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IACtF;AACJ;AACO,SAAS,6BAA6B,GAAG;IAC5C,OAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QACb,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,QACtB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,KAClB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dynamic/dynamic.js"], "sourcesContent": ["/// <reference types=\"./dynamic.d.ts\" />\nimport { DynamicReferenceBuilder } from './dynamic-reference-builder.js';\nimport { DynamicTableBuilder } from './dynamic-table-builder.js';\nexport class DynamicModule {\n    /**\n     * Creates a dynamic reference to a column that is not know at compile time.\n     *\n     * Kysely is built in a way that by default you can't refer to tables or columns\n     * that are not actually visible in the current query and context. This is all\n     * done by TypeScript at compile time, which means that you need to know the\n     * columns and tables at compile time. This is not always the case of course.\n     *\n     * This method is meant to be used in those cases where the column names\n     * come from the user input or are not otherwise known at compile time.\n     *\n     * WARNING! Unlike values, column names are not escaped by the database engine\n     * or Kysely and if you pass in unchecked column names using this method, you\n     * create an SQL injection vulnerability. Always __always__ validate the user\n     * input before passing it to this method.\n     *\n     * There are couple of examples below for some use cases, but you can pass\n     * `ref` to other methods as well. If the types allow you to pass a `ref`\n     * value to some place, it should work.\n     *\n     * ### Examples\n     *\n     * Filter by a column not know at compile time:\n     *\n     * ```ts\n     * async function someQuery(filterColumn: string, filterValue: string) {\n     *   const { ref } = db.dynamic\n     *\n     *   return await db\n     *     .selectFrom('person')\n     *     .selectAll()\n     *     .where(ref(filterColumn), '=', filterValue)\n     *     .execute()\n     * }\n     *\n     * someQuery('first_name', 'Arnold')\n     * someQuery('person.last_name', 'Aniston')\n     * ```\n     *\n     * Order by a column not know at compile time:\n     *\n     * ```ts\n     * async function someQuery(orderBy: string) {\n     *   const { ref } = db.dynamic\n     *\n     *   return await db\n     *     .selectFrom('person')\n     *     .select('person.first_name as fn')\n     *     .orderBy(ref(orderBy))\n     *     .execute()\n     * }\n     *\n     * someQuery('fn')\n     * ```\n     *\n     * In this example we add selections dynamically:\n     *\n     * ```ts\n     * const { ref } = db.dynamic\n     *\n     * // Some column name provided by the user. Value not known at compile time.\n     * const columnFromUserInput: PossibleColumns = 'birthdate';\n     *\n     * // A type that lists all possible values `columnFromUserInput` can have.\n     * // You can use `keyof Person` if any column of an interface is allowed.\n     * type PossibleColumns = 'last_name' | 'first_name' | 'birthdate'\n     *\n     * const [person] = await db.selectFrom('person')\n     *   .select([\n     *     ref<PossibleColumns>(columnFromUserInput),\n     *     'id'\n     *   ])\n     *   .execute()\n     *\n     * // The resulting type contains all `PossibleColumns` as optional fields\n     * // because we cannot know which field was actually selected before\n     * // running the code.\n     * const lastName: string | null | undefined = person?.last_name\n     * const firstName: string | undefined = person?.first_name\n     * const birthDate: Date | null | undefined = person?.birthdate\n     *\n     * // The result type also contains the compile time selection `id`.\n     * person?.id\n     * ```\n     */\n    ref(reference) {\n        return new DynamicReferenceBuilder(reference);\n    }\n    /**\n     * Creates a table reference to a table that's not fully known at compile time.\n     *\n     * The type `T` is allowed to be a union of multiple tables.\n     *\n     * <!-- siteExample(\"select\", \"Generic find query\", 130) -->\n     *\n     * A generic type-safe helper function for finding a row by a column value:\n     *\n     * ```ts\n     * import { SelectType } from 'kysely'\n     * import { Database } from 'type-editor'\n     *\n     * async function getRowByColumn<\n     *   T extends keyof Database,\n     *   C extends keyof Database[T] & string,\n     *   V extends SelectType<Database[T][C]>,\n     * >(t: T, c: C, v: V) {\n     *   // We need to use the dynamic module since the table name\n     *   // is not known at compile time.\n     *   const { table, ref } = db.dynamic\n     *\n     *   return await db\n     *     .selectFrom(table(t).as('t'))\n     *     .selectAll()\n     *     .where(ref(c), '=', v)\n     *     .orderBy('t.id')\n     *     .executeTakeFirstOrThrow()\n     * }\n     *\n     * const person = await getRowByColumn('person', 'first_name', 'Arnold')\n     * ```\n     */\n    table(table) {\n        return new DynamicTableBuilder(table);\n    }\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;AACxC;AACA;;;AACO,MAAM;IACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoFC,GACD,IAAI,SAAS,EAAE;QACX,OAAO,IAAI,yOAAA,CAAA,0BAAuB,CAAC;IACvC;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgCC,GACD,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,qOAAA,CAAA,sBAAmB,CAAC;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/plugin/with-schema/with-schema-transformer.js"], "sourcesContent": ["/// <reference types=\"./with-schema-transformer.d.ts\" />\nimport { AliasNode } from '../../operation-node/alias-node.js';\nimport { IdentifierNode } from '../../operation-node/identifier-node.js';\nimport { ListNode } from '../../operation-node/list-node.js';\nimport { OperationNodeTransformer } from '../../operation-node/operation-node-transformer.js';\nimport { SchemableIdentifierNode } from '../../operation-node/schemable-identifier-node.js';\nimport { TableNode } from '../../operation-node/table-node.js';\nimport { freeze } from '../../util/object-utils.js';\n// This object exist only so that we get a type error when a new RootOperationNode\n// is added. If you get a type error here, make sure to add the new root node and\n// handle it correctly in the transformer.\n//\n// DO NOT REFACTOR THIS EVEN IF IT SEEMS USELESS TO YOU!\nconst ROOT_OPERATION_NODES = freeze({\n    AlterTableNode: true,\n    CreateIndexNode: true,\n    CreateSchemaNode: true,\n    CreateTableNode: true,\n    CreateTypeNode: true,\n    CreateViewNode: true,\n    RefreshMaterializedViewNode: true,\n    DeleteQueryNode: true,\n    DropIndexNode: true,\n    DropSchemaNode: true,\n    DropTableNode: true,\n    DropTypeNode: true,\n    DropViewNode: true,\n    InsertQueryNode: true,\n    RawNode: true,\n    SelectQueryNode: true,\n    UpdateQueryNode: true,\n    MergeQueryNode: true,\n});\nconst SCHEMALESS_FUNCTIONS = {\n    json_agg: true,\n    to_json: true,\n};\nexport class WithSchemaTransformer extends OperationNodeTransformer {\n    #schema;\n    #schemableIds = new Set();\n    #ctes = new Set();\n    constructor(schema) {\n        super();\n        this.#schema = schema;\n    }\n    transformNodeImpl(node, queryId) {\n        if (!this.#isRootOperationNode(node)) {\n            return super.transformNodeImpl(node, queryId);\n        }\n        const ctes = this.#collectCTEs(node);\n        for (const cte of ctes) {\n            this.#ctes.add(cte);\n        }\n        const tables = this.#collectSchemableIds(node);\n        for (const table of tables) {\n            this.#schemableIds.add(table);\n        }\n        const transformed = super.transformNodeImpl(node, queryId);\n        for (const table of tables) {\n            this.#schemableIds.delete(table);\n        }\n        for (const cte of ctes) {\n            this.#ctes.delete(cte);\n        }\n        return transformed;\n    }\n    transformSchemableIdentifier(node, queryId) {\n        const transformed = super.transformSchemableIdentifier(node, queryId);\n        if (transformed.schema || !this.#schemableIds.has(node.identifier.name)) {\n            return transformed;\n        }\n        return {\n            ...transformed,\n            schema: IdentifierNode.create(this.#schema),\n        };\n    }\n    transformReferences(node, queryId) {\n        const transformed = super.transformReferences(node, queryId);\n        if (transformed.table.table.schema) {\n            return transformed;\n        }\n        return {\n            ...transformed,\n            table: TableNode.createWithSchema(this.#schema, transformed.table.table.identifier.name),\n        };\n    }\n    transformAggregateFunction(node, queryId) {\n        return {\n            ...super.transformAggregateFunction({ ...node, aggregated: [] }, queryId),\n            aggregated: this.#transformTableArgsWithoutSchemas(node, queryId, 'aggregated'),\n        };\n    }\n    transformFunction(node, queryId) {\n        return {\n            ...super.transformFunction({ ...node, arguments: [] }, queryId),\n            arguments: this.#transformTableArgsWithoutSchemas(node, queryId, 'arguments'),\n        };\n    }\n    #transformTableArgsWithoutSchemas(node, queryId, argsKey) {\n        return SCHEMALESS_FUNCTIONS[node.func]\n            ? node[argsKey].map((arg) => !TableNode.is(arg) || arg.table.schema\n                ? this.transformNode(arg, queryId)\n                : {\n                    ...arg,\n                    table: this.transformIdentifier(arg.table.identifier, queryId),\n                })\n            : this.transformNodeList(node[argsKey], queryId);\n    }\n    #isRootOperationNode(node) {\n        return node.kind in ROOT_OPERATION_NODES;\n    }\n    #collectSchemableIds(node) {\n        const schemableIds = new Set();\n        if ('name' in node && node.name && SchemableIdentifierNode.is(node.name)) {\n            this.#collectSchemableId(node.name, schemableIds);\n        }\n        if ('from' in node && node.from) {\n            for (const from of node.from.froms) {\n                this.#collectSchemableIdsFromTableExpr(from, schemableIds);\n            }\n        }\n        if ('into' in node && node.into) {\n            this.#collectSchemableIdsFromTableExpr(node.into, schemableIds);\n        }\n        if ('table' in node && node.table) {\n            this.#collectSchemableIdsFromTableExpr(node.table, schemableIds);\n        }\n        if ('joins' in node && node.joins) {\n            for (const join of node.joins) {\n                this.#collectSchemableIdsFromTableExpr(join.table, schemableIds);\n            }\n        }\n        if ('using' in node && node.using) {\n            this.#collectSchemableIdsFromTableExpr(node.using, schemableIds);\n        }\n        return schemableIds;\n    }\n    #collectCTEs(node) {\n        const ctes = new Set();\n        if ('with' in node && node.with) {\n            this.#collectCTEIds(node.with, ctes);\n        }\n        return ctes;\n    }\n    #collectSchemableIdsFromTableExpr(node, schemableIds) {\n        if (TableNode.is(node)) {\n            this.#collectSchemableId(node.table, schemableIds);\n        }\n        else if (AliasNode.is(node) && TableNode.is(node.node)) {\n            this.#collectSchemableId(node.node.table, schemableIds);\n        }\n        else if (ListNode.is(node)) {\n            for (const table of node.items) {\n                this.#collectSchemableIdsFromTableExpr(table, schemableIds);\n            }\n        }\n    }\n    #collectSchemableId(node, schemableIds) {\n        const id = node.identifier.name;\n        if (!this.#schemableIds.has(id) && !this.#ctes.has(id)) {\n            schemableIds.add(id);\n        }\n    }\n    #collectCTEIds(node, ctes) {\n        for (const expr of node.expressions) {\n            const cteId = expr.name.table.table.identifier.name;\n            if (!this.#ctes.has(cteId)) {\n                ctes.add(cteId);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,kFAAkF;AAClF,iFAAiF;AACjF,0CAA0C;AAC1C,EAAE;AACF,wDAAwD;AACxD,MAAM,uBAAuB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAChC,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,iBAAiB;IACjB,gBAAgB;IAChB,gBAAgB;IAChB,6BAA6B;IAC7B,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,eAAe;IACf,cAAc;IACd,cAAc;IACd,iBAAiB;IACjB,SAAS;IACT,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB;AACpB;AACA,MAAM,uBAAuB;IACzB,UAAU;IACV,SAAS;AACb;AACO,MAAM,8BAA8B,oPAAA,CAAA,2BAAwB;IAC/D,CAAA,MAAO,CAAC;IACR,CAAA,YAAa,GAAG,IAAI,MAAM;IAC1B,CAAA,IAAK,GAAG,IAAI,MAAM;IAClB,YAAY,MAAM,CAAE;QAChB,KAAK;QACL,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,kBAAkB,IAAI,EAAE,OAAO,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,CAAA,mBAAoB,CAAC,OAAO;YAClC,OAAO,KAAK,CAAC,kBAAkB,MAAM;QACzC;QACA,MAAM,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC;QAC/B,KAAK,MAAM,OAAO,KAAM;YACpB,IAAI,CAAC,CAAA,IAAK,CAAC,GAAG,CAAC;QACnB;QACA,MAAM,SAAS,IAAI,CAAC,CAAA,mBAAoB,CAAC;QACzC,KAAK,MAAM,SAAS,OAAQ;YACxB,IAAI,CAAC,CAAA,YAAa,CAAC,GAAG,CAAC;QAC3B;QACA,MAAM,cAAc,KAAK,CAAC,kBAAkB,MAAM;QAClD,KAAK,MAAM,SAAS,OAAQ;YACxB,IAAI,CAAC,CAAA,YAAa,CAAC,MAAM,CAAC;QAC9B;QACA,KAAK,MAAM,OAAO,KAAM;YACpB,IAAI,CAAC,CAAA,IAAK,CAAC,MAAM,CAAC;QACtB;QACA,OAAO;IACX;IACA,6BAA6B,IAAI,EAAE,OAAO,EAAE;QACxC,MAAM,cAAc,KAAK,CAAC,6BAA6B,MAAM;QAC7D,IAAI,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC,CAAA,YAAa,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC,IAAI,GAAG;YACrE,OAAO;QACX;QACA,OAAO;YACH,GAAG,WAAW;YACd,QAAQ,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO;QAC9C;IACJ;IACA,oBAAoB,IAAI,EAAE,OAAO,EAAE;QAC/B,MAAM,cAAc,KAAK,CAAC,oBAAoB,MAAM;QACpD,IAAI,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;YAChC,OAAO;QACX;QACA,OAAO;YACH,GAAG,WAAW;YACd,OAAO,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,YAAY,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI;QAC3F;IACJ;IACA,2BAA2B,IAAI,EAAE,OAAO,EAAE;QACtC,OAAO;YACH,GAAG,KAAK,CAAC,2BAA2B;gBAAE,GAAG,IAAI;gBAAE,YAAY,EAAE;YAAC,GAAG,QAAQ;YACzE,YAAY,IAAI,CAAC,CAAA,gCAAiC,CAAC,MAAM,SAAS;QACtE;IACJ;IACA,kBAAkB,IAAI,EAAE,OAAO,EAAE;QAC7B,OAAO;YACH,GAAG,KAAK,CAAC,kBAAkB;gBAAE,GAAG,IAAI;gBAAE,WAAW,EAAE;YAAC,GAAG,QAAQ;YAC/D,WAAW,IAAI,CAAC,CAAA,gCAAiC,CAAC,MAAM,SAAS;QACrE;IACJ;IACA,CAAA,gCAAiC,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO;QACpD,OAAO,oBAAoB,CAAC,KAAK,IAAI,CAAC,GAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC,iOAAA,CAAA,YAAS,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,GAC7D,IAAI,CAAC,aAAa,CAAC,KAAK,WACxB;gBACE,GAAG,GAAG;gBACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE;YAC1D,KACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,EAAE;IAChD;IACA,CAAA,mBAAoB,CAAC,IAAI;QACrB,OAAO,KAAK,IAAI,IAAI;IACxB;IACA,CAAA,mBAAoB,CAAC,IAAI;QACrB,MAAM,eAAe,IAAI;QACzB,IAAI,UAAU,QAAQ,KAAK,IAAI,IAAI,mPAAA,CAAA,0BAAuB,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG;YACtE,IAAI,CAAC,CAAA,kBAAmB,CAAC,KAAK,IAAI,EAAE;QACxC;QACA,IAAI,UAAU,QAAQ,KAAK,IAAI,EAAE;YAC7B,KAAK,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAE;gBAChC,IAAI,CAAC,CAAA,gCAAiC,CAAC,MAAM;YACjD;QACJ;QACA,IAAI,UAAU,QAAQ,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC,CAAA,gCAAiC,CAAC,KAAK,IAAI,EAAE;QACtD;QACA,IAAI,WAAW,QAAQ,KAAK,KAAK,EAAE;YAC/B,IAAI,CAAC,CAAA,gCAAiC,CAAC,KAAK,KAAK,EAAE;QACvD;QACA,IAAI,WAAW,QAAQ,KAAK,KAAK,EAAE;YAC/B,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;gBAC3B,IAAI,CAAC,CAAA,gCAAiC,CAAC,KAAK,KAAK,EAAE;YACvD;QACJ;QACA,IAAI,WAAW,QAAQ,KAAK,KAAK,EAAE;YAC/B,IAAI,CAAC,CAAA,gCAAiC,CAAC,KAAK,KAAK,EAAE;QACvD;QACA,OAAO;IACX;IACA,CAAA,WAAY,CAAC,IAAI;QACb,MAAM,OAAO,IAAI;QACjB,IAAI,UAAU,QAAQ,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC,CAAA,aAAc,CAAC,KAAK,IAAI,EAAE;QACnC;QACA,OAAO;IACX;IACA,CAAA,gCAAiC,CAAC,IAAI,EAAE,YAAY;QAChD,IAAI,iOAAA,CAAA,YAAS,CAAC,EAAE,CAAC,OAAO;YACpB,IAAI,CAAC,CAAA,kBAAmB,CAAC,KAAK,KAAK,EAAE;QACzC,OACK,IAAI,iOAAA,CAAA,YAAS,CAAC,EAAE,CAAC,SAAS,iOAAA,CAAA,YAAS,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG;YACpD,IAAI,CAAC,CAAA,kBAAmB,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;QAC9C,OACK,IAAI,gOAAA,CAAA,WAAQ,CAAC,EAAE,CAAC,OAAO;YACxB,KAAK,MAAM,SAAS,KAAK,KAAK,CAAE;gBAC5B,IAAI,CAAC,CAAA,gCAAiC,CAAC,OAAO;YAClD;QACJ;IACJ;IACA,CAAA,kBAAmB,CAAC,IAAI,EAAE,YAAY;QAClC,MAAM,KAAK,KAAK,UAAU,CAAC,IAAI;QAC/B,IAAI,CAAC,IAAI,CAAC,CAAA,YAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,GAAG,CAAC,KAAK;YACpD,aAAa,GAAG,CAAC;QACrB;IACJ;IACA,CAAA,aAAc,CAAC,IAAI,EAAE,IAAI;QACrB,KAAK,MAAM,QAAQ,KAAK,WAAW,CAAE;YACjC,MAAM,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI;YACnD,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,GAAG,CAAC,QAAQ;gBACxB,KAAK,GAAG,CAAC;YACb;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/plugin/with-schema/with-schema-plugin.js"], "sourcesContent": ["/// <reference types=\"./with-schema-plugin.d.ts\" />\nimport { WithSchemaTransformer } from './with-schema-transformer.js';\nexport class WithSchemaPlugin {\n    #transformer;\n    constructor(schema) {\n        this.#transformer = new WithSchemaTransformer(schema);\n    }\n    transformQuery(args) {\n        return this.#transformer.transformNode(args.node, args.queryId);\n    }\n    async transformResult(args) {\n        return args.result;\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;;AACO,MAAM;IACT,CAAA,WAAY,CAAC;IACb,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,WAAY,GAAG,IAAI,wPAAA,CAAA,wBAAqB,CAAC;IAClD;IACA,eAAe,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,KAAK,OAAO;IAClE;IACA,MAAM,gBAAgB,IAAI,EAAE;QACxB,OAAO,KAAK,MAAM;IACtB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/plugin/immediate-value/immediate-value-transformer.js"], "sourcesContent": ["/// <reference types=\"./immediate-value-transformer.d.ts\" />\nimport { OperationNodeTransformer } from '../../operation-node/operation-node-transformer.js';\nimport { ValueListNode } from '../../operation-node/value-list-node.js';\nimport { ValueNode } from '../../operation-node/value-node.js';\n/**\n * Transforms all ValueNodes to immediate.\n *\n * WARNING! This should never be part of the public API. Users should never use this.\n * This is an internal helper.\n *\n * @internal\n */\nexport class ImmediateValueTransformer extends OperationNodeTransformer {\n    transformPrimitiveValueList(node) {\n        return ValueListNode.create(node.values.map(ValueNode.createImmediate));\n    }\n    transformValue(node) {\n        return ValueNode.createImmediate(node.value);\n    }\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;AAC5D;AACA;AACA;;;;AASO,MAAM,kCAAkC,oPAAA,CAAA,2BAAwB;IACnE,4BAA4B,IAAI,EAAE;QAC9B,OAAO,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,iOAAA,CAAA,YAAS,CAAC,eAAe;IACzE;IACA,eAAe,IAAI,EAAE;QACjB,OAAO,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,KAAK,KAAK;IAC/C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/plugin/immediate-value/immediate-value-plugin.js"], "sourcesContent": ["/// <reference types=\"./immediate-value-plugin.d.ts\" />\nimport { ImmediateValueTransformer } from './immediate-value-transformer.js';\n/**\n * Transforms all ValueNodes to immediate.\n *\n * WARNING! This should never be part of the public API. Users should never use this.\n * This is an internal helper.\n *\n * @internal\n */\nexport class ImmediateValuePlugin {\n    #transformer = new ImmediateValueTransformer();\n    transformQuery(args) {\n        return this.#transformer.transformNode(args.node, args.queryId);\n    }\n    transformResult(args) {\n        return Promise.resolve(args.result);\n    }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;AACvD;;AASO,MAAM;IACT,CAAA,WAAY,GAAG,IAAI,gQAAA,CAAA,4BAAyB,GAAG;IAC/C,eAAe,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,KAAK,OAAO;IAClE;IACA,gBAAgB,IAAI,EAAE;QAClB,OAAO,QAAQ,OAAO,CAAC,KAAK,MAAM;IACtC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/plugin/noop-plugin.js"], "sourcesContent": ["/// <reference types=\"./noop-plugin.d.ts\" />\nexport class NoopPlugin {\n    transformQuery(args) {\n        return args.node;\n    }\n    async transformResult(args) {\n        return args.result;\n    }\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AACrC,MAAM;IACT,eAAe,IAAI,EAAE;QACjB,OAAO,KAAK,IAAI;IACpB;IACA,MAAM,gBAAgB,IAAI,EAAE;QACxB,OAAO,KAAK,MAAM;IACtB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-executor/query-executor-base.js"], "sourcesContent": ["/// <reference types=\"./query-executor-base.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { provideControlledConnection } from '../util/provide-controlled-connection.js';\nimport { logOnce } from '../util/log-once.js';\nconst NO_PLUGINS = freeze([]);\nexport class QueryExecutorBase {\n    #plugins;\n    constructor(plugins = NO_PLUGINS) {\n        this.#plugins = plugins;\n    }\n    get plugins() {\n        return this.#plugins;\n    }\n    transformQuery(node, queryId) {\n        for (const plugin of this.#plugins) {\n            const transformedNode = plugin.transformQuery({ node, queryId });\n            // We need to do a runtime check here. There is no good way\n            // to write types that enforce this constraint.\n            if (transformedNode.kind === node.kind) {\n                node = transformedNode;\n            }\n            else {\n                throw new Error([\n                    `KyselyPlugin.transformQuery must return a node`,\n                    `of the same kind that was given to it.`,\n                    `The plugin was given a ${node.kind}`,\n                    `but it returned a ${transformedNode.kind}`,\n                ].join(' '));\n            }\n        }\n        return node;\n    }\n    async executeQuery(compiledQuery, queryId) {\n        return await this.provideConnection(async (connection) => {\n            const result = await connection.executeQuery(compiledQuery);\n            if ('numUpdatedOrDeletedRows' in result) {\n                logOnce('kysely:warning: outdated driver/plugin detected! `QueryResult.numUpdatedOrDeletedRows` has been replaced with `QueryResult.numAffectedRows`.');\n            }\n            return await this.#transformResult(result, queryId);\n        });\n    }\n    async *stream(compiledQuery, chunkSize, queryId) {\n        const { connection, release } = await provideControlledConnection(this);\n        try {\n            for await (const result of connection.streamQuery(compiledQuery, chunkSize)) {\n                yield await this.#transformResult(result, queryId);\n            }\n        }\n        finally {\n            release();\n        }\n    }\n    async #transformResult(result, queryId) {\n        for (const plugin of this.#plugins) {\n            result = await plugin.transformResult({ result, queryId });\n        }\n        return result;\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;AACA;;;;AACA,MAAM,aAAa,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,EAAE;AACrB,MAAM;IACT,CAAA,OAAQ,CAAC;IACT,YAAY,UAAU,UAAU,CAAE;QAC9B,IAAI,CAAC,CAAA,OAAQ,GAAG;IACpB;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,OAAQ;IACxB;IACA,eAAe,IAAI,EAAE,OAAO,EAAE;QAC1B,KAAK,MAAM,UAAU,IAAI,CAAC,CAAA,OAAQ,CAAE;YAChC,MAAM,kBAAkB,OAAO,cAAc,CAAC;gBAAE;gBAAM;YAAQ;YAC9D,2DAA2D;YAC3D,+CAA+C;YAC/C,IAAI,gBAAgB,IAAI,KAAK,KAAK,IAAI,EAAE;gBACpC,OAAO;YACX,OACK;gBACD,MAAM,IAAI,MAAM;oBACZ,CAAC,8CAA8C,CAAC;oBAChD,CAAC,sCAAsC,CAAC;oBACxC,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;oBACrC,CAAC,kBAAkB,EAAE,gBAAgB,IAAI,EAAE;iBAC9C,CAAC,IAAI,CAAC;YACX;QACJ;QACA,OAAO;IACX;IACA,MAAM,aAAa,aAAa,EAAE,OAAO,EAAE;QACvC,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO;YACvC,MAAM,SAAS,MAAM,WAAW,YAAY,CAAC;YAC7C,IAAI,6BAA6B,QAAQ;gBACrC,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE;YACZ;YACA,OAAO,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,QAAQ;QAC/C;IACJ;IACA,OAAO,OAAO,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE;QAC7C,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,0OAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI;QACtE,IAAI;YACA,WAAW,MAAM,UAAU,WAAW,WAAW,CAAC,eAAe,WAAY;gBACzE,MAAM,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,QAAQ;YAC9C;QACJ,SACQ;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,eAAgB,CAAC,MAAM,EAAE,OAAO;QAClC,KAAK,MAAM,UAAU,IAAI,CAAC,CAAA,OAAQ,CAAE;YAChC,SAAS,MAAM,OAAO,eAAe,CAAC;gBAAE;gBAAQ;YAAQ;QAC5D;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-executor/noop-query-executor.js"], "sourcesContent": ["/// <reference types=\"./noop-query-executor.d.ts\" />\nimport { QueryExecutorBase } from './query-executor-base.js';\n/**\n * A {@link QueryExecutor} subclass that can be used when you don't\n * have a {@link QueryCompiler}, {@link ConnectionProvider} or any\n * other needed things to actually execute queries.\n */\nexport class NoopQueryExecutor extends QueryExecutorBase {\n    get adapter() {\n        throw new Error('this query cannot be compiled to SQL');\n    }\n    compileQuery() {\n        throw new Error('this query cannot be compiled to SQL');\n    }\n    provideConnection() {\n        throw new Error('this query cannot be executed');\n    }\n    withConnectionProvider() {\n        throw new Error('this query cannot have a connection provider');\n    }\n    withPlugin(plugin) {\n        return new NoopQueryExecutor([...this.plugins, plugin]);\n    }\n    withPlugins(plugins) {\n        return new NoopQueryExecutor([...this.plugins, ...plugins]);\n    }\n    withPluginAtFront(plugin) {\n        return new NoopQueryExecutor([plugin, ...this.plugins]);\n    }\n    withoutPlugins() {\n        return new NoopQueryExecutor([]);\n    }\n}\nexport const NOOP_QUERY_EXECUTOR = new NoopQueryExecutor();\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AACpD;;AAMO,MAAM,0BAA0B,6OAAA,CAAA,oBAAiB;IACpD,IAAI,UAAU;QACV,MAAM,IAAI,MAAM;IACpB;IACA,eAAe;QACX,MAAM,IAAI,MAAM;IACpB;IACA,oBAAoB;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,yBAAyB;QACrB,MAAM,IAAI,MAAM;IACpB;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,kBAAkB;eAAI,IAAI,CAAC,OAAO;YAAE;SAAO;IAC1D;IACA,YAAY,OAAO,EAAE;QACjB,OAAO,IAAI,kBAAkB;eAAI,IAAI,CAAC,OAAO;eAAK;SAAQ;IAC9D;IACA,kBAAkB,MAAM,EAAE;QACtB,OAAO,IAAI,kBAAkB;YAAC;eAAW,IAAI,CAAC,OAAO;SAAC;IAC1D;IACA,iBAAiB;QACb,OAAO,IAAI,kBAAkB,EAAE;IACnC;AACJ;AACO,MAAM,sBAAsB,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-executor/default-query-executor.js"], "sourcesContent": ["/// <reference types=\"./default-query-executor.d.ts\" />\nimport { QueryExecutorBase } from './query-executor-base.js';\nexport class DefaultQueryExecutor extends QueryExecutorBase {\n    #compiler;\n    #adapter;\n    #connectionProvider;\n    constructor(compiler, adapter, connectionProvider, plugins = []) {\n        super(plugins);\n        this.#compiler = compiler;\n        this.#adapter = adapter;\n        this.#connectionProvider = connectionProvider;\n    }\n    get adapter() {\n        return this.#adapter;\n    }\n    compileQuery(node, queryId) {\n        return this.#compiler.compileQuery(node, queryId);\n    }\n    provideConnection(consumer) {\n        return this.#connectionProvider.provideConnection(consumer);\n    }\n    withPlugins(plugins) {\n        return new DefaultQueryExecutor(this.#compiler, this.#adapter, this.#connectionProvider, [...this.plugins, ...plugins]);\n    }\n    withPlugin(plugin) {\n        return new DefaultQueryExecutor(this.#compiler, this.#adapter, this.#connectionProvider, [...this.plugins, plugin]);\n    }\n    withPluginAtFront(plugin) {\n        return new DefaultQueryExecutor(this.#compiler, this.#adapter, this.#connectionProvider, [plugin, ...this.plugins]);\n    }\n    withConnectionProvider(connectionProvider) {\n        return new DefaultQueryExecutor(this.#compiler, this.#adapter, connectionProvider, [...this.plugins]);\n    }\n    withoutPlugins() {\n        return new DefaultQueryExecutor(this.#compiler, this.#adapter, this.#connectionProvider, []);\n    }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;AACvD;;AACO,MAAM,6BAA6B,6OAAA,CAAA,oBAAiB;IACvD,CAAA,QAAS,CAAC;IACV,CAAA,OAAQ,CAAC;IACT,CAAA,kBAAmB,CAAC;IACpB,YAAY,QAAQ,EAAE,OAAO,EAAE,kBAAkB,EAAE,UAAU,EAAE,CAAE;QAC7D,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,QAAS,GAAG;QACjB,IAAI,CAAC,CAAA,OAAQ,GAAG;QAChB,IAAI,CAAC,CAAA,kBAAmB,GAAG;IAC/B;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,OAAQ;IACxB;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,YAAY,CAAC,MAAM;IAC7C;IACA,kBAAkB,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC,CAAA,kBAAmB,CAAC,iBAAiB,CAAC;IACtD;IACA,YAAY,OAAO,EAAE;QACjB,OAAO,IAAI,qBAAqB,IAAI,CAAC,CAAA,QAAS,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,IAAI,CAAC,CAAA,kBAAmB,EAAE;eAAI,IAAI,CAAC,OAAO;eAAK;SAAQ;IAC1H;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,qBAAqB,IAAI,CAAC,CAAA,QAAS,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,IAAI,CAAC,CAAA,kBAAmB,EAAE;eAAI,IAAI,CAAC,OAAO;YAAE;SAAO;IACtH;IACA,kBAAkB,MAAM,EAAE;QACtB,OAAO,IAAI,qBAAqB,IAAI,CAAC,CAAA,QAAS,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,IAAI,CAAC,CAAA,kBAAmB,EAAE;YAAC;eAAW,IAAI,CAAC,OAAO;SAAC;IACtH;IACA,uBAAuB,kBAAkB,EAAE;QACvC,OAAO,IAAI,qBAAqB,IAAI,CAAC,CAAA,QAAS,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,oBAAoB;eAAI,IAAI,CAAC,OAAO;SAAC;IACxG;IACA,iBAAiB;QACb,OAAO,IAAI,qBAAqB,IAAI,CAAC,CAAA,QAAS,EAAE,IAAI,CAAC,CAAA,OAAQ,EAAE,IAAI,CAAC,CAAA,kBAAmB,EAAE,EAAE;IAC/F;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-creator.js"], "sourcesContent": ["/// <reference types=\"./query-creator.d.ts\" />\nimport { createSelectQueryBuilder, } from './query-builder/select-query-builder.js';\nimport { InsertQueryBuilder } from './query-builder/insert-query-builder.js';\nimport { DeleteQueryBuilder } from './query-builder/delete-query-builder.js';\nimport { UpdateQueryBuilder } from './query-builder/update-query-builder.js';\nimport { DeleteQueryNode } from './operation-node/delete-query-node.js';\nimport { InsertQueryNode } from './operation-node/insert-query-node.js';\nimport { SelectQueryNode } from './operation-node/select-query-node.js';\nimport { UpdateQueryNode } from './operation-node/update-query-node.js';\nimport { parseTable, parseTableExpressionOrList, parseAliasedTable, } from './parser/table-parser.js';\nimport { parseCommonTableExpression, } from './parser/with-parser.js';\nimport { WithNode } from './operation-node/with-node.js';\nimport { createQueryId } from './util/query-id.js';\nimport { WithSchemaPlugin } from './plugin/with-schema/with-schema-plugin.js';\nimport { freeze } from './util/object-utils.js';\nimport { parseSelectArg, } from './parser/select-parser.js';\nimport { MergeQueryBuilder } from './query-builder/merge-query-builder.js';\nimport { MergeQueryNode } from './operation-node/merge-query-node.js';\nexport class QueryCreator {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Creates a `select` query builder for the given table or tables.\n     *\n     * The tables passed to this method are built as the query's `from` clause.\n     *\n     * ### Examples\n     *\n     * Create a select query for one table:\n     *\n     * ```ts\n     * db.selectFrom('person').selectAll()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select * from \"person\"\n     * ```\n     *\n     * Create a select query for one table with an alias:\n     *\n     * ```ts\n     * const persons = await db.selectFrom('person as p')\n     *   .select(['p.id', 'first_name'])\n     *   .execute()\n     *\n     * console.log(persons[0].id)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"p\".\"id\", \"first_name\" from \"person\" as \"p\"\n     * ```\n     *\n     * Create a select query from a subquery:\n     *\n     * ```ts\n     * const persons = await db.selectFrom(\n     *     (eb) => eb.selectFrom('person').select('person.id as identifier').as('p')\n     *   )\n     *   .select('p.identifier')\n     *   .execute()\n     *\n     * console.log(persons[0].identifier)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"p\".\"identifier\",\n     * from (\n     *   select \"person\".\"id\" as \"identifier\" from \"person\"\n     * ) as p\n     * ```\n     *\n     * Create a select query from raw sql:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * const items = await db\n     *   .selectFrom(sql<{ one: number }>`(select 1 as one)`.as('q'))\n     *   .select('q.one')\n     *   .execute()\n     *\n     * console.log(items[0].one)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"q\".\"one\",\n     * from (\n     *   select 1 as one\n     * ) as q\n     * ```\n     *\n     * When you use the `sql` tag you need to also provide the result type of the\n     * raw snippet / query so that Kysely can figure out what columns are\n     * available for the rest of the query.\n     *\n     * The `selectFrom` method also accepts an array for multiple tables. All\n     * the above examples can also be used in an array.\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * const items = await db.selectFrom([\n     *     'person as p',\n     *     db.selectFrom('pet').select('pet.species').as('a'),\n     *     sql<{ one: number }>`(select 1 as one)`.as('q')\n     *   ])\n     *   .select(['p.id', 'a.species', 'q.one'])\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"p\".id, \"a\".\"species\", \"q\".\"one\"\n     * from\n     *   \"person\" as \"p\",\n     *   (select \"pet\".\"species\" from \"pet\") as a,\n     *   (select 1 as one) as \"q\"\n     * ```\n     */\n    selectFrom(from) {\n        return createSelectQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: SelectQueryNode.createFrom(parseTableExpressionOrList(from), this.#props.withNode),\n        });\n    }\n    selectNoFrom(selection) {\n        return createSelectQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: SelectQueryNode.cloneWithSelections(SelectQueryNode.create(this.#props.withNode), parseSelectArg(selection)),\n        });\n    }\n    /**\n     * Creates an insert query.\n     *\n     * The return value of this query is an instance of {@link InsertResult}. {@link InsertResult}\n     * has the {@link InsertResult.insertId | insertId} field that holds the auto incremented id of\n     * the inserted row if the db returned one.\n     *\n     * See the {@link InsertQueryBuilder.values | values} method for more info and examples. Also see\n     * the {@link ReturningInterface.returning | returning} method for a way to return columns\n     * on supported databases like PostgreSQL.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db\n     *   .insertInto('person')\n     *   .values({\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston'\n     *   })\n     *   .executeTakeFirst()\n     *\n     * console.log(result.insertId)\n     * ```\n     *\n     * Some databases like PostgreSQL support the `returning` method:\n     *\n     * ```ts\n     * const { id } = await db\n     *   .insertInto('person')\n     *   .values({\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston'\n     *   })\n     *   .returning('id')\n     *   .executeTakeFirstOrThrow()\n     * ```\n     */\n    insertInto(table) {\n        return new InsertQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: InsertQueryNode.create(parseTable(table), this.#props.withNode),\n        });\n    }\n    /**\n     * Creates a \"replace into\" query.\n     *\n     * This is only supported by some dialects like MySQL or SQLite.\n     *\n     * Similar to MySQL's {@link InsertQueryBuilder.onDuplicateKeyUpdate} that deletes\n     * and inserts values on collision instead of updating existing rows.\n     *\n     * An alias of SQLite's {@link InsertQueryBuilder.orReplace}.\n     *\n     * The return value of this query is an instance of {@link InsertResult}. {@link InsertResult}\n     * has the {@link InsertResult.insertId | insertId} field that holds the auto incremented id of\n     * the inserted row if the db returned one.\n     *\n     * See the {@link InsertQueryBuilder.values | values} method for more info and examples.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db\n     *   .replaceInto('person')\n     *   .values({\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston'\n     *   })\n     *   .executeTakeFirstOrThrow()\n     *\n     * console.log(result.insertId)\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * replace into `person` (`first_name`, `last_name`) values (?, ?)\n     * ```\n     */\n    replaceInto(table) {\n        return new InsertQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: InsertQueryNode.create(parseTable(table), this.#props.withNode, true),\n        });\n    }\n    /**\n     * Creates a delete query.\n     *\n     * See the {@link DeleteQueryBuilder.where} method for examples on how to specify\n     * a where clause for the delete operation.\n     *\n     * The return value of the query is an instance of {@link DeleteResult}.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"delete\", \"Single row\", 10) -->\n     *\n     * Delete a single row:\n     *\n     * ```ts\n     * const result = await db\n     *   .deleteFrom('person')\n     *   .where('person.id', '=', 1)\n     *   .executeTakeFirst()\n     *\n     * console.log(result.numDeletedRows)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * delete from \"person\" where \"person\".\"id\" = $1\n     * ```\n     *\n     * Some databases such as MySQL support deleting from multiple tables:\n     *\n     * ```ts\n     * const result = await db\n     *   .deleteFrom(['person', 'pet'])\n     *   .using('person')\n     *   .innerJoin('pet', 'pet.owner_id', 'person.id')\n     *   .where('person.id', '=', 1)\n     *   .executeTakeFirst()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * delete from `person`, `pet`\n     * using `person`\n     * inner join `pet` on `pet`.`owner_id` = `person`.`id`\n     * where `person`.`id` = ?\n     * ```\n     */\n    deleteFrom(from) {\n        return new DeleteQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: DeleteQueryNode.create(parseTableExpressionOrList(from), this.#props.withNode),\n        });\n    }\n    /**\n     * Creates an update query.\n     *\n     * See the {@link UpdateQueryBuilder.where} method for examples on how to specify\n     * a where clause for the update operation.\n     *\n     * See the {@link UpdateQueryBuilder.set} method for examples on how to\n     * specify the updates.\n     *\n     * The return value of the query is an {@link UpdateResult}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db\n     *   .updateTable('person')\n     *   .set({ first_name: 'Jennifer' })\n     *   .where('person.id', '=', 1)\n     *   .executeTakeFirst()\n     *\n     * console.log(result.numUpdatedRows)\n     * ```\n     */\n    updateTable(tables) {\n        return new UpdateQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: UpdateQueryNode.create(parseTableExpressionOrList(tables), this.#props.withNode),\n        });\n    }\n    /**\n     * Creates a merge query.\n     *\n     * The return value of the query is a {@link MergeResult}.\n     *\n     * See the {@link MergeQueryBuilder.using} method for examples on how to specify\n     * the other table.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"merge\", \"Source row existence\", 10) -->\n     *\n     * Update a target column based on the existence of a source row:\n     *\n     * ```ts\n     * const result = await db\n     *   .mergeInto('person as target')\n     *   .using('pet as source', 'source.owner_id', 'target.id')\n     *   .whenMatchedAnd('target.has_pets', '!=', 'Y')\n     *   .thenUpdateSet({ has_pets: 'Y' })\n     *   .whenNotMatchedBySourceAnd('target.has_pets', '=', 'Y')\n     *   .thenUpdateSet({ has_pets: 'N' })\n     *   .executeTakeFirstOrThrow()\n     *\n     * console.log(result.numChangedRows)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\"\n     * on \"pet\".\"owner_id\" = \"person\".\"id\"\n     * when matched and \"has_pets\" != $1\n     * then update set \"has_pets\" = $2\n     * when not matched by source and \"has_pets\" = $3\n     * then update set \"has_pets\" = $4\n     * ```\n     *\n     * <!-- siteExample(\"merge\", \"Temporary changes table\", 20) -->\n     *\n     * Merge new entries from a temporary changes table:\n     *\n     * ```ts\n     * const result = await db\n     *   .mergeInto('wine as target')\n     *   .using(\n     *     'wine_stock_change as source',\n     *     'source.wine_name',\n     *     'target.name',\n     *   )\n     *   .whenNotMatchedAnd('source.stock_delta', '>', 0)\n     *   .thenInsertValues(({ ref }) => ({\n     *     name: ref('source.wine_name'),\n     *     stock: ref('source.stock_delta'),\n     *   }))\n     *   .whenMatchedAnd(\n     *     (eb) => eb('target.stock', '+', eb.ref('source.stock_delta')),\n     *     '>',\n     *     0,\n     *   )\n     *   .thenUpdateSet('stock', (eb) =>\n     *     eb('target.stock', '+', eb.ref('source.stock_delta')),\n     *   )\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"wine\" as \"target\"\n     * using \"wine_stock_change\" as \"source\"\n     * on \"source\".\"wine_name\" = \"target\".\"name\"\n     * when not matched and \"source\".\"stock_delta\" > $1\n     * then insert (\"name\", \"stock\") values (\"source\".\"wine_name\", \"source\".\"stock_delta\")\n     * when matched and \"target\".\"stock\" + \"source\".\"stock_delta\" > $2\n     * then update set \"stock\" = \"target\".\"stock\" + \"source\".\"stock_delta\"\n     * when matched\n     * then delete\n     * ```\n     */\n    mergeInto(targetTable) {\n        return new MergeQueryBuilder({\n            queryId: createQueryId(),\n            executor: this.#props.executor,\n            queryNode: MergeQueryNode.create(parseAliasedTable(targetTable), this.#props.withNode),\n        });\n    }\n    /**\n     * Creates a `with` query (Common Table Expression).\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"cte\", \"Simple selects\", 10) -->\n     *\n     * Common table expressions (CTE) are a great way to modularize complex queries.\n     * Essentially they allow you to run multiple separate queries within a\n     * single roundtrip to the DB.\n     *\n     * Since CTEs are a part of the main query, query optimizers inside DB\n     * engines are able to optimize the overall query. For example, postgres\n     * is able to inline the CTEs inside the using queries if it decides it's\n     * faster.\n     *\n     * ```ts\n     * const result = await db\n     *   // Create a CTE called `jennifers` that selects all\n     *   // persons named 'Jennifer'.\n     *   .with('jennifers', (db) => db\n     *     .selectFrom('person')\n     *     .where('first_name', '=', 'Jennifer')\n     *     .select(['id', 'age'])\n     *   )\n     *   // Select all rows from the `jennifers` CTE and\n     *   // further filter it.\n     *   .with('adult_jennifers', (db) => db\n     *     .selectFrom('jennifers')\n     *     .where('age', '>', 18)\n     *     .select(['id', 'age'])\n     *   )\n     *   // Finally select all adult jennifers that are\n     *   // also younger than 60.\n     *   .selectFrom('adult_jennifers')\n     *   .where('age', '<', 60)\n     *   .selectAll()\n     *   .execute()\n     * ```\n     *\n     * <!-- siteExample(\"cte\", \"Inserts, updates and deletions\", 20) -->\n     *\n     * Some databases like postgres also allow you to run other queries than selects\n     * in CTEs. On these databases CTEs are extremely powerful:\n     *\n     * ```ts\n     * const result = await db\n     *   .with('new_person', (db) => db\n     *     .insertInto('person')\n     *     .values({\n     *       first_name: 'Jennifer',\n     *       age: 35,\n     *     })\n     *     .returning('id')\n     *   )\n     *   .with('new_pet', (db) => db\n     *     .insertInto('pet')\n     *     .values({\n     *       name: 'Doggo',\n     *       species: 'dog',\n     *       is_favorite: true,\n     *       // Use the id of the person we just inserted.\n     *       owner_id: db\n     *         .selectFrom('new_person')\n     *         .select('id')\n     *     })\n     *     .returning('id')\n     *   )\n     *   .selectFrom(['new_person', 'new_pet'])\n     *   .select([\n     *     'new_person.id as person_id',\n     *     'new_pet.id as pet_id'\n     *   ])\n     *   .execute()\n     * ```\n     *\n     * The CTE name can optionally specify column names in addition to\n     * a name. In that case Kysely requires the expression to retun\n     * rows with the same columns.\n     *\n     * ```ts\n     * await db\n     *   .with('jennifers(id, age)', (db) => db\n     *     .selectFrom('person')\n     *     .where('first_name', '=', 'Jennifer')\n     *     // This is ok since we return columns with the same\n     *     // names as specified by `jennifers(id, age)`.\n     *     .select(['id', 'age'])\n     *   )\n     *   .selectFrom('jennifers')\n     *   .selectAll()\n     *   .execute()\n     * ```\n     *\n     * The first argument can also be a callback. The callback is passed\n     * a `CTEBuilder` instance that can be used to configure the CTE:\n     *\n     * ```ts\n     * await db\n     *   .with(\n     *     (cte) => cte('jennifers').materialized(),\n     *     (db) => db\n     *       .selectFrom('person')\n     *       .where('first_name', '=', 'Jennifer')\n     *       .select(['id', 'age'])\n     *   )\n     *   .selectFrom('jennifers')\n     *   .selectAll()\n     *   .execute()\n     * ```\n     */\n    with(nameOrBuilder, expression) {\n        const cte = parseCommonTableExpression(nameOrBuilder, expression);\n        return new QueryCreator({\n            ...this.#props,\n            withNode: this.#props.withNode\n                ? WithNode.cloneWithExpression(this.#props.withNode, cte)\n                : WithNode.create(cte),\n        });\n    }\n    /**\n     * Creates a recursive `with` query (Common Table Expression).\n     *\n     * Note that recursiveness is a property of the whole `with` statement.\n     * You cannot have recursive and non-recursive CTEs in a same `with` statement.\n     * Therefore the recursiveness is determined by the **first** `with` or\n     * `withRecusive` call you make.\n     *\n     * See the {@link with} method for examples and more documentation.\n     */\n    withRecursive(nameOrBuilder, expression) {\n        const cte = parseCommonTableExpression(nameOrBuilder, expression);\n        return new QueryCreator({\n            ...this.#props,\n            withNode: this.#props.withNode\n                ? WithNode.cloneWithExpression(this.#props.withNode, cte)\n                : WithNode.create(cte, { recursive: true }),\n        });\n    }\n    /**\n     * Returns a copy of this query creator instance with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new QueryCreator({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    /**\n     * Returns a copy of this query creator instance without any plugins.\n     */\n    withoutPlugins() {\n        return new QueryCreator({\n            ...this.#props,\n            executor: this.#props.executor.withoutPlugins(),\n        });\n    }\n    /**\n     * Sets the schema to be used for all table references that don't explicitly\n     * specify a schema.\n     *\n     * This only affects the query created through the builder returned from\n     * this method and doesn't modify the `db` instance.\n     *\n     * See [this recipe](https://github.com/kysely-org/kysely/blob/master/site/docs/recipes/0007-schemas.md)\n     * for a more detailed explanation.\n     *\n     * ### Examples\n     *\n     * ```\n     * await db\n     *   .withSchema('mammals')\n     *   .selectFrom('pet')\n     *   .selectAll()\n     *   .innerJoin('public.person', 'public.person.id', 'pet.owner_id')\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select * from \"mammals\".\"pet\"\n     * inner join \"public\".\"person\"\n     * on \"public\".\"person\".\"id\" = \"mammals\".\"pet\".\"owner_id\"\n     * ```\n     *\n     * `withSchema` is smart enough to not add schema for aliases,\n     * common table expressions or other places where the schema\n     * doesn't belong to:\n     *\n     * ```\n     * await db\n     *   .withSchema('mammals')\n     *   .selectFrom('pet as p')\n     *   .select('p.name')\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"p\".\"name\" from \"mammals\".\"pet\" as \"p\"\n     * ```\n     */\n    withSchema(schema) {\n        return new QueryCreator({\n            ...this.#props,\n            executor: this.#props.executor.withPluginAtFront(new WithSchemaPlugin(schema)),\n        });\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0GC,GACD,WAAW,IAAI,EAAE;QACb,OAAO,CAAA,GAAA,6OAAA,CAAA,2BAAwB,AAAD,EAAE;YAC5B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QAChG;IACJ;IACA,aAAa,SAAS,EAAE;QACpB,OAAO,CAAA,GAAA,6OAAA,CAAA,2BAAwB,AAAD,EAAE;YAC5B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAChH;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqCC,GACD,WAAW,KAAK,EAAE;QACd,OAAO,IAAI,6OAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QAC7E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCC,GACD,YAAY,KAAK,EAAE;QACf,OAAO,IAAI,6OAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE;QAC/E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgDC,GACD,WAAW,IAAI,EAAE;QACb,OAAO,IAAI,6OAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QAC5F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,YAAY,MAAM,EAAE;QAChB,OAAO,IAAI,6OAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QAC9F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkFC,GACD,UAAU,WAAW,EAAE;QACnB,OAAO,IAAI,4OAAA,CAAA,oBAAiB,CAAC;YACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;YAC9B,WAAW,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACzF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8GC,GACD,KAAK,aAAa,EAAE,UAAU,EAAE;QAC5B,MAAM,MAAM,CAAA,GAAA,uNAAA,CAAA,6BAA0B,AAAD,EAAE,eAAe;QACtD,OAAO,IAAI,aAAa;YACpB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,GACxB,gOAAA,CAAA,WAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,OACnD,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC;QAC1B;IACJ;IACA;;;;;;;;;KASC,GACD,cAAc,aAAa,EAAE,UAAU,EAAE;QACrC,MAAM,MAAM,CAAA,GAAA,uNAAA,CAAA,6BAA0B,AAAD,EAAE,eAAe;QACtD,OAAO,IAAI,aAAa;YACpB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,GACxB,gOAAA,CAAA,WAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,OACnD,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,KAAK;gBAAE,WAAW;YAAK;QACjD;IACJ;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,aAAa;YACpB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA;;KAEC,GACD,iBAAiB;QACb,OAAO,IAAI,aAAa;YACpB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc;QACjD;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8CC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,aAAa;YACpB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;QAC1E;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/driver/default-connection-provider.js"], "sourcesContent": ["/// <reference types=\"./default-connection-provider.d.ts\" />\nexport class DefaultConnectionProvider {\n    #driver;\n    constructor(driver) {\n        this.#driver = driver;\n    }\n    async provideConnection(consumer) {\n        const connection = await this.#driver.acquireConnection();\n        try {\n            return await consumer(connection);\n        }\n        finally {\n            await this.#driver.releaseConnection(connection);\n        }\n    }\n}\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;AACrD,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,MAAM,kBAAkB,QAAQ,EAAE;QAC9B,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,iBAAiB;QACvD,IAAI;YACA,OAAO,MAAM,SAAS;QAC1B,SACQ;YACJ,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,iBAAiB,CAAC;QACzC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/driver/runtime-driver.js"], "sourcesContent": ["/// <reference types=\"./runtime-driver.d.ts\" />\nimport { performanceNow } from '../util/performance-now.js';\n/**\n * A small wrapper around {@link Driver} that makes sure the driver is\n * initialized before it is used, only initialized and destroyed\n * once etc.\n */\nexport class RuntimeDriver {\n    #driver;\n    #log;\n    #initPromise;\n    #initDone;\n    #destroyPromise;\n    #connections = new WeakSet();\n    constructor(driver, log) {\n        this.#initDone = false;\n        this.#driver = driver;\n        this.#log = log;\n    }\n    async init() {\n        if (this.#destroyPromise) {\n            throw new Error('driver has already been destroyed');\n        }\n        if (!this.#initPromise) {\n            this.#initPromise = this.#driver\n                .init()\n                .then(() => {\n                this.#initDone = true;\n            })\n                .catch((err) => {\n                this.#initPromise = undefined;\n                return Promise.reject(err);\n            });\n        }\n        await this.#initPromise;\n    }\n    async acquireConnection() {\n        if (this.#destroyPromise) {\n            throw new Error('driver has already been destroyed');\n        }\n        if (!this.#initDone) {\n            await this.init();\n        }\n        const connection = await this.#driver.acquireConnection();\n        if (!this.#connections.has(connection)) {\n            if (this.#needsLogging()) {\n                this.#addLogging(connection);\n            }\n            this.#connections.add(connection);\n        }\n        return connection;\n    }\n    async releaseConnection(connection) {\n        await this.#driver.releaseConnection(connection);\n    }\n    beginTransaction(connection, settings) {\n        return this.#driver.beginTransaction(connection, settings);\n    }\n    commitTransaction(connection) {\n        return this.#driver.commitTransaction(connection);\n    }\n    rollbackTransaction(connection) {\n        return this.#driver.rollbackTransaction(connection);\n    }\n    savepoint(connection, savepointName, compileQuery) {\n        if (this.#driver.savepoint) {\n            return this.#driver.savepoint(connection, savepointName, compileQuery);\n        }\n        throw new Error('The `savepoint` method is not supported by this driver');\n    }\n    rollbackToSavepoint(connection, savepointName, compileQuery) {\n        if (this.#driver.rollbackToSavepoint) {\n            return this.#driver.rollbackToSavepoint(connection, savepointName, compileQuery);\n        }\n        throw new Error('The `rollbackToSavepoint` method is not supported by this driver');\n    }\n    releaseSavepoint(connection, savepointName, compileQuery) {\n        if (this.#driver.releaseSavepoint) {\n            return this.#driver.releaseSavepoint(connection, savepointName, compileQuery);\n        }\n        throw new Error('The `releaseSavepoint` method is not supported by this driver');\n    }\n    async destroy() {\n        if (!this.#initPromise) {\n            return;\n        }\n        await this.#initPromise;\n        if (!this.#destroyPromise) {\n            this.#destroyPromise = this.#driver.destroy().catch((err) => {\n                this.#destroyPromise = undefined;\n                return Promise.reject(err);\n            });\n        }\n        await this.#destroyPromise;\n    }\n    #needsLogging() {\n        return (this.#log.isLevelEnabled('query') || this.#log.isLevelEnabled('error'));\n    }\n    // This method monkey patches the database connection's executeQuery method\n    // by adding logging code around it. Monkey patching is not pretty, but it's\n    // the best option in this case.\n    #addLogging(connection) {\n        const executeQuery = connection.executeQuery;\n        const streamQuery = connection.streamQuery;\n        const dis = this;\n        connection.executeQuery = async (compiledQuery) => {\n            let caughtError;\n            const startTime = performanceNow();\n            try {\n                return await executeQuery.call(connection, compiledQuery);\n            }\n            catch (error) {\n                caughtError = error;\n                await dis.#logError(error, compiledQuery, startTime);\n                throw error;\n            }\n            finally {\n                if (!caughtError) {\n                    await dis.#logQuery(compiledQuery, startTime);\n                }\n            }\n        };\n        connection.streamQuery = async function* (compiledQuery, chunkSize) {\n            let caughtError;\n            const startTime = performanceNow();\n            try {\n                for await (const result of streamQuery.call(connection, compiledQuery, chunkSize)) {\n                    yield result;\n                }\n            }\n            catch (error) {\n                caughtError = error;\n                await dis.#logError(error, compiledQuery, startTime);\n                throw error;\n            }\n            finally {\n                if (!caughtError) {\n                    await dis.#logQuery(compiledQuery, startTime, true);\n                }\n            }\n        };\n    }\n    async #logError(error, compiledQuery, startTime) {\n        await this.#log.error(() => ({\n            level: 'error',\n            error,\n            query: compiledQuery,\n            queryDurationMillis: this.#calculateDurationMillis(startTime),\n        }));\n    }\n    async #logQuery(compiledQuery, startTime, isStream = false) {\n        await this.#log.query(() => ({\n            level: 'query',\n            isStream,\n            query: compiledQuery,\n            queryDurationMillis: this.#calculateDurationMillis(startTime),\n        }));\n    }\n    #calculateDurationMillis(startTime) {\n        return performanceNow() - startTime;\n    }\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;AAC/C;;AAMO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,GAAI,CAAC;IACL,CAAA,WAAY,CAAC;IACb,CAAA,QAAS,CAAC;IACV,CAAA,cAAe,CAAC;IAChB,CAAA,WAAY,GAAG,IAAI,UAAU;IAC7B,YAAY,MAAM,EAAE,GAAG,CAAE;QACrB,IAAI,CAAC,CAAA,QAAS,GAAG;QACjB,IAAI,CAAC,CAAA,MAAO,GAAG;QACf,IAAI,CAAC,CAAA,GAAI,GAAG;IAChB;IACA,MAAM,OAAO;QACT,IAAI,IAAI,CAAC,CAAA,cAAe,EAAE;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,IAAI,CAAC,CAAA,WAAY,EAAE;YACpB,IAAI,CAAC,CAAA,WAAY,GAAG,IAAI,CAAC,CAAA,MAAO,CAC3B,IAAI,GACJ,IAAI,CAAC;gBACN,IAAI,CAAC,CAAA,QAAS,GAAG;YACrB,GACK,KAAK,CAAC,CAAC;gBACR,IAAI,CAAC,CAAA,WAAY,GAAG;gBACpB,OAAO,QAAQ,MAAM,CAAC;YAC1B;QACJ;QACA,MAAM,IAAI,CAAC,CAAA,WAAY;IAC3B;IACA,MAAM,oBAAoB;QACtB,IAAI,IAAI,CAAC,CAAA,cAAe,EAAE;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,IAAI,CAAC,CAAA,QAAS,EAAE;YACjB,MAAM,IAAI,CAAC,IAAI;QACnB;QACA,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,iBAAiB;QACvD,IAAI,CAAC,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC,aAAa;YACpC,IAAI,IAAI,CAAC,CAAA,YAAa,IAAI;gBACtB,IAAI,CAAC,CAAA,UAAW,CAAC;YACrB;YACA,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO;IACX;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,iBAAiB,CAAC;IACzC;IACA,iBAAiB,UAAU,EAAE,QAAQ,EAAE;QACnC,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,gBAAgB,CAAC,YAAY;IACrD;IACA,kBAAkB,UAAU,EAAE;QAC1B,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,iBAAiB,CAAC;IAC1C;IACA,oBAAoB,UAAU,EAAE;QAC5B,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,CAAC;IAC5C;IACA,UAAU,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC/C,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,SAAS,EAAE;YACxB,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,SAAS,CAAC,YAAY,eAAe;QAC7D;QACA,MAAM,IAAI,MAAM;IACpB;IACA,oBAAoB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QACzD,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,EAAE;YAClC,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,CAAC,YAAY,eAAe;QACvE;QACA,MAAM,IAAI,MAAM;IACpB;IACA,iBAAiB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QACtD,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,gBAAgB,EAAE;YAC/B,OAAO,IAAI,CAAC,CAAA,MAAO,CAAC,gBAAgB,CAAC,YAAY,eAAe;QACpE;QACA,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,CAAA,WAAY,EAAE;YACpB;QACJ;QACA,MAAM,IAAI,CAAC,CAAA,WAAY;QACvB,IAAI,CAAC,IAAI,CAAC,CAAA,cAAe,EAAE;YACvB,IAAI,CAAC,CAAA,cAAe,GAAG,IAAI,CAAC,CAAA,MAAO,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,CAAA,cAAe,GAAG;gBACvB,OAAO,QAAQ,MAAM,CAAC;YAC1B;QACJ;QACA,MAAM,IAAI,CAAC,CAAA,cAAe;IAC9B;IACA,CAAA,YAAa;QACT,OAAQ,IAAI,CAAC,CAAA,GAAI,CAAC,cAAc,CAAC,YAAY,IAAI,CAAC,CAAA,GAAI,CAAC,cAAc,CAAC;IAC1E;IACA,2EAA2E;IAC3E,4EAA4E;IAC5E,gCAAgC;IAChC,CAAA,UAAW,CAAC,UAAU;QAClB,MAAM,eAAe,WAAW,YAAY;QAC5C,MAAM,cAAc,WAAW,WAAW;QAC1C,MAAM,MAAM,IAAI;QAChB,WAAW,YAAY,GAAG,OAAO;YAC7B,IAAI;YACJ,MAAM,YAAY,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD;YAC/B,IAAI;gBACA,OAAO,MAAM,aAAa,IAAI,CAAC,YAAY;YAC/C,EACA,OAAO,OAAO;gBACV,cAAc;gBACd,MAAM,IAAI,CAAA,QAAS,CAAC,OAAO,eAAe;gBAC1C,MAAM;YACV,SACQ;gBACJ,IAAI,CAAC,aAAa;oBACd,MAAM,IAAI,CAAA,QAAS,CAAC,eAAe;gBACvC;YACJ;QACJ;QACA,WAAW,WAAW,GAAG,gBAAiB,aAAa,EAAE,SAAS;YAC9D,IAAI;YACJ,MAAM,YAAY,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD;YAC/B,IAAI;gBACA,WAAW,MAAM,UAAU,YAAY,IAAI,CAAC,YAAY,eAAe,WAAY;oBAC/E,MAAM;gBACV;YACJ,EACA,OAAO,OAAO;gBACV,cAAc;gBACd,MAAM,IAAI,CAAA,QAAS,CAAC,OAAO,eAAe;gBAC1C,MAAM;YACV,SACQ;gBACJ,IAAI,CAAC,aAAa;oBACd,MAAM,IAAI,CAAA,QAAS,CAAC,eAAe,WAAW;gBAClD;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,QAAS,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS;QAC3C,MAAM,IAAI,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,IAAM,CAAC;gBACzB,OAAO;gBACP;gBACA,OAAO;gBACP,qBAAqB,IAAI,CAAC,CAAA,uBAAwB,CAAC;YACvD,CAAC;IACL;IACA,MAAM,CAAA,QAAS,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,KAAK;QACtD,MAAM,IAAI,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,IAAM,CAAC;gBACzB,OAAO;gBACP;gBACA,OAAO;gBACP,qBAAqB,IAAI,CAAC,CAAA,uBAAwB,CAAC;YACvD,CAAC;IACL;IACA,CAAA,uBAAwB,CAAC,SAAS;QAC9B,OAAO,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,MAAM;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2355, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/driver/single-connection-provider.js"], "sourcesContent": ["/// <reference types=\"./single-connection-provider.d.ts\" />\nconst ignoreError = () => { };\nexport class SingleConnectionProvider {\n    #connection;\n    #runningPromise;\n    constructor(connection) {\n        this.#connection = connection;\n    }\n    async provideConnection(consumer) {\n        while (this.#runningPromise) {\n            await this.#runningPromise.catch(ignoreError);\n        }\n        // `#runningPromise` must be set to undefined before it's\n        // resolved or rejected. Otherwise the while loop above\n        // will misbehave.\n        this.#runningPromise = this.#run(consumer).finally(() => {\n            this.#runningPromise = undefined;\n        });\n        return this.#runningPromise;\n    }\n    // Run the runner in an async function to make sure it doesn't\n    // throw synchronous errors.\n    async #run(runner) {\n        return await runner(this.#connection);\n    }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;AAC3D,MAAM,cAAc,KAAQ;AACrB,MAAM;IACT,CAAA,UAAW,CAAC;IACZ,CAAA,cAAe,CAAC;IAChB,YAAY,UAAU,CAAE;QACpB,IAAI,CAAC,CAAA,UAAW,GAAG;IACvB;IACA,MAAM,kBAAkB,QAAQ,EAAE;QAC9B,MAAO,IAAI,CAAC,CAAA,cAAe,CAAE;YACzB,MAAM,IAAI,CAAC,CAAA,cAAe,CAAC,KAAK,CAAC;QACrC;QACA,yDAAyD;QACzD,uDAAuD;QACvD,kBAAkB;QAClB,IAAI,CAAC,CAAA,cAAe,GAAG,IAAI,CAAC,CAAA,GAAI,CAAC,UAAU,OAAO,CAAC;YAC/C,IAAI,CAAC,CAAA,cAAe,GAAG;QAC3B;QACA,OAAO,IAAI,CAAC,CAAA,cAAe;IAC/B;IACA,8DAA8D;IAC9D,4BAA4B;IAC5B,MAAM,CAAA,GAAI,CAAC,MAAM;QACb,OAAO,MAAM,OAAO,IAAI,CAAC,CAAA,UAAW;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/driver/driver.js"], "sourcesContent": ["/// <reference types=\"./driver.d.ts\" />\nexport const TRANSACTION_ACCESS_MODES = ['read only', 'read write'];\nexport const TRANSACTION_ISOLATION_LEVELS = [\n    'read uncommitted',\n    'read committed',\n    'repeatable read',\n    'serializable',\n    'snapshot',\n];\nexport function validateTransactionSettings(settings) {\n    if (settings.accessMode &&\n        !TRANSACTION_ACCESS_MODES.includes(settings.accessMode)) {\n        throw new Error(`invalid transaction access mode ${settings.accessMode}`);\n    }\n    if (settings.isolationLevel &&\n        !TRANSACTION_ISOLATION_LEVELS.includes(settings.isolationLevel)) {\n        throw new Error(`invalid transaction isolation level ${settings.isolationLevel}`);\n    }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;AAChC,MAAM,2BAA2B;IAAC;IAAa;CAAa;AAC5D,MAAM,+BAA+B;IACxC;IACA;IACA;IACA;IACA;CACH;AACM,SAAS,4BAA4B,QAAQ;IAChD,IAAI,SAAS,UAAU,IACnB,CAAC,yBAAyB,QAAQ,CAAC,SAAS,UAAU,GAAG;QACzD,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,UAAU,EAAE;IAC5E;IACA,IAAI,SAAS,cAAc,IACvB,CAAC,6BAA6B,QAAQ,CAAC,SAAS,cAAc,GAAG;QACjE,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,SAAS,cAAc,EAAE;IACpF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/kysely.js"], "sourcesContent": ["/// <reference types=\"./kysely.d.ts\" />\nimport { SchemaModule } from './schema/schema.js';\nimport { DynamicModule } from './dynamic/dynamic.js';\nimport { DefaultConnectionProvider } from './driver/default-connection-provider.js';\nimport { QueryCreator } from './query-creator.js';\nimport { DefaultQueryExecutor } from './query-executor/default-query-executor.js';\nimport { freeze, isObject, isUndefined } from './util/object-utils.js';\nimport { RuntimeDriver } from './driver/runtime-driver.js';\nimport { SingleConnectionProvider } from './driver/single-connection-provider.js';\nimport { validateTransactionSettings, } from './driver/driver.js';\nimport { createFunctionModule, } from './query-builder/function-module.js';\nimport { Log } from './util/log.js';\nimport { createQueryId } from './util/query-id.js';\nimport { isCompilable } from './util/compilable.js';\nimport { CaseBuilder } from './query-builder/case-builder.js';\nimport { CaseNode } from './operation-node/case-node.js';\nimport { parseExpression } from './parser/expression-parser.js';\nimport { WithSchemaPlugin } from './plugin/with-schema/with-schema-plugin.js';\nimport { provideControlledConnection, } from './util/provide-controlled-connection.js';\n// @ts-ignore\nSymbol.asyncDispose ??= Symbol('Symbol.asyncDispose');\n/**\n * The main Kysely class.\n *\n * You should create one instance of `Kysely` per database using the {@link Kysely}\n * constructor. Each `Kysely` instance maintains its own connection pool.\n *\n * ### Examples\n *\n * This example assumes your database has a \"person\" table:\n *\n * ```ts\n * import * as Sqlite from 'better-sqlite3'\n * import { type Generated, Kysely, SqliteDialect } from 'kysely'\n *\n * interface Database {\n *   person: {\n *     id: Generated<number>\n *     first_name: string\n *     last_name: string | null\n *   }\n * }\n *\n * const db = new Kysely<Database>({\n *   dialect: new SqliteDialect({\n *     database: new Sqlite(':memory:'),\n *   })\n * })\n * ```\n *\n * @typeParam DB - The database interface type. Keys of this type must be table names\n *    in the database and values must be interfaces that describe the rows in those\n *    tables. See the examples above.\n */\nexport class Kysely extends QueryCreator {\n    #props;\n    constructor(args) {\n        let superProps;\n        let props;\n        if (isKyselyProps(args)) {\n            superProps = { executor: args.executor };\n            props = { ...args };\n        }\n        else {\n            const dialect = args.dialect;\n            const driver = dialect.createDriver();\n            const compiler = dialect.createQueryCompiler();\n            const adapter = dialect.createAdapter();\n            const log = new Log(args.log ?? []);\n            const runtimeDriver = new RuntimeDriver(driver, log);\n            const connectionProvider = new DefaultConnectionProvider(runtimeDriver);\n            const executor = new DefaultQueryExecutor(compiler, adapter, connectionProvider, args.plugins ?? []);\n            superProps = { executor };\n            props = {\n                config: args,\n                executor,\n                dialect,\n                driver: runtimeDriver,\n            };\n        }\n        super(superProps);\n        this.#props = freeze(props);\n    }\n    /**\n     * Returns the {@link SchemaModule} module for building database schema.\n     */\n    get schema() {\n        return new SchemaModule(this.#props.executor);\n    }\n    /**\n     * Returns a the {@link DynamicModule} module.\n     *\n     * The {@link DynamicModule} module can be used to bypass strict typing and\n     * passing in dynamic values for the queries.\n     */\n    get dynamic() {\n        return new DynamicModule();\n    }\n    /**\n     * Returns a {@link DatabaseIntrospector | database introspector}.\n     */\n    get introspection() {\n        return this.#props.dialect.createIntrospector(this.withoutPlugins());\n    }\n    case(value) {\n        return new CaseBuilder({\n            node: CaseNode.create(isUndefined(value) ? undefined : parseExpression(value)),\n        });\n    }\n    /**\n     * Returns a {@link FunctionModule} that can be used to write somewhat type-safe function\n     * calls.\n     *\n     * ```ts\n     * const { count } = db.fn\n     *\n     * await db.selectFrom('person')\n     *   .innerJoin('pet', 'pet.owner_id', 'person.id')\n     *   .select([\n     *     'id',\n     *     count('pet.id').as('person_count'),\n     *   ])\n     *   .groupBy('person.id')\n     *   .having(count('pet.id'), '>', 10)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"person\".\"id\", count(\"pet\".\"id\") as \"person_count\"\n     * from \"person\"\n     * inner join \"pet\" on \"pet\".\"owner_id\" = \"person\".\"id\"\n     * group by \"person\".\"id\"\n     * having count(\"pet\".\"id\") > $1\n     * ```\n     *\n     * Why \"somewhat\" type-safe? Because the function calls are not bound to the\n     * current query context. They allow you to reference columns and tables that\n     * are not in the current query. E.g. remove the `innerJoin` from the previous\n     * query and TypeScript won't even complain.\n     *\n     * If you want to make the function calls fully type-safe, you can use the\n     * {@link ExpressionBuilder.fn} getter for a query context-aware, stricter {@link FunctionModule}.\n     *\n     * ```ts\n     * await db.selectFrom('person')\n     *   .innerJoin('pet', 'pet.owner_id', 'person.id')\n     *   .select((eb) => [\n     *     'person.id',\n     *     eb.fn.count('pet.id').as('pet_count')\n     *   ])\n     *   .groupBy('person.id')\n     *   .having((eb) => eb.fn.count('pet.id'), '>', 10)\n     *   .execute()\n     * ```\n     */\n    get fn() {\n        return createFunctionModule();\n    }\n    /**\n     * Creates a {@link TransactionBuilder} that can be used to run queries inside a transaction.\n     *\n     * The returned {@link TransactionBuilder} can be used to configure the transaction. The\n     * {@link TransactionBuilder.execute} method can then be called to run the transaction.\n     * {@link TransactionBuilder.execute} takes a function that is run inside the\n     * transaction. If the function throws an exception,\n     * 1. the exception is caught,\n     * 2. the transaction is rolled back, and\n     * 3. the exception is thrown again.\n     * Otherwise the transaction is committed.\n     *\n     * The callback function passed to the {@link TransactionBuilder.execute | execute}\n     * method gets the transaction object as its only argument. The transaction is\n     * of type {@link Transaction} which inherits {@link Kysely}. Any query\n     * started through the transaction object is executed inside the transaction.\n     *\n     * To run a controlled transaction, allowing you to commit and rollback manually,\n     * use {@link startTransaction} instead.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"transactions\", \"Simple transaction\", 10) -->\n     *\n     * This example inserts two rows in a transaction. If an exception is thrown inside\n     * the callback passed to the `execute` method,\n     * 1. the exception is caught,\n     * 2. the transaction is rolled back, and\n     * 3. the exception is thrown again.\n     * Otherwise the transaction is committed.\n     *\n     * ```ts\n     * const catto = await db.transaction().execute(async (trx) => {\n     *   const jennifer = await trx.insertInto('person')\n     *     .values({\n     *       first_name: 'Jennifer',\n     *       last_name: 'Aniston',\n     *       age: 40,\n     *     })\n     *     .returning('id')\n     *     .executeTakeFirstOrThrow()\n     *\n     *   return await trx.insertInto('pet')\n     *     .values({\n     *       owner_id: jennifer.id,\n     *       name: 'Catto',\n     *       species: 'cat',\n     *       is_favorite: false,\n     *     })\n     *     .returningAll()\n     *     .executeTakeFirst()\n     * })\n     * ```\n     *\n     * Setting the isolation level:\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     *\n     * await db\n     *   .transaction()\n     *   .setIsolationLevel('serializable')\n     *   .execute(async (trx) => {\n     *     await doStuff(trx)\n     *   })\n     *\n     * async function doStuff(kysely: typeof db) {\n     *   // ...\n     * }\n     * ```\n     */\n    transaction() {\n        return new TransactionBuilder({ ...this.#props });\n    }\n    /**\n     * Creates a {@link ControlledTransactionBuilder} that can be used to run queries inside a controlled transaction.\n     *\n     * The returned {@link ControlledTransactionBuilder} can be used to configure the transaction.\n     * The {@link ControlledTransactionBuilder.execute} method can then be called\n     * to start the transaction and return a {@link ControlledTransaction}.\n     *\n     * A {@link ControlledTransaction} allows you to commit and rollback manually,\n     * execute savepoint commands. It extends {@link Transaction} which extends {@link Kysely},\n     * so you can run queries inside the transaction. Once the transaction is committed,\n     * or rolled back, it can't be used anymore - all queries will throw an error.\n     * This is to prevent accidentally running queries outside the transaction - where\n     * atomicity is not guaranteed anymore.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"transactions\", \"Controlled transaction\", 11) -->\n     *\n     * A controlled transaction allows you to commit and rollback manually, execute\n     * savepoint commands, and queries in general.\n     *\n     * In this example we start a transaction, use it to insert two rows and then commit\n     * the transaction. If an error is thrown, we catch it and rollback the transaction.\n     *\n     * ```ts\n     * const trx = await db.startTransaction().execute()\n     *\n     * try {\n     *   const jennifer = await trx.insertInto('person')\n     *     .values({\n     *       first_name: 'Jennifer',\n     *       last_name: 'Aniston',\n     *       age: 40,\n     *     })\n     *     .returning('id')\n     *     .executeTakeFirstOrThrow()\n     *\n     *   const catto = await trx.insertInto('pet')\n     *     .values({\n     *       owner_id: jennifer.id,\n     *       name: 'Catto',\n     *       species: 'cat',\n     *       is_favorite: false,\n     *     })\n     *     .returningAll()\n     *     .executeTakeFirstOrThrow()\n     *\n     *   await trx.commit().execute()\n     *\n     *   // ...\n     * } catch (error) {\n     *   await trx.rollback().execute()\n     * }\n     * ```\n     *\n     * <!-- siteExample(\"transactions\", \"Controlled transaction /w savepoints\", 12) -->\n     *\n     * A controlled transaction allows you to commit and rollback manually, execute\n     * savepoint commands, and queries in general.\n     *\n     * In this example we start a transaction, insert a person, create a savepoint,\n     * try inserting a toy and a pet, and if an error is thrown, we rollback to the\n     * savepoint. Eventually we release the savepoint, insert an audit record and\n     * commit the transaction. If an error is thrown, we catch it and rollback the\n     * transaction.\n     *\n     * ```ts\n     * const trx = await db.startTransaction().execute()\n     *\n     * try {\n     *   const jennifer = await trx\n     *     .insertInto('person')\n     *     .values({\n     *       first_name: 'Jennifer',\n     *       last_name: 'Aniston',\n     *       age: 40,\n     *     })\n     *     .returning('id')\n     *     .executeTakeFirstOrThrow()\n     *\n     *   const trxAfterJennifer = await trx.savepoint('after_jennifer').execute()\n     *\n     *   try {\n     *     const catto = await trxAfterJennifer\n     *       .insertInto('pet')\n     *       .values({\n     *         owner_id: jennifer.id,\n     *         name: 'Catto',\n     *         species: 'cat',\n     *       })\n     *       .returning('id')\n     *       .executeTakeFirstOrThrow()\n     *\n     *     await trxAfterJennifer\n     *       .insertInto('toy')\n     *       .values({ name: 'Bone', price: 1.99, pet_id: catto.id })\n     *       .execute()\n     *   } catch (error) {\n     *     await trxAfterJennifer.rollbackToSavepoint('after_jennifer').execute()\n     *   }\n     *\n     *   await trxAfterJennifer.releaseSavepoint('after_jennifer').execute()\n     *\n     *   await trx.insertInto('audit').values({ action: 'added Jennifer' }).execute()\n     *\n     *   await trx.commit().execute()\n     * } catch (error) {\n     *   await trx.rollback().execute()\n     * }\n     * ```\n     */\n    startTransaction() {\n        return new ControlledTransactionBuilder({ ...this.#props });\n    }\n    /**\n     * Provides a kysely instance bound to a single database connection.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db\n     *   .connection()\n     *   .execute(async (db) => {\n     *     // `db` is an instance of `Kysely` that's bound to a single\n     *     // database connection. All queries executed through `db` use\n     *     // the same connection.\n     *     await doStuff(db)\n     *   })\n     *\n     * async function doStuff(kysely: typeof db) {\n     *   // ...\n     * }\n     * ```\n     */\n    connection() {\n        return new ConnectionBuilder({ ...this.#props });\n    }\n    /**\n     * Returns a copy of this Kysely instance with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new Kysely({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    /**\n     * Returns a copy of this Kysely instance without any plugins.\n     */\n    withoutPlugins() {\n        return new Kysely({\n            ...this.#props,\n            executor: this.#props.executor.withoutPlugins(),\n        });\n    }\n    /**\n     * @override\n     */\n    withSchema(schema) {\n        return new Kysely({\n            ...this.#props,\n            executor: this.#props.executor.withPluginAtFront(new WithSchemaPlugin(schema)),\n        });\n    }\n    /**\n     * Returns a copy of this Kysely instance with tables added to its\n     * database type.\n     *\n     * This method only modifies the types and doesn't affect any of the\n     * executed queries in any way.\n     *\n     * ### Examples\n     *\n     * The following example adds and uses a temporary table:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('temp_table')\n     *   .temporary()\n     *   .addColumn('some_column', 'integer')\n     *   .execute()\n     *\n     * const tempDb = db.withTables<{\n     *   temp_table: {\n     *     some_column: number\n     *   }\n     * }>()\n     *\n     * await tempDb\n     *   .insertInto('temp_table')\n     *   .values({ some_column: 100 })\n     *   .execute()\n     * ```\n     */\n    withTables() {\n        return new Kysely({ ...this.#props });\n    }\n    /**\n     * Releases all resources and disconnects from the database.\n     *\n     * You need to call this when you are done using the `Kysely` instance.\n     */\n    async destroy() {\n        await this.#props.driver.destroy();\n    }\n    /**\n     * Returns true if this `Kysely` instance is a transaction.\n     *\n     * You can also use `db instanceof Transaction`.\n     */\n    get isTransaction() {\n        return false;\n    }\n    /**\n     * @internal\n     * @private\n     */\n    getExecutor() {\n        return this.#props.executor;\n    }\n    /**\n     * Executes a given compiled query or query builder.\n     *\n     * See {@link https://github.com/kysely-org/kysely/blob/master/site/docs/recipes/0004-splitting-query-building-and-execution.md#execute-compiled-queries splitting build, compile and execute code recipe} for more information.\n     */\n    executeQuery(query, queryId = createQueryId()) {\n        const compiledQuery = isCompilable(query) ? query.compile() : query;\n        return this.getExecutor().executeQuery(compiledQuery, queryId);\n    }\n    async [Symbol.asyncDispose]() {\n        await this.destroy();\n    }\n}\nexport class Transaction extends Kysely {\n    #props;\n    constructor(props) {\n        super(props);\n        this.#props = props;\n    }\n    // The return type is `true` instead of `boolean` to make Kysely<DB>\n    // unassignable to Transaction<DB> while allowing assignment the\n    // other way around.\n    get isTransaction() {\n        return true;\n    }\n    transaction() {\n        throw new Error('calling the transaction method for a Transaction is not supported');\n    }\n    connection() {\n        throw new Error('calling the connection method for a Transaction is not supported');\n    }\n    async destroy() {\n        throw new Error('calling the destroy method for a Transaction is not supported');\n    }\n    withPlugin(plugin) {\n        return new Transaction({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    withoutPlugins() {\n        return new Transaction({\n            ...this.#props,\n            executor: this.#props.executor.withoutPlugins(),\n        });\n    }\n    withSchema(schema) {\n        return new Transaction({\n            ...this.#props,\n            executor: this.#props.executor.withPluginAtFront(new WithSchemaPlugin(schema)),\n        });\n    }\n    withTables() {\n        return new Transaction({ ...this.#props });\n    }\n}\nexport function isKyselyProps(obj) {\n    return (isObject(obj) &&\n        isObject(obj.config) &&\n        isObject(obj.driver) &&\n        isObject(obj.executor) &&\n        isObject(obj.dialect));\n}\nexport class ConnectionBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    async execute(callback) {\n        return this.#props.executor.provideConnection(async (connection) => {\n            const executor = this.#props.executor.withConnectionProvider(new SingleConnectionProvider(connection));\n            const db = new Kysely({\n                ...this.#props,\n                executor,\n            });\n            return await callback(db);\n        });\n    }\n}\nexport class TransactionBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    setAccessMode(accessMode) {\n        return new TransactionBuilder({\n            ...this.#props,\n            accessMode,\n        });\n    }\n    setIsolationLevel(isolationLevel) {\n        return new TransactionBuilder({\n            ...this.#props,\n            isolationLevel,\n        });\n    }\n    async execute(callback) {\n        const { isolationLevel, accessMode, ...kyselyProps } = this.#props;\n        const settings = { isolationLevel, accessMode };\n        validateTransactionSettings(settings);\n        return this.#props.executor.provideConnection(async (connection) => {\n            const executor = this.#props.executor.withConnectionProvider(new SingleConnectionProvider(connection));\n            const transaction = new Transaction({\n                ...kyselyProps,\n                executor,\n            });\n            try {\n                await this.#props.driver.beginTransaction(connection, settings);\n                const result = await callback(transaction);\n                await this.#props.driver.commitTransaction(connection);\n                return result;\n            }\n            catch (error) {\n                await this.#props.driver.rollbackTransaction(connection);\n                throw error;\n            }\n        });\n    }\n}\nexport class ControlledTransactionBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    setAccessMode(accessMode) {\n        return new ControlledTransactionBuilder({\n            ...this.#props,\n            accessMode,\n        });\n    }\n    setIsolationLevel(isolationLevel) {\n        return new ControlledTransactionBuilder({\n            ...this.#props,\n            isolationLevel,\n        });\n    }\n    async execute() {\n        const { isolationLevel, accessMode, ...props } = this.#props;\n        const settings = { isolationLevel, accessMode };\n        validateTransactionSettings(settings);\n        const connection = await provideControlledConnection(this.#props.executor);\n        await this.#props.driver.beginTransaction(connection.connection, settings);\n        return new ControlledTransaction({\n            ...props,\n            connection,\n            executor: this.#props.executor.withConnectionProvider(new SingleConnectionProvider(connection.connection)),\n        });\n    }\n}\nexport class ControlledTransaction extends Transaction {\n    #props;\n    #compileQuery;\n    #state;\n    constructor(props) {\n        const state = { isCommitted: false, isRolledBack: false };\n        props = {\n            ...props,\n            executor: new NotCommittedOrRolledBackAssertingExecutor(props.executor, state),\n        };\n        const { connection, ...transactionProps } = props;\n        super(transactionProps);\n        this.#props = freeze(props);\n        this.#state = state;\n        const queryId = createQueryId();\n        this.#compileQuery = (node) => props.executor.compileQuery(node, queryId);\n    }\n    get isCommitted() {\n        return this.#state.isCommitted;\n    }\n    get isRolledBack() {\n        return this.#state.isRolledBack;\n    }\n    /**\n     * Commits the transaction.\n     *\n     * See {@link rollback}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     * import type { Database } from 'type-editor' // imaginary module\n     *\n     * const trx = await db.startTransaction().execute()\n     *\n     * try {\n     *   await doSomething(trx)\n     *\n     *   await trx.commit().execute()\n     * } catch (error) {\n     *   await trx.rollback().execute()\n     * }\n     *\n     * async function doSomething(kysely: Kysely<Database>) {}\n     * ```\n     */\n    commit() {\n        assertNotCommittedOrRolledBack(this.#state);\n        return new Command(async () => {\n            await this.#props.driver.commitTransaction(this.#props.connection.connection);\n            this.#state.isCommitted = true;\n            this.#props.connection.release();\n        });\n    }\n    /**\n     * Rolls back the transaction.\n     *\n     * See {@link commit} and {@link rollbackToSavepoint}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     * import type { Database } from 'type-editor' // imaginary module\n     *\n     * const trx = await db.startTransaction().execute()\n     *\n     * try {\n     *   await doSomething(trx)\n     *\n     *   await trx.commit().execute()\n     * } catch (error) {\n     *   await trx.rollback().execute()\n     * }\n     *\n     * async function doSomething(kysely: Kysely<Database>) {}\n     * ```\n     */\n    rollback() {\n        assertNotCommittedOrRolledBack(this.#state);\n        return new Command(async () => {\n            await this.#props.driver.rollbackTransaction(this.#props.connection.connection);\n            this.#state.isRolledBack = true;\n            this.#props.connection.release();\n        });\n    }\n    /**\n     * Creates a savepoint with a given name.\n     *\n     * See {@link rollbackToSavepoint} and {@link releaseSavepoint}.\n     *\n     * For a type-safe experience, you should use the returned instance from now on.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     * import type { Database } from 'type-editor' // imaginary module\n     *\n     * const trx = await db.startTransaction().execute()\n     *\n     * await insertJennifer(trx)\n     *\n     * const trxAfterJennifer = await trx.savepoint('after_jennifer').execute()\n     *\n     * try {\n     *   await doSomething(trxAfterJennifer)\n     * } catch (error) {\n     *   await trxAfterJennifer.rollbackToSavepoint('after_jennifer').execute()\n     * }\n     *\n     * async function insertJennifer(kysely: Kysely<Database>) {}\n     * async function doSomething(kysely: Kysely<Database>) {}\n     * ```\n     */\n    savepoint(savepointName) {\n        assertNotCommittedOrRolledBack(this.#state);\n        return new Command(async () => {\n            await this.#props.driver.savepoint?.(this.#props.connection.connection, savepointName, this.#compileQuery);\n            return new ControlledTransaction({ ...this.#props });\n        });\n    }\n    /**\n     * Rolls back to a savepoint with a given name.\n     *\n     * See {@link savepoint} and {@link releaseSavepoint}.\n     *\n     * You must use the same instance returned by {@link savepoint}, or\n     * escape the type-check by using `as any`.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     * import type { Database } from 'type-editor' // imaginary module\n     *\n     * const trx = await db.startTransaction().execute()\n     *\n     * await insertJennifer(trx)\n     *\n     * const trxAfterJennifer = await trx.savepoint('after_jennifer').execute()\n     *\n     * try {\n     *   await doSomething(trxAfterJennifer)\n     * } catch (error) {\n     *   await trxAfterJennifer.rollbackToSavepoint('after_jennifer').execute()\n     * }\n     *\n     * async function insertJennifer(kysely: Kysely<Database>) {}\n     * async function doSomething(kysely: Kysely<Database>) {}\n     * ```\n     */\n    rollbackToSavepoint(savepointName) {\n        assertNotCommittedOrRolledBack(this.#state);\n        return new Command(async () => {\n            await this.#props.driver.rollbackToSavepoint?.(this.#props.connection.connection, savepointName, this.#compileQuery);\n            return new ControlledTransaction({ ...this.#props });\n        });\n    }\n    /**\n     * Releases a savepoint with a given name.\n     *\n     * See {@link savepoint} and {@link rollbackToSavepoint}.\n     *\n     * You must use the same instance returned by {@link savepoint}, or\n     * escape the type-check by using `as any`.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Kysely } from 'kysely'\n     * import type { Database } from 'type-editor' // imaginary module\n     *\n     * const trx = await db.startTransaction().execute()\n     *\n     * await insertJennifer(trx)\n     *\n     * const trxAfterJennifer = await trx.savepoint('after_jennifer').execute()\n     *\n     * try {\n     *   await doSomething(trxAfterJennifer)\n     * } catch (error) {\n     *   await trxAfterJennifer.rollbackToSavepoint('after_jennifer').execute()\n     * }\n     *\n     * await trxAfterJennifer.releaseSavepoint('after_jennifer').execute()\n     *\n     * await doSomethingElse(trx)\n     *\n     * async function insertJennifer(kysely: Kysely<Database>) {}\n     * async function doSomething(kysely: Kysely<Database>) {}\n     * async function doSomethingElse(kysely: Kysely<Database>) {}\n     * ```\n     */\n    releaseSavepoint(savepointName) {\n        assertNotCommittedOrRolledBack(this.#state);\n        return new Command(async () => {\n            await this.#props.driver.releaseSavepoint?.(this.#props.connection.connection, savepointName, this.#compileQuery);\n            return new ControlledTransaction({ ...this.#props });\n        });\n    }\n    withPlugin(plugin) {\n        return new ControlledTransaction({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    withoutPlugins() {\n        return new ControlledTransaction({\n            ...this.#props,\n            executor: this.#props.executor.withoutPlugins(),\n        });\n    }\n    withSchema(schema) {\n        return new ControlledTransaction({\n            ...this.#props,\n            executor: this.#props.executor.withPluginAtFront(new WithSchemaPlugin(schema)),\n        });\n    }\n    withTables() {\n        return new ControlledTransaction({ ...this.#props });\n    }\n}\nexport class Command {\n    #cb;\n    constructor(cb) {\n        this.#cb = cb;\n    }\n    /**\n     * Executes the command.\n     */\n    async execute() {\n        return await this.#cb();\n    }\n}\nfunction assertNotCommittedOrRolledBack(state) {\n    if (state.isCommitted) {\n        throw new Error('Transaction is already committed');\n    }\n    if (state.isRolledBack) {\n        throw new Error('Transaction is already rolled back');\n    }\n}\n/**\n * An executor wrapper that asserts that the transaction state is not committed\n * or rolled back when a query is executed.\n *\n * @internal\n */\nclass NotCommittedOrRolledBackAssertingExecutor {\n    #executor;\n    #state;\n    constructor(executor, state) {\n        if (executor instanceof NotCommittedOrRolledBackAssertingExecutor) {\n            this.#executor = executor.#executor;\n        }\n        else {\n            this.#executor = executor;\n        }\n        this.#state = state;\n    }\n    get adapter() {\n        return this.#executor.adapter;\n    }\n    get plugins() {\n        return this.#executor.plugins;\n    }\n    transformQuery(node, queryId) {\n        return this.#executor.transformQuery(node, queryId);\n    }\n    compileQuery(node, queryId) {\n        return this.#executor.compileQuery(node, queryId);\n    }\n    provideConnection(consumer) {\n        return this.#executor.provideConnection(consumer);\n    }\n    executeQuery(compiledQuery, queryId) {\n        assertNotCommittedOrRolledBack(this.#state);\n        return this.#executor.executeQuery(compiledQuery, queryId);\n    }\n    stream(compiledQuery, chunkSize, queryId) {\n        assertNotCommittedOrRolledBack(this.#state);\n        return this.#executor.stream(compiledQuery, chunkSize, queryId);\n    }\n    withConnectionProvider(connectionProvider) {\n        return new NotCommittedOrRolledBackAssertingExecutor(this.#executor.withConnectionProvider(connectionProvider), this.#state);\n    }\n    withPlugin(plugin) {\n        return new NotCommittedOrRolledBackAssertingExecutor(this.#executor.withPlugin(plugin), this.#state);\n    }\n    withPlugins(plugins) {\n        return new NotCommittedOrRolledBackAssertingExecutor(this.#executor.withPlugins(plugins), this.#state);\n    }\n    withPluginAtFront(plugin) {\n        return new NotCommittedOrRolledBackAssertingExecutor(this.#executor.withPluginAtFront(plugin), this.#state);\n    }\n    withoutPlugins() {\n        return new NotCommittedOrRolledBackAssertingExecutor(this.#executor.withoutPlugins(), this.#state);\n    }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;;;;;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACA,aAAa;AACb,OAAO,YAAY,KAAK,OAAO;AAkCxB,MAAM,eAAe,+MAAA,CAAA,eAAY;IACpC,CAAA,KAAM,CAAC;IACP,YAAY,IAAI,CAAE;QACd,IAAI;QACJ,IAAI;QACJ,IAAI,cAAc,OAAO;YACrB,aAAa;gBAAE,UAAU,KAAK,QAAQ;YAAC;YACvC,QAAQ;gBAAE,GAAG,IAAI;YAAC;QACtB,OACK;YACD,MAAM,UAAU,KAAK,OAAO;YAC5B,MAAM,SAAS,QAAQ,YAAY;YACnC,MAAM,WAAW,QAAQ,mBAAmB;YAC5C,MAAM,UAAU,QAAQ,aAAa;YACrC,MAAM,MAAM,IAAI,0MAAA,CAAA,MAAG,CAAC,KAAK,GAAG,IAAI,EAAE;YAClC,MAAM,gBAAgB,IAAI,0NAAA,CAAA,gBAAa,CAAC,QAAQ;YAChD,MAAM,qBAAqB,IAAI,0OAAA,CAAA,4BAAyB,CAAC;YACzD,MAAM,WAAW,IAAI,gPAAA,CAAA,uBAAoB,CAAC,UAAU,SAAS,oBAAoB,KAAK,OAAO,IAAI,EAAE;YACnG,aAAa;gBAAE;YAAS;YACxB,QAAQ;gBACJ,QAAQ;gBACR;gBACA;gBACA,QAAQ;YACZ;QACJ;QACA,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;KAEC,GACD,IAAI,SAAS;QACT,OAAO,IAAI,+MAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;IAChD;IACA;;;;;KAKC,GACD,IAAI,UAAU;QACV,OAAO,IAAI,iNAAA,CAAA,gBAAa;IAC5B;IACA;;KAEC,GACD,IAAI,gBAAgB;QAChB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc;IACrE;IACA,KAAK,KAAK,EAAE;QACR,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC;YACnB,MAAM,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,SAAS,YAAY,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;QAC3E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+CC,GACD,IAAI,KAAK;QACL,OAAO,CAAA,GAAA,qOAAA,CAAA,uBAAoB,AAAD;IAC9B;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsEC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IACnD;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8GC,GACD,mBAAmB;QACf,OAAO,IAAI,6BAA6B;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IAC7D;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,aAAa;QACT,OAAO,IAAI,kBAAkB;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IAClD;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,OAAO;YACd,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA;;KAEC,GACD,iBAAiB;QACb,OAAO,IAAI,OAAO;YACd,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc;QACjD;IACJ;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,OAAO;YACd,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;QAC1E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,aAAa;QACT,OAAO,IAAI,OAAO;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IACvC;IACA;;;;KAIC,GACD,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,OAAO;IACpC;IACA;;;;KAIC,GACD,IAAI,gBAAgB;QAChB,OAAO;IACX;IACA;;;KAGC,GACD,cAAc;QACV,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;IAC/B;IACA;;;;KAIC,GACD,aAAa,KAAK,EAAE,UAAU,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD,GAAG,EAAE;QAC3C,MAAM,gBAAgB,CAAA,GAAA,iNAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM,OAAO,KAAK;QAC9D,OAAO,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,eAAe;IAC1D;IACA,MAAM,CAAC,OAAO,YAAY,CAAC,GAAG;QAC1B,MAAM,IAAI,CAAC,OAAO;IACtB;AACJ;AACO,MAAM,oBAAoB;IAC7B,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,oEAAoE;IACpE,gEAAgE;IAChE,oBAAoB;IACpB,IAAI,gBAAgB;QAChB,OAAO;IACX;IACA,cAAc;QACV,MAAM,IAAI,MAAM;IACpB;IACA,aAAa;QACT,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,MAAM;IACpB;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,iBAAiB;QACb,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc;QACjD;IACJ;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;QAC1E;IACJ;IACA,aAAa;QACT,OAAO,IAAI,YAAY;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IAC5C;AACJ;AACO,SAAS,cAAc,GAAG;IAC7B,OAAQ,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QACb,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,MAAM,KACnB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,MAAM,KACnB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,KACrB,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO;AAC5B;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,MAAM,QAAQ,QAAQ,EAAE;QACpB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO;YACjD,MAAM,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,yOAAA,CAAA,2BAAwB,CAAC;YAC1F,MAAM,KAAK,IAAI,OAAO;gBAClB,GAAG,IAAI,CAAC,CAAA,KAAM;gBACd;YACJ;YACA,OAAO,MAAM,SAAS;QAC1B;IACJ;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,cAAc,UAAU,EAAE;QACtB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd;QACJ;IACJ;IACA,kBAAkB,cAAc,EAAE;QAC9B,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd;QACJ;IACJ;IACA,MAAM,QAAQ,QAAQ,EAAE;QACpB,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,aAAa,GAAG,IAAI,CAAC,CAAA,KAAM;QAClE,MAAM,WAAW;YAAE;YAAgB;QAAW;QAC9C,CAAA,GAAA,+MAAA,CAAA,8BAA2B,AAAD,EAAE;QAC5B,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO;YACjD,MAAM,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,yOAAA,CAAA,2BAAwB,CAAC;YAC1F,MAAM,cAAc,IAAI,YAAY;gBAChC,GAAG,WAAW;gBACd;YACJ;YACA,IAAI;gBACA,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBACtD,MAAM,SAAS,MAAM,SAAS;gBAC9B,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBAC3C,OAAO;YACX,EACA,OAAO,OAAO;gBACV,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC7C,MAAM;YACV;QACJ;IACJ;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,cAAc,UAAU,EAAE;QACtB,OAAO,IAAI,6BAA6B;YACpC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd;QACJ;IACJ;IACA,kBAAkB,cAAc,EAAE;QAC9B,OAAO,IAAI,6BAA6B;YACpC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd;QACJ;IACJ;IACA,MAAM,UAAU;QACZ,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA,KAAM;QAC5D,MAAM,WAAW;YAAE;YAAgB;QAAW;QAC9C,CAAA,GAAA,+MAAA,CAAA,8BAA2B,AAAD,EAAE;QAC5B,MAAM,aAAa,MAAM,CAAA,GAAA,0OAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACzE,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,UAAU,EAAE;QACjE,OAAO,IAAI,sBAAsB;YAC7B,GAAG,KAAK;YACR;YACA,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,yOAAA,CAAA,2BAAwB,CAAC,WAAW,UAAU;QAC5G;IACJ;AACJ;AACO,MAAM,8BAA8B;IACvC,CAAA,KAAM,CAAC;IACP,CAAA,YAAa,CAAC;IACd,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,MAAM,QAAQ;YAAE,aAAa;YAAO,cAAc;QAAM;QACxD,QAAQ;YACJ,GAAG,KAAK;YACR,UAAU,IAAI,0CAA0C,MAAM,QAAQ,EAAE;QAC5E;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,kBAAkB,GAAG;QAC5C,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;QACrB,IAAI,CAAC,CAAA,KAAM,GAAG;QACd,MAAM,UAAU,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;QAC5B,IAAI,CAAC,CAAA,YAAa,GAAG,CAAC,OAAS,MAAM,QAAQ,CAAC,YAAY,CAAC,MAAM;IACrE;IACA,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,WAAW;IAClC;IACA,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,YAAY;IACnC;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,SAAS;QACL,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,QAAQ;YACf,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,UAAU;YAC5E,IAAI,CAAC,CAAA,KAAM,CAAC,WAAW,GAAG;YAC1B,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,OAAO;QAClC;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,WAAW;QACP,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,QAAQ;YACf,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,UAAU;YAC9E,IAAI,CAAC,CAAA,KAAM,CAAC,YAAY,GAAG;YAC3B,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,OAAO;QAClC;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BC,GACD,UAAU,aAAa,EAAE;QACrB,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,QAAQ;YACf,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC,CAAA,YAAa;YACzG,OAAO,IAAI,sBAAsB;gBAAE,GAAG,IAAI,CAAC,CAAA,KAAM;YAAC;QACtD;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,oBAAoB,aAAa,EAAE;QAC/B,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,QAAQ;YACf,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC,CAAA,YAAa;YACnH,OAAO,IAAI,sBAAsB;gBAAE,GAAG,IAAI,CAAC,CAAA,KAAM;YAAC;QACtD;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkCC,GACD,iBAAiB,aAAa,EAAE;QAC5B,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,QAAQ;YACf,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,UAAU,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC,CAAA,YAAa;YAChH,OAAO,IAAI,sBAAsB;gBAAE,GAAG,IAAI,CAAC,CAAA,KAAM;YAAC;QACtD;IACJ;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,sBAAsB;YAC7B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,iBAAiB;QACb,OAAO,IAAI,sBAAsB;YAC7B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc;QACjD;IACJ;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,sBAAsB;YAC7B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;QAC1E;IACJ;IACA,aAAa;QACT,OAAO,IAAI,sBAAsB;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IACtD;AACJ;AACO,MAAM;IACT,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA;;KAEC,GACD,MAAM,UAAU;QACZ,OAAO,MAAM,IAAI,CAAC,CAAA,EAAG;IACzB;AACJ;AACA,SAAS,+BAA+B,KAAK;IACzC,IAAI,MAAM,WAAW,EAAE;QACnB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,MAAM,YAAY,EAAE;QACpB,MAAM,IAAI,MAAM;IACpB;AACJ;AACA;;;;;CAKC,GACD,MAAM;IACF,CAAA,QAAS,CAAC;IACV,CAAA,KAAM,CAAC;IACP,YAAY,QAAQ,EAAE,KAAK,CAAE;QACzB,IAAI,oBAAoB,2CAA2C;YAC/D,IAAI,CAAC,CAAA,QAAS,GAAG,SAAS,CAAA,QAAS;QACvC,OACK;YACD,IAAI,CAAC,CAAA,QAAS,GAAG;QACrB;QACA,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,OAAO;IACjC;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,OAAO;IACjC;IACA,eAAe,IAAI,EAAE,OAAO,EAAE;QAC1B,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,cAAc,CAAC,MAAM;IAC/C;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,YAAY,CAAC,MAAM;IAC7C;IACA,kBAAkB,QAAQ,EAAE;QACxB,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,iBAAiB,CAAC;IAC5C;IACA,aAAa,aAAa,EAAE,OAAO,EAAE;QACjC,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,YAAY,CAAC,eAAe;IACtD;IACA,OAAO,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE;QACtC,+BAA+B,IAAI,CAAC,CAAA,KAAM;QAC1C,OAAO,IAAI,CAAC,CAAA,QAAS,CAAC,MAAM,CAAC,eAAe,WAAW;IAC3D;IACA,uBAAuB,kBAAkB,EAAE;QACvC,OAAO,IAAI,0CAA0C,IAAI,CAAC,CAAA,QAAS,CAAC,sBAAsB,CAAC,qBAAqB,IAAI,CAAC,CAAA,KAAM;IAC/H;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,0CAA0C,IAAI,CAAC,CAAA,QAAS,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,CAAA,KAAM;IACvG;IACA,YAAY,OAAO,EAAE;QACjB,OAAO,IAAI,0CAA0C,IAAI,CAAC,CAAA,QAAS,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,CAAA,KAAM;IACzG;IACA,kBAAkB,MAAM,EAAE;QACtB,OAAO,IAAI,0CAA0C,IAAI,CAAC,CAAA,QAAS,CAAC,iBAAiB,CAAC,SAAS,IAAI,CAAC,CAAA,KAAM;IAC9G;IACA,iBAAiB;QACb,OAAO,IAAI,0CAA0C,IAAI,CAAC,CAAA,QAAS,CAAC,cAAc,IAAI,IAAI,CAAC,CAAA,KAAM;IACrG;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3330, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-compiler/compiled-query.js"], "sourcesContent": ["/// <reference types=\"./compiled-query.d.ts\" />\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { freeze } from '../util/object-utils.js';\nimport { createQueryId } from '../util/query-id.js';\nexport const CompiledQuery = freeze({\n    raw(sql, parameters = []) {\n        return freeze({\n            sql,\n            query: RawNode.createWithSql(sql),\n            parameters: freeze(parameters),\n            queryId: createQueryId(),\n        });\n    },\n});\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;AAC/C;AACA;AACA;;;;AACO,MAAM,gBAAgB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAChC,KAAI,GAAG,EAAE,aAAa,EAAE;QACpB,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACV;YACA,OAAO,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YAC7B,YAAY,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACnB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;QACzB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3356, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-compiler/default-query-compiler.js"], "sourcesContent": ["/// <reference types=\"./default-query-compiler.d.ts\" />\nimport { CreateTableNode } from '../operation-node/create-table-node.js';\nimport { InsertQueryNode } from '../operation-node/insert-query-node.js';\nimport { OperationNodeVisitor } from '../operation-node/operation-node-visitor.js';\nimport { OperatorNode } from '../operation-node/operator-node.js';\nimport { ParensNode } from '../operation-node/parens-node.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { freeze, isString, isNumber, isBoolean, isNull, isDate, isBigInt, } from '../util/object-utils.js';\nimport { CreateViewNode } from '../operation-node/create-view-node.js';\nimport { SetOperationNode } from '../operation-node/set-operation-node.js';\nimport { MergeQueryNode } from '../operation-node/merge-query-node.js';\nimport { logOnce } from '../util/log-once.js';\nconst LIT_WRAP_REGEX = /'/g;\nexport class DefaultQueryCompiler extends OperationNodeVisitor {\n    #sql = '';\n    #parameters = [];\n    get numParameters() {\n        return this.#parameters.length;\n    }\n    compileQuery(node, queryId) {\n        this.#sql = '';\n        this.#parameters = [];\n        this.nodeStack.splice(0, this.nodeStack.length);\n        this.visitNode(node);\n        return freeze({\n            query: node,\n            queryId,\n            sql: this.getSql(),\n            parameters: [...this.#parameters],\n        });\n    }\n    getSql() {\n        return this.#sql;\n    }\n    visitSelectQuery(node) {\n        const wrapInParens = this.parentNode !== undefined &&\n            !ParensNode.is(this.parentNode) &&\n            !InsertQueryNode.is(this.parentNode) &&\n            !CreateTableNode.is(this.parentNode) &&\n            !CreateViewNode.is(this.parentNode) &&\n            !SetOperationNode.is(this.parentNode);\n        if (this.parentNode === undefined && node.explain) {\n            this.visitNode(node.explain);\n            this.append(' ');\n        }\n        if (wrapInParens) {\n            this.append('(');\n        }\n        if (node.with) {\n            this.visitNode(node.with);\n            this.append(' ');\n        }\n        this.append('select');\n        if (node.distinctOn) {\n            this.append(' ');\n            this.compileDistinctOn(node.distinctOn);\n        }\n        if (node.frontModifiers?.length) {\n            this.append(' ');\n            this.compileList(node.frontModifiers, ' ');\n        }\n        if (node.top) {\n            this.append(' ');\n            this.visitNode(node.top);\n        }\n        if (node.selections) {\n            this.append(' ');\n            this.compileList(node.selections);\n        }\n        if (node.from) {\n            this.append(' ');\n            this.visitNode(node.from);\n        }\n        if (node.joins) {\n            this.append(' ');\n            this.compileList(node.joins, ' ');\n        }\n        if (node.where) {\n            this.append(' ');\n            this.visitNode(node.where);\n        }\n        if (node.groupBy) {\n            this.append(' ');\n            this.visitNode(node.groupBy);\n        }\n        if (node.having) {\n            this.append(' ');\n            this.visitNode(node.having);\n        }\n        if (node.setOperations) {\n            this.append(' ');\n            this.compileList(node.setOperations, ' ');\n        }\n        if (node.orderBy) {\n            this.append(' ');\n            this.visitNode(node.orderBy);\n        }\n        if (node.limit) {\n            this.append(' ');\n            this.visitNode(node.limit);\n        }\n        if (node.offset) {\n            this.append(' ');\n            this.visitNode(node.offset);\n        }\n        if (node.fetch) {\n            this.append(' ');\n            this.visitNode(node.fetch);\n        }\n        if (node.endModifiers?.length) {\n            this.append(' ');\n            this.compileList(this.sortSelectModifiers([...node.endModifiers]), ' ');\n        }\n        if (wrapInParens) {\n            this.append(')');\n        }\n    }\n    visitFrom(node) {\n        this.append('from ');\n        this.compileList(node.froms);\n    }\n    visitSelection(node) {\n        this.visitNode(node.selection);\n    }\n    visitColumn(node) {\n        this.visitNode(node.column);\n    }\n    compileDistinctOn(expressions) {\n        this.append('distinct on (');\n        this.compileList(expressions);\n        this.append(')');\n    }\n    compileList(nodes, separator = ', ') {\n        const lastIndex = nodes.length - 1;\n        for (let i = 0; i <= lastIndex; i++) {\n            this.visitNode(nodes[i]);\n            if (i < lastIndex) {\n                this.append(separator);\n            }\n        }\n    }\n    visitWhere(node) {\n        this.append('where ');\n        this.visitNode(node.where);\n    }\n    visitHaving(node) {\n        this.append('having ');\n        this.visitNode(node.having);\n    }\n    visitInsertQuery(node) {\n        const rootQueryNode = this.nodeStack.find(QueryNode.is);\n        const isSubQuery = rootQueryNode !== node;\n        if (!isSubQuery && node.explain) {\n            this.visitNode(node.explain);\n            this.append(' ');\n        }\n        if (isSubQuery && !MergeQueryNode.is(rootQueryNode)) {\n            this.append('(');\n        }\n        if (node.with) {\n            this.visitNode(node.with);\n            this.append(' ');\n        }\n        this.append(node.replace ? 'replace' : 'insert');\n        // TODO: remove in 0.29.\n        if (node.ignore) {\n            logOnce('`InsertQueryNode.ignore` is deprecated. Use `InsertQueryNode.orAction` instead.');\n            this.append(' ignore');\n        }\n        if (node.orAction) {\n            this.append(' ');\n            this.visitNode(node.orAction);\n        }\n        if (node.top) {\n            this.append(' ');\n            this.visitNode(node.top);\n        }\n        if (node.into) {\n            this.append(' into ');\n            this.visitNode(node.into);\n        }\n        if (node.columns) {\n            this.append(' (');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n        if (node.output) {\n            this.append(' ');\n            this.visitNode(node.output);\n        }\n        if (node.values) {\n            this.append(' ');\n            this.visitNode(node.values);\n        }\n        if (node.defaultValues) {\n            this.append(' ');\n            this.append('default values');\n        }\n        if (node.onConflict) {\n            this.append(' ');\n            this.visitNode(node.onConflict);\n        }\n        if (node.onDuplicateKey) {\n            this.append(' ');\n            this.visitNode(node.onDuplicateKey);\n        }\n        if (node.returning) {\n            this.append(' ');\n            this.visitNode(node.returning);\n        }\n        if (isSubQuery && !MergeQueryNode.is(rootQueryNode)) {\n            this.append(')');\n        }\n        if (node.endModifiers?.length) {\n            this.append(' ');\n            this.compileList(node.endModifiers, ' ');\n        }\n    }\n    visitValues(node) {\n        this.append('values ');\n        this.compileList(node.values);\n    }\n    visitDeleteQuery(node) {\n        const isSubQuery = this.nodeStack.find(QueryNode.is) !== node;\n        if (!isSubQuery && node.explain) {\n            this.visitNode(node.explain);\n            this.append(' ');\n        }\n        if (isSubQuery) {\n            this.append('(');\n        }\n        if (node.with) {\n            this.visitNode(node.with);\n            this.append(' ');\n        }\n        this.append('delete ');\n        if (node.top) {\n            this.visitNode(node.top);\n            this.append(' ');\n        }\n        this.visitNode(node.from);\n        if (node.output) {\n            this.append(' ');\n            this.visitNode(node.output);\n        }\n        if (node.using) {\n            this.append(' ');\n            this.visitNode(node.using);\n        }\n        if (node.joins) {\n            this.append(' ');\n            this.compileList(node.joins, ' ');\n        }\n        if (node.where) {\n            this.append(' ');\n            this.visitNode(node.where);\n        }\n        if (node.orderBy) {\n            this.append(' ');\n            this.visitNode(node.orderBy);\n        }\n        if (node.limit) {\n            this.append(' ');\n            this.visitNode(node.limit);\n        }\n        if (node.returning) {\n            this.append(' ');\n            this.visitNode(node.returning);\n        }\n        if (isSubQuery) {\n            this.append(')');\n        }\n        if (node.endModifiers?.length) {\n            this.append(' ');\n            this.compileList(node.endModifiers, ' ');\n        }\n    }\n    visitReturning(node) {\n        this.append('returning ');\n        this.compileList(node.selections);\n    }\n    visitAlias(node) {\n        this.visitNode(node.node);\n        this.append(' as ');\n        this.visitNode(node.alias);\n    }\n    visitReference(node) {\n        if (node.table) {\n            this.visitNode(node.table);\n            this.append('.');\n        }\n        this.visitNode(node.column);\n    }\n    visitSelectAll(_) {\n        this.append('*');\n    }\n    visitIdentifier(node) {\n        this.append(this.getLeftIdentifierWrapper());\n        this.compileUnwrappedIdentifier(node);\n        this.append(this.getRightIdentifierWrapper());\n    }\n    compileUnwrappedIdentifier(node) {\n        if (!isString(node.name)) {\n            throw new Error('a non-string identifier was passed to compileUnwrappedIdentifier.');\n        }\n        this.append(this.sanitizeIdentifier(node.name));\n    }\n    visitAnd(node) {\n        this.visitNode(node.left);\n        this.append(' and ');\n        this.visitNode(node.right);\n    }\n    visitOr(node) {\n        this.visitNode(node.left);\n        this.append(' or ');\n        this.visitNode(node.right);\n    }\n    visitValue(node) {\n        if (node.immediate) {\n            this.appendImmediateValue(node.value);\n        }\n        else {\n            this.appendValue(node.value);\n        }\n    }\n    visitValueList(node) {\n        this.append('(');\n        this.compileList(node.values);\n        this.append(')');\n    }\n    visitTuple(node) {\n        this.append('(');\n        this.compileList(node.values);\n        this.append(')');\n    }\n    visitPrimitiveValueList(node) {\n        this.append('(');\n        const { values } = node;\n        for (let i = 0; i < values.length; ++i) {\n            this.appendValue(values[i]);\n            if (i !== values.length - 1) {\n                this.append(', ');\n            }\n        }\n        this.append(')');\n    }\n    visitParens(node) {\n        this.append('(');\n        this.visitNode(node.node);\n        this.append(')');\n    }\n    visitJoin(node) {\n        this.append(JOIN_TYPE_SQL[node.joinType]);\n        this.append(' ');\n        this.visitNode(node.table);\n        if (node.on) {\n            this.append(' ');\n            this.visitNode(node.on);\n        }\n    }\n    visitOn(node) {\n        this.append('on ');\n        this.visitNode(node.on);\n    }\n    visitRaw(node) {\n        const { sqlFragments, parameters: params } = node;\n        for (let i = 0; i < sqlFragments.length; ++i) {\n            this.append(sqlFragments[i]);\n            if (params.length > i) {\n                this.visitNode(params[i]);\n            }\n        }\n    }\n    visitOperator(node) {\n        this.append(node.operator);\n    }\n    visitTable(node) {\n        this.visitNode(node.table);\n    }\n    visitSchemableIdentifier(node) {\n        if (node.schema) {\n            this.visitNode(node.schema);\n            this.append('.');\n        }\n        this.visitNode(node.identifier);\n    }\n    visitCreateTable(node) {\n        this.append('create ');\n        if (node.frontModifiers && node.frontModifiers.length > 0) {\n            this.compileList(node.frontModifiers, ' ');\n            this.append(' ');\n        }\n        if (node.temporary) {\n            this.append('temporary ');\n        }\n        this.append('table ');\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.table);\n        if (node.selectQuery) {\n            this.append(' as ');\n            this.visitNode(node.selectQuery);\n        }\n        else {\n            this.append(' (');\n            this.compileList([...node.columns, ...(node.constraints ?? [])]);\n            this.append(')');\n            if (node.onCommit) {\n                this.append(' on commit ');\n                this.append(node.onCommit);\n            }\n            if (node.endModifiers && node.endModifiers.length > 0) {\n                this.append(' ');\n                this.compileList(node.endModifiers, ' ');\n            }\n        }\n    }\n    visitColumnDefinition(node) {\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.column);\n        this.append(' ');\n        this.visitNode(node.dataType);\n        if (node.unsigned) {\n            this.append(' unsigned');\n        }\n        if (node.frontModifiers && node.frontModifiers.length > 0) {\n            this.append(' ');\n            this.compileList(node.frontModifiers, ' ');\n        }\n        if (node.generated) {\n            this.append(' ');\n            this.visitNode(node.generated);\n        }\n        if (node.identity) {\n            this.append(' identity');\n        }\n        if (node.defaultTo) {\n            this.append(' ');\n            this.visitNode(node.defaultTo);\n        }\n        if (node.notNull) {\n            this.append(' not null');\n        }\n        if (node.unique) {\n            this.append(' unique');\n        }\n        if (node.nullsNotDistinct) {\n            this.append(' nulls not distinct');\n        }\n        if (node.primaryKey) {\n            this.append(' primary key');\n        }\n        if (node.autoIncrement) {\n            this.append(' ');\n            this.append(this.getAutoIncrement());\n        }\n        if (node.references) {\n            this.append(' ');\n            this.visitNode(node.references);\n        }\n        if (node.check) {\n            this.append(' ');\n            this.visitNode(node.check);\n        }\n        if (node.endModifiers && node.endModifiers.length > 0) {\n            this.append(' ');\n            this.compileList(node.endModifiers, ' ');\n        }\n    }\n    getAutoIncrement() {\n        return 'auto_increment';\n    }\n    visitReferences(node) {\n        this.append('references ');\n        this.visitNode(node.table);\n        this.append(' (');\n        this.compileList(node.columns);\n        this.append(')');\n        if (node.onDelete) {\n            this.append(' on delete ');\n            this.append(node.onDelete);\n        }\n        if (node.onUpdate) {\n            this.append(' on update ');\n            this.append(node.onUpdate);\n        }\n    }\n    visitDropTable(node) {\n        this.append('drop table ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.table);\n        if (node.cascade) {\n            this.append(' cascade');\n        }\n    }\n    visitDataType(node) {\n        this.append(node.dataType);\n    }\n    visitOrderBy(node) {\n        this.append('order by ');\n        this.compileList(node.items);\n    }\n    visitOrderByItem(node) {\n        this.visitNode(node.orderBy);\n        if (node.collation) {\n            this.append(' ');\n            this.visitNode(node.collation);\n        }\n        if (node.direction) {\n            this.append(' ');\n            this.visitNode(node.direction);\n        }\n        if (node.nulls) {\n            this.append(' nulls ');\n            this.append(node.nulls);\n        }\n    }\n    visitGroupBy(node) {\n        this.append('group by ');\n        this.compileList(node.items);\n    }\n    visitGroupByItem(node) {\n        this.visitNode(node.groupBy);\n    }\n    visitUpdateQuery(node) {\n        const rootQueryNode = this.nodeStack.find(QueryNode.is);\n        const isSubQuery = rootQueryNode !== node;\n        if (!isSubQuery && node.explain) {\n            this.visitNode(node.explain);\n            this.append(' ');\n        }\n        if (isSubQuery && !MergeQueryNode.is(rootQueryNode)) {\n            this.append('(');\n        }\n        if (node.with) {\n            this.visitNode(node.with);\n            this.append(' ');\n        }\n        this.append('update ');\n        if (node.top) {\n            this.visitNode(node.top);\n            this.append(' ');\n        }\n        if (node.table) {\n            this.visitNode(node.table);\n            this.append(' ');\n        }\n        this.append('set ');\n        if (node.updates) {\n            this.compileList(node.updates);\n        }\n        if (node.output) {\n            this.append(' ');\n            this.visitNode(node.output);\n        }\n        if (node.from) {\n            this.append(' ');\n            this.visitNode(node.from);\n        }\n        if (node.joins) {\n            if (!node.from) {\n                throw new Error(\"Joins in an update query are only supported as a part of a PostgreSQL 'update set from join' query. If you want to create a MySQL 'update join set' query, see https://kysely.dev/docs/examples/update/my-sql-joins\");\n            }\n            this.append(' ');\n            this.compileList(node.joins, ' ');\n        }\n        if (node.where) {\n            this.append(' ');\n            this.visitNode(node.where);\n        }\n        if (node.orderBy) {\n            this.append(' ');\n            this.visitNode(node.orderBy);\n        }\n        if (node.limit) {\n            this.append(' ');\n            this.visitNode(node.limit);\n        }\n        if (node.returning) {\n            this.append(' ');\n            this.visitNode(node.returning);\n        }\n        if (isSubQuery && !MergeQueryNode.is(rootQueryNode)) {\n            this.append(')');\n        }\n        if (node.endModifiers?.length) {\n            this.append(' ');\n            this.compileList(node.endModifiers, ' ');\n        }\n    }\n    visitColumnUpdate(node) {\n        this.visitNode(node.column);\n        this.append(' = ');\n        this.visitNode(node.value);\n    }\n    visitLimit(node) {\n        this.append('limit ');\n        this.visitNode(node.limit);\n    }\n    visitOffset(node) {\n        this.append('offset ');\n        this.visitNode(node.offset);\n    }\n    visitOnConflict(node) {\n        this.append('on conflict');\n        if (node.columns) {\n            this.append(' (');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n        else if (node.constraint) {\n            this.append(' on constraint ');\n            this.visitNode(node.constraint);\n        }\n        else if (node.indexExpression) {\n            this.append(' (');\n            this.visitNode(node.indexExpression);\n            this.append(')');\n        }\n        if (node.indexWhere) {\n            this.append(' ');\n            this.visitNode(node.indexWhere);\n        }\n        if (node.doNothing === true) {\n            this.append(' do nothing');\n        }\n        else if (node.updates) {\n            this.append(' do update set ');\n            this.compileList(node.updates);\n            if (node.updateWhere) {\n                this.append(' ');\n                this.visitNode(node.updateWhere);\n            }\n        }\n    }\n    visitOnDuplicateKey(node) {\n        this.append('on duplicate key update ');\n        this.compileList(node.updates);\n    }\n    visitCreateIndex(node) {\n        this.append('create ');\n        if (node.unique) {\n            this.append('unique ');\n        }\n        this.append('index ');\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.name);\n        if (node.table) {\n            this.append(' on ');\n            this.visitNode(node.table);\n        }\n        if (node.using) {\n            this.append(' using ');\n            this.visitNode(node.using);\n        }\n        if (node.columns) {\n            this.append(' (');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n        if (node.nullsNotDistinct) {\n            this.append(' nulls not distinct');\n        }\n        if (node.where) {\n            this.append(' ');\n            this.visitNode(node.where);\n        }\n    }\n    visitDropIndex(node) {\n        this.append('drop index ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.name);\n        if (node.table) {\n            this.append(' on ');\n            this.visitNode(node.table);\n        }\n        if (node.cascade) {\n            this.append(' cascade');\n        }\n    }\n    visitCreateSchema(node) {\n        this.append('create schema ');\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.schema);\n    }\n    visitDropSchema(node) {\n        this.append('drop schema ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.schema);\n        if (node.cascade) {\n            this.append(' cascade');\n        }\n    }\n    visitPrimaryKeyConstraint(node) {\n        if (node.name) {\n            this.append('constraint ');\n            this.visitNode(node.name);\n            this.append(' ');\n        }\n        this.append('primary key (');\n        this.compileList(node.columns);\n        this.append(')');\n        this.buildDeferrable(node);\n    }\n    buildDeferrable(node) {\n        if (node.deferrable !== undefined) {\n            if (node.deferrable) {\n                this.append(' deferrable');\n            }\n            else {\n                this.append(' not deferrable');\n            }\n        }\n        if (node.initiallyDeferred !== undefined) {\n            if (node.initiallyDeferred) {\n                this.append(' initially deferred');\n            }\n            else {\n                this.append(' initially immediate');\n            }\n        }\n    }\n    visitUniqueConstraint(node) {\n        if (node.name) {\n            this.append('constraint ');\n            this.visitNode(node.name);\n            this.append(' ');\n        }\n        this.append('unique');\n        if (node.nullsNotDistinct) {\n            this.append(' nulls not distinct');\n        }\n        this.append(' (');\n        this.compileList(node.columns);\n        this.append(')');\n        this.buildDeferrable(node);\n    }\n    visitCheckConstraint(node) {\n        if (node.name) {\n            this.append('constraint ');\n            this.visitNode(node.name);\n            this.append(' ');\n        }\n        this.append('check (');\n        this.visitNode(node.expression);\n        this.append(')');\n    }\n    visitForeignKeyConstraint(node) {\n        if (node.name) {\n            this.append('constraint ');\n            this.visitNode(node.name);\n            this.append(' ');\n        }\n        this.append('foreign key (');\n        this.compileList(node.columns);\n        this.append(') ');\n        this.visitNode(node.references);\n        if (node.onDelete) {\n            this.append(' on delete ');\n            this.append(node.onDelete);\n        }\n        if (node.onUpdate) {\n            this.append(' on update ');\n            this.append(node.onUpdate);\n        }\n        this.buildDeferrable(node);\n    }\n    visitList(node) {\n        this.compileList(node.items);\n    }\n    visitWith(node) {\n        this.append('with ');\n        if (node.recursive) {\n            this.append('recursive ');\n        }\n        this.compileList(node.expressions);\n    }\n    visitCommonTableExpression(node) {\n        this.visitNode(node.name);\n        this.append(' as ');\n        if (isBoolean(node.materialized)) {\n            if (!node.materialized) {\n                this.append('not ');\n            }\n            this.append('materialized ');\n        }\n        this.visitNode(node.expression);\n    }\n    visitCommonTableExpressionName(node) {\n        this.visitNode(node.table);\n        if (node.columns) {\n            this.append('(');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n    }\n    visitAlterTable(node) {\n        this.append('alter table ');\n        this.visitNode(node.table);\n        this.append(' ');\n        if (node.renameTo) {\n            this.append('rename to ');\n            this.visitNode(node.renameTo);\n        }\n        if (node.setSchema) {\n            this.append('set schema ');\n            this.visitNode(node.setSchema);\n        }\n        if (node.addConstraint) {\n            this.visitNode(node.addConstraint);\n        }\n        if (node.dropConstraint) {\n            this.visitNode(node.dropConstraint);\n        }\n        if (node.renameConstraint) {\n            this.visitNode(node.renameConstraint);\n        }\n        if (node.columnAlterations) {\n            this.compileColumnAlterations(node.columnAlterations);\n        }\n        if (node.addIndex) {\n            this.visitNode(node.addIndex);\n        }\n        if (node.dropIndex) {\n            this.visitNode(node.dropIndex);\n        }\n    }\n    visitAddColumn(node) {\n        this.append('add column ');\n        this.visitNode(node.column);\n    }\n    visitRenameColumn(node) {\n        this.append('rename column ');\n        this.visitNode(node.column);\n        this.append(' to ');\n        this.visitNode(node.renameTo);\n    }\n    visitDropColumn(node) {\n        this.append('drop column ');\n        this.visitNode(node.column);\n    }\n    visitAlterColumn(node) {\n        this.append('alter column ');\n        this.visitNode(node.column);\n        this.append(' ');\n        if (node.dataType) {\n            if (this.announcesNewColumnDataType()) {\n                this.append('type ');\n            }\n            this.visitNode(node.dataType);\n            if (node.dataTypeExpression) {\n                this.append('using ');\n                this.visitNode(node.dataTypeExpression);\n            }\n        }\n        if (node.setDefault) {\n            this.append('set default ');\n            this.visitNode(node.setDefault);\n        }\n        if (node.dropDefault) {\n            this.append('drop default');\n        }\n        if (node.setNotNull) {\n            this.append('set not null');\n        }\n        if (node.dropNotNull) {\n            this.append('drop not null');\n        }\n    }\n    visitModifyColumn(node) {\n        this.append('modify column ');\n        this.visitNode(node.column);\n    }\n    visitAddConstraint(node) {\n        this.append('add ');\n        this.visitNode(node.constraint);\n    }\n    visitDropConstraint(node) {\n        this.append('drop constraint ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.constraintName);\n        if (node.modifier === 'cascade') {\n            this.append(' cascade');\n        }\n        else if (node.modifier === 'restrict') {\n            this.append(' restrict');\n        }\n    }\n    visitRenameConstraint(node) {\n        this.append('rename constraint ');\n        this.visitNode(node.oldName);\n        this.append(' to ');\n        this.visitNode(node.newName);\n    }\n    visitSetOperation(node) {\n        this.append(node.operator);\n        this.append(' ');\n        if (node.all) {\n            this.append('all ');\n        }\n        this.visitNode(node.expression);\n    }\n    visitCreateView(node) {\n        this.append('create ');\n        if (node.orReplace) {\n            this.append('or replace ');\n        }\n        if (node.materialized) {\n            this.append('materialized ');\n        }\n        if (node.temporary) {\n            this.append('temporary ');\n        }\n        this.append('view ');\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.name);\n        this.append(' ');\n        if (node.columns) {\n            this.append('(');\n            this.compileList(node.columns);\n            this.append(') ');\n        }\n        if (node.as) {\n            this.append('as ');\n            this.visitNode(node.as);\n        }\n    }\n    visitRefreshMaterializedView(node) {\n        this.append('refresh materialized view ');\n        if (node.concurrently) {\n            this.append('concurrently ');\n        }\n        this.visitNode(node.name);\n        if (node.withNoData) {\n            this.append(' with no data');\n        }\n        else {\n            this.append(' with data');\n        }\n    }\n    visitDropView(node) {\n        this.append('drop ');\n        if (node.materialized) {\n            this.append('materialized ');\n        }\n        this.append('view ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.name);\n        if (node.cascade) {\n            this.append(' cascade');\n        }\n    }\n    visitGenerated(node) {\n        this.append('generated ');\n        if (node.always) {\n            this.append('always ');\n        }\n        if (node.byDefault) {\n            this.append('by default ');\n        }\n        this.append('as ');\n        if (node.identity) {\n            this.append('identity');\n        }\n        if (node.expression) {\n            this.append('(');\n            this.visitNode(node.expression);\n            this.append(')');\n        }\n        if (node.stored) {\n            this.append(' stored');\n        }\n    }\n    visitDefaultValue(node) {\n        this.append('default ');\n        this.visitNode(node.defaultValue);\n    }\n    visitSelectModifier(node) {\n        if (node.rawModifier) {\n            this.visitNode(node.rawModifier);\n        }\n        else {\n            this.append(SELECT_MODIFIER_SQL[node.modifier]);\n        }\n        if (node.of) {\n            this.append(' of ');\n            this.compileList(node.of, ', ');\n        }\n    }\n    visitCreateType(node) {\n        this.append('create type ');\n        this.visitNode(node.name);\n        if (node.enum) {\n            this.append(' as enum ');\n            this.visitNode(node.enum);\n        }\n    }\n    visitDropType(node) {\n        this.append('drop type ');\n        if (node.ifExists) {\n            this.append('if exists ');\n        }\n        this.visitNode(node.name);\n    }\n    visitExplain(node) {\n        this.append('explain');\n        if (node.options || node.format) {\n            this.append(' ');\n            this.append(this.getLeftExplainOptionsWrapper());\n            if (node.options) {\n                this.visitNode(node.options);\n                if (node.format) {\n                    this.append(this.getExplainOptionsDelimiter());\n                }\n            }\n            if (node.format) {\n                this.append('format');\n                this.append(this.getExplainOptionAssignment());\n                this.append(node.format);\n            }\n            this.append(this.getRightExplainOptionsWrapper());\n        }\n    }\n    visitDefaultInsertValue(_) {\n        this.append('default');\n    }\n    visitAggregateFunction(node) {\n        this.append(node.func);\n        this.append('(');\n        if (node.distinct) {\n            this.append('distinct ');\n        }\n        this.compileList(node.aggregated);\n        if (node.orderBy) {\n            this.append(' ');\n            this.visitNode(node.orderBy);\n        }\n        this.append(')');\n        if (node.withinGroup) {\n            this.append(' within group (');\n            this.visitNode(node.withinGroup);\n            this.append(')');\n        }\n        if (node.filter) {\n            this.append(' filter(');\n            this.visitNode(node.filter);\n            this.append(')');\n        }\n        if (node.over) {\n            this.append(' ');\n            this.visitNode(node.over);\n        }\n    }\n    visitOver(node) {\n        this.append('over(');\n        if (node.partitionBy) {\n            this.visitNode(node.partitionBy);\n            if (node.orderBy) {\n                this.append(' ');\n            }\n        }\n        if (node.orderBy) {\n            this.visitNode(node.orderBy);\n        }\n        this.append(')');\n    }\n    visitPartitionBy(node) {\n        this.append('partition by ');\n        this.compileList(node.items);\n    }\n    visitPartitionByItem(node) {\n        this.visitNode(node.partitionBy);\n    }\n    visitBinaryOperation(node) {\n        this.visitNode(node.leftOperand);\n        this.append(' ');\n        this.visitNode(node.operator);\n        this.append(' ');\n        this.visitNode(node.rightOperand);\n    }\n    visitUnaryOperation(node) {\n        this.visitNode(node.operator);\n        if (!this.isMinusOperator(node.operator)) {\n            this.append(' ');\n        }\n        this.visitNode(node.operand);\n    }\n    isMinusOperator(node) {\n        return OperatorNode.is(node) && node.operator === '-';\n    }\n    visitUsing(node) {\n        this.append('using ');\n        this.compileList(node.tables);\n    }\n    visitFunction(node) {\n        this.append(node.func);\n        this.append('(');\n        this.compileList(node.arguments);\n        this.append(')');\n    }\n    visitCase(node) {\n        this.append('case');\n        if (node.value) {\n            this.append(' ');\n            this.visitNode(node.value);\n        }\n        if (node.when) {\n            this.append(' ');\n            this.compileList(node.when, ' ');\n        }\n        if (node.else) {\n            this.append(' else ');\n            this.visitNode(node.else);\n        }\n        this.append(' end');\n        if (node.isStatement) {\n            this.append(' case');\n        }\n    }\n    visitWhen(node) {\n        this.append('when ');\n        this.visitNode(node.condition);\n        if (node.result) {\n            this.append(' then ');\n            this.visitNode(node.result);\n        }\n    }\n    visitJSONReference(node) {\n        this.visitNode(node.reference);\n        this.visitNode(node.traversal);\n    }\n    visitJSONPath(node) {\n        if (node.inOperator) {\n            this.visitNode(node.inOperator);\n        }\n        this.append(\"'$\");\n        for (const pathLeg of node.pathLegs) {\n            this.visitNode(pathLeg);\n        }\n        this.append(\"'\");\n    }\n    visitJSONPathLeg(node) {\n        const isArrayLocation = node.type === 'ArrayLocation';\n        this.append(isArrayLocation ? '[' : '.');\n        this.append(String(node.value));\n        if (isArrayLocation) {\n            this.append(']');\n        }\n    }\n    visitJSONOperatorChain(node) {\n        for (let i = 0, len = node.values.length; i < len; i++) {\n            if (i === len - 1) {\n                this.visitNode(node.operator);\n            }\n            else {\n                this.append('->');\n            }\n            this.visitNode(node.values[i]);\n        }\n    }\n    visitMergeQuery(node) {\n        if (node.with) {\n            this.visitNode(node.with);\n            this.append(' ');\n        }\n        this.append('merge ');\n        if (node.top) {\n            this.visitNode(node.top);\n            this.append(' ');\n        }\n        this.append('into ');\n        this.visitNode(node.into);\n        if (node.using) {\n            this.append(' ');\n            this.visitNode(node.using);\n        }\n        if (node.whens) {\n            this.append(' ');\n            this.compileList(node.whens, ' ');\n        }\n        if (node.returning) {\n            this.append(' ');\n            this.visitNode(node.returning);\n        }\n        if (node.output) {\n            this.append(' ');\n            this.visitNode(node.output);\n        }\n        if (node.endModifiers?.length) {\n            this.append(' ');\n            this.compileList(node.endModifiers, ' ');\n        }\n    }\n    visitMatched(node) {\n        if (node.not) {\n            this.append('not ');\n        }\n        this.append('matched');\n        if (node.bySource) {\n            this.append(' by source');\n        }\n    }\n    visitAddIndex(node) {\n        this.append('add ');\n        if (node.unique) {\n            this.append('unique ');\n        }\n        this.append('index ');\n        this.visitNode(node.name);\n        if (node.columns) {\n            this.append(' (');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n        if (node.using) {\n            this.append(' using ');\n            this.visitNode(node.using);\n        }\n    }\n    visitCast(node) {\n        this.append('cast(');\n        this.visitNode(node.expression);\n        this.append(' as ');\n        this.visitNode(node.dataType);\n        this.append(')');\n    }\n    visitFetch(node) {\n        this.append('fetch next ');\n        this.visitNode(node.rowCount);\n        this.append(` rows ${node.modifier}`);\n    }\n    visitOutput(node) {\n        this.append('output ');\n        this.compileList(node.selections);\n    }\n    visitTop(node) {\n        this.append(`top(${node.expression})`);\n        if (node.modifiers) {\n            this.append(` ${node.modifiers}`);\n        }\n    }\n    visitOrAction(node) {\n        this.append(node.action);\n    }\n    visitCollate(node) {\n        this.append('collate ');\n        this.visitNode(node.collation);\n    }\n    append(str) {\n        this.#sql += str;\n    }\n    appendValue(parameter) {\n        this.addParameter(parameter);\n        this.append(this.getCurrentParameterPlaceholder());\n    }\n    getLeftIdentifierWrapper() {\n        return '\"';\n    }\n    getRightIdentifierWrapper() {\n        return '\"';\n    }\n    getCurrentParameterPlaceholder() {\n        return '$' + this.numParameters;\n    }\n    getLeftExplainOptionsWrapper() {\n        return '(';\n    }\n    getExplainOptionAssignment() {\n        return ' ';\n    }\n    getExplainOptionsDelimiter() {\n        return ', ';\n    }\n    getRightExplainOptionsWrapper() {\n        return ')';\n    }\n    sanitizeIdentifier(identifier) {\n        const leftWrap = this.getLeftIdentifierWrapper();\n        const rightWrap = this.getRightIdentifierWrapper();\n        let sanitized = '';\n        for (const c of identifier) {\n            sanitized += c;\n            if (c === leftWrap) {\n                sanitized += leftWrap;\n            }\n            else if (c === rightWrap) {\n                sanitized += rightWrap;\n            }\n        }\n        return sanitized;\n    }\n    sanitizeStringLiteral(value) {\n        return value.replace(LIT_WRAP_REGEX, \"''\");\n    }\n    addParameter(parameter) {\n        this.#parameters.push(parameter);\n    }\n    appendImmediateValue(value) {\n        if (isString(value)) {\n            this.appendStringLiteral(value);\n        }\n        else if (isNumber(value) || isBoolean(value)) {\n            this.append(value.toString());\n        }\n        else if (isNull(value)) {\n            this.append('null');\n        }\n        else if (isDate(value)) {\n            this.appendImmediateValue(value.toISOString());\n        }\n        else if (isBigInt(value)) {\n            this.appendImmediateValue(value.toString());\n        }\n        else {\n            throw new Error(`invalid immediate value ${value}`);\n        }\n    }\n    appendStringLiteral(value) {\n        this.append(\"'\");\n        this.append(this.sanitizeStringLiteral(value));\n        this.append(\"'\");\n    }\n    sortSelectModifiers(arr) {\n        arr.sort((left, right) => left.modifier && right.modifier\n            ? SELECT_MODIFIER_PRIORITY[left.modifier] -\n                SELECT_MODIFIER_PRIORITY[right.modifier]\n            : 1);\n        return freeze(arr);\n    }\n    compileColumnAlterations(columnAlterations) {\n        this.compileList(columnAlterations);\n    }\n    /**\n     * controls whether the dialect adds a \"type\" keyword before a column's new data\n     * type in an ALTER TABLE statement.\n     */\n    announcesNewColumnDataType() {\n        return true;\n    }\n}\nconst SELECT_MODIFIER_SQL = freeze({\n    ForKeyShare: 'for key share',\n    ForNoKeyUpdate: 'for no key update',\n    ForUpdate: 'for update',\n    ForShare: 'for share',\n    NoWait: 'nowait',\n    SkipLocked: 'skip locked',\n    Distinct: 'distinct',\n});\nconst SELECT_MODIFIER_PRIORITY = freeze({\n    ForKeyShare: 1,\n    ForNoKeyUpdate: 1,\n    ForUpdate: 1,\n    ForShare: 1,\n    NoWait: 2,\n    SkipLocked: 2,\n    Distinct: 0,\n});\nconst JOIN_TYPE_SQL = freeze({\n    InnerJoin: 'inner join',\n    LeftJoin: 'left join',\n    RightJoin: 'right join',\n    FullJoin: 'full join',\n    CrossJoin: 'cross join',\n    LateralInnerJoin: 'inner join lateral',\n    LateralLeftJoin: 'left join lateral',\n    LateralCrossJoin: 'cross join lateral',\n    OuterApply: 'outer apply',\n    CrossApply: 'cross apply',\n    Using: 'using',\n});\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACA,MAAM,iBAAiB;AAChB,MAAM,6BAA6B,gPAAA,CAAA,uBAAoB;IAC1D,CAAA,GAAI,GAAG,GAAG;IACV,CAAA,UAAW,GAAG,EAAE,CAAC;IACjB,IAAI,gBAAgB;QAChB,OAAO,IAAI,CAAC,CAAA,UAAW,CAAC,MAAM;IAClC;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,CAAA,GAAI,GAAG;QACZ,IAAI,CAAC,CAAA,UAAW,GAAG,EAAE;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;QAC9C,IAAI,CAAC,SAAS,CAAC;QACf,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACV,OAAO;YACP;YACA,KAAK,IAAI,CAAC,MAAM;YAChB,YAAY;mBAAI,IAAI,CAAC,CAAA,UAAW;aAAC;QACrC;IACJ;IACA,SAAS;QACL,OAAO,IAAI,CAAC,CAAA,GAAI;IACpB;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,eAAe,IAAI,CAAC,UAAU,KAAK,aACrC,CAAC,kOAAA,CAAA,aAAU,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,KAC9B,CAAC,2OAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,KACnC,CAAC,2OAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,KACnC,CAAC,0OAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,KAClC,CAAC,4OAAA,CAAA,mBAAgB,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU;QACxC,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,OAAO,EAAE;YAC/C,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;YAC3B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,cAAc;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,iBAAiB,CAAC,KAAK,UAAU;QAC1C;QACA,IAAI,KAAK,cAAc,EAAE,QAAQ;YAC7B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAE;QAC1C;QACA,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QAC3B;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU;QACpC;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;QACjC;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,aAAa,EAAE;QACzC;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,YAAY,EAAE,QAAQ;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC;mBAAI,KAAK,YAAY;aAAC,GAAG;QACvE;QACA,IAAI,cAAc;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAC/B;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;IACjC;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,kBAAkB,WAAW,EAAE;QAC3B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,YAAY,KAAK,EAAE,YAAY,IAAI,EAAE;QACjC,MAAM,YAAY,MAAM,MAAM,GAAG;QACjC,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,IAAK;YACjC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YACvB,IAAI,IAAI,WAAW;gBACf,IAAI,CAAC,MAAM,CAAC;YAChB;QACJ;IACJ;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iOAAA,CAAA,YAAS,CAAC,EAAE;QACtD,MAAM,aAAa,kBAAkB;QACrC,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;YAC3B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,cAAc,CAAC,0OAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,gBAAgB;YACjD,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,YAAY;QACvC,wBAAwB;QACxB,IAAI,KAAK,MAAM,EAAE;YACb,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE;YACR,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAChC;QACA,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QAC3B;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC;QACA,IAAI,KAAK,cAAc,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,cAAc;QACtC;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,cAAc,CAAC,0OAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,gBAAgB;YACjD,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,YAAY,EAAE,QAAQ;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;QACxC;IACJ;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM;IAChC;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iOAAA,CAAA,YAAS,CAAC,EAAE,MAAM;QACzD,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;YAC3B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,YAAY;YACZ,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;QACjC;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,YAAY;YACZ,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,YAAY,EAAE,QAAQ;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;QACxC;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU;IACpC;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;YACzB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,eAAe,CAAC,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB;QACzC,IAAI,CAAC,0BAA0B,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB;IAC9C;IACA,2BAA2B,IAAI,EAAE;QAC7B,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,GAAG;YACtB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,IAAI;IACjD;IACA,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,QAAQ,IAAI,EAAE;QACV,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,WAAW,IAAI,EAAE;QACb,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,oBAAoB,CAAC,KAAK,KAAK;QACxC,OACK;YACD,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;QAC/B;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,wBAAwB,IAAI,EAAE;QAC1B,IAAI,CAAC,MAAM,CAAC;QACZ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,EAAG;YACpC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;YAC1B,IAAI,MAAM,OAAO,MAAM,GAAG,GAAG;gBACzB,IAAI,CAAC,MAAM,CAAC;YAChB;QACJ;QACA,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,KAAK,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QAC1B;IACJ;IACA,QAAQ,IAAI,EAAE;QACV,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;IAC1B;IACA,SAAS,IAAI,EAAE;QACX,MAAM,EAAE,YAAY,EAAE,YAAY,MAAM,EAAE,GAAG;QAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,EAAE,EAAG;YAC1C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YAC3B,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YAC5B;QACJ;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;IAC7B;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,yBAAyB,IAAI,EAAE;QAC3B,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;YAC1B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;IAClC;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,MAAM,GAAG,GAAG;YACvD,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;QACnC,OACK;YACD,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC;mBAAI,KAAK,OAAO;mBAAM,KAAK,WAAW,IAAI,EAAE;aAAE;YAC/D,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,KAAK,QAAQ,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;YAC7B;YACA,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG;gBACnD,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;YACxC;QACJ;IACJ;IACA,sBAAsB,IAAI,EAAE;QACxB,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC5B,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,MAAM,GAAG,GAAG;YACvD,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAE;QAC1C;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;QACrC;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG;YACnD,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;QACxC;IACJ;IACA,mBAAmB;QACf,OAAO;IACX;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC7B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;QAC7B;QACA,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;QAC7B;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;IAC7B;IACA,aAAa,IAAI,EAAE;QACf,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAC/B;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC3B,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK;QAC1B;IACJ;IACA,aAAa,IAAI,EAAE;QACf,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAC/B;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;IAC/B;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iOAAA,CAAA,YAAS,CAAC,EAAE;QACtD,MAAM,aAAa,kBAAkB;QACrC,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;YAC3B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,cAAc,CAAC,0OAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,gBAAgB;YACjD,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;YACzB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QACjC;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,IAAI,EAAE;gBACZ,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;QACjC;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,cAAc,CAAC,0OAAA,CAAA,iBAAc,CAAC,EAAE,CAAC,gBAAgB;YACjD,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,YAAY,EAAE,QAAQ;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;QACxC;IACJ;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;IAC7B;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB,OACK,IAAI,KAAK,UAAU,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC,OACK,IAAI,KAAK,eAAe,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,eAAe;YACnC,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC;QACA,IAAI,KAAK,SAAS,KAAK,MAAM;YACzB,IAAI,CAAC,MAAM,CAAC;QAChB,OACK,IAAI,KAAK,OAAO,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,KAAK,WAAW,EAAE;gBAClB,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;YACnC;QACJ;IACJ;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;IACjC;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,0BAA0B,IAAI,EAAE;QAC5B,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC7B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,eAAe,CAAC;IACzB;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,KAAK,UAAU,KAAK,WAAW;YAC/B,IAAI,KAAK,UAAU,EAAE;gBACjB,IAAI,CAAC,MAAM,CAAC;YAChB,OACK;gBACD,IAAI,CAAC,MAAM,CAAC;YAChB;QACJ;QACA,IAAI,KAAK,iBAAiB,KAAK,WAAW;YACtC,IAAI,KAAK,iBAAiB,EAAE;gBACxB,IAAI,CAAC,MAAM,CAAC;YAChB,OACK;gBACD,IAAI,CAAC,MAAM,CAAC;YAChB;QACJ;IACJ;IACA,sBAAsB,IAAI,EAAE;QACxB,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC7B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,eAAe,CAAC;IACzB;IACA,qBAAqB,IAAI,EAAE;QACvB,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAC9B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,0BAA0B,IAAI,EAAE;QAC5B,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;QAC7B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAC9B,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;QAC7B;QACA,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;QAC7B;QACA,IAAI,CAAC,eAAe,CAAC;IACzB;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAC/B;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,WAAW,CAAC,KAAK,WAAW;IACrC;IACA,2BAA2B,IAAI,EAAE;QAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,KAAK,YAAY,GAAG;YAC9B,IAAI,CAAC,KAAK,YAAY,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC;YAChB;YACA,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;IAClC;IACA,+BAA+B,IAAI,EAAE;QACjC,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QACzB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAChC;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,aAAa,EAAE;YACpB,IAAI,CAAC,SAAS,CAAC,KAAK,aAAa;QACrC;QACA,IAAI,KAAK,cAAc,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,KAAK,cAAc;QACtC;QACA,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,KAAK,gBAAgB;QACxC;QACA,IAAI,KAAK,iBAAiB,EAAE;YACxB,IAAI,CAAC,wBAAwB,CAAC,KAAK,iBAAiB;QACxD;QACA,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAChC;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;IAChC;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,IAAI,CAAC,0BAA0B,IAAI;gBACnC,IAAI,CAAC,MAAM,CAAC;YAChB;YACA,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;YAC5B,IAAI,KAAK,kBAAkB,EAAE;gBACzB,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,SAAS,CAAC,KAAK,kBAAkB;YAC1C;QACJ;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC;QACA,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;IAClC;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,cAAc;QAClC,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB,OACK,IAAI,KAAK,QAAQ,KAAK,YAAY;YACnC,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,sBAAsB,IAAI,EAAE;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC3B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;IAC/B;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ;QACzB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;IAClC;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,YAAY,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QAC1B;IACJ;IACA,6BAA6B,IAAI,EAAE;QAC/B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,YAAY,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;QAChB,OACK;YACD,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,YAAY,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;YAC9B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,kBAAkB,IAAI,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,YAAY;IACpC;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;QACnC,OACK;YACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,QAAQ,CAAC;QAClD;QACA,IAAI,KAAK,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE;QAC9B;IACJ;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;IAC5B;IACA,aAAa,IAAI,EAAE;QACf,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B;YAC7C,IAAI,KAAK,OAAO,EAAE;gBACd,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;gBAC3B,IAAI,KAAK,MAAM,EAAE;oBACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;gBAC/C;YACJ;YACA,IAAI,KAAK,MAAM,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;gBAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;YAC3B;YACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B;QAClD;IACJ;IACA,wBAAwB,CAAC,EAAE;QACvB,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,uBAAuB,IAAI,EAAE;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;QACrB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU;QAChC,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;YAC/B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;YAC1B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;YAC/B,IAAI,KAAK,OAAO,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC;YAChB;QACJ;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;QAC/B;QACA,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK;IAC/B;IACA,qBAAqB,IAAI,EAAE;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;IACnC;IACA,qBAAqB,IAAI,EAAE;QACvB,IAAI,CAAC,SAAS,CAAC,KAAK,WAAW;QAC/B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC5B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,YAAY;IACpC;IACA,oBAAoB,IAAI,EAAE;QACtB,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ,GAAG;YACtC,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO;IAC/B;IACA,gBAAgB,IAAI,EAAE;QAClB,OAAO,oOAAA,CAAA,eAAY,CAAC,EAAE,CAAC,SAAS,KAAK,QAAQ,KAAK;IACtD;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM;IAChC;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;QACrB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,SAAS;QAC/B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;QAChC;QACA,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QAC5B;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QAC7B,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;IACJ;IACA,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;IACjC;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,KAAK,UAAU,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAClC;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,KAAK,MAAM,WAAW,KAAK,QAAQ,CAAE;YACjC,IAAI,CAAC,SAAS,CAAC;QACnB;QACA,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,kBAAkB,KAAK,IAAI,KAAK;QACtC,IAAI,CAAC,MAAM,CAAC,kBAAkB,MAAM;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,KAAK;QAC7B,IAAI,iBAAiB;YACjB,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,uBAAuB,IAAI,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YACpD,IAAI,MAAM,MAAM,GAAG;gBACf,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;YAChC,OACK;gBACD,IAAI,CAAC,MAAM,CAAC;YAChB;YACA,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM,CAAC,EAAE;QACjC;IACJ;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,KAAK,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;YACxB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;YACvB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,EAAE;QACjC;QACA,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;QACjC;QACA,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC9B;QACA,IAAI,KAAK,YAAY,EAAE,QAAQ;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;QACxC;IACJ;IACA,aAAa,IAAI,EAAE;QACf,IAAI,KAAK,GAAG,EAAE;YACV,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,QAAQ,EAAE;YACf,IAAI,CAAC,MAAM,CAAC;QAChB;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,UAAU;QAC9B,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC5B,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ;QAC5B,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,QAAQ,EAAE;IACxC;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK,UAAU;IACpC;IACA,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,UAAU,CAAC,CAAC,CAAC;QACrC,IAAI,KAAK,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;QACpC;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;IAC3B;IACA,aAAa,IAAI,EAAE;QACf,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,SAAS,CAAC,KAAK,SAAS;IACjC;IACA,OAAO,GAAG,EAAE;QACR,IAAI,CAAC,CAAA,GAAI,IAAI;IACjB;IACA,YAAY,SAAS,EAAE;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8BAA8B;IACnD;IACA,2BAA2B;QACvB,OAAO;IACX;IACA,4BAA4B;QACxB,OAAO;IACX;IACA,iCAAiC;QAC7B,OAAO,MAAM,IAAI,CAAC,aAAa;IACnC;IACA,+BAA+B;QAC3B,OAAO;IACX;IACA,6BAA6B;QACzB,OAAO;IACX;IACA,6BAA6B;QACzB,OAAO;IACX;IACA,gCAAgC;QAC5B,OAAO;IACX;IACA,mBAAmB,UAAU,EAAE;QAC3B,MAAM,WAAW,IAAI,CAAC,wBAAwB;QAC9C,MAAM,YAAY,IAAI,CAAC,yBAAyB;QAChD,IAAI,YAAY;QAChB,KAAK,MAAM,KAAK,WAAY;YACxB,aAAa;YACb,IAAI,MAAM,UAAU;gBAChB,aAAa;YACjB,OACK,IAAI,MAAM,WAAW;gBACtB,aAAa;YACjB;QACJ;QACA,OAAO;IACX;IACA,sBAAsB,KAAK,EAAE;QACzB,OAAO,MAAM,OAAO,CAAC,gBAAgB;IACzC;IACA,aAAa,SAAS,EAAE;QACpB,IAAI,CAAC,CAAA,UAAW,CAAC,IAAI,CAAC;IAC1B;IACA,qBAAqB,KAAK,EAAE;QACxB,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACjB,IAAI,CAAC,mBAAmB,CAAC;QAC7B,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,QAAQ;QAC9B,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACpB,IAAI,CAAC,MAAM,CAAC;QAChB,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACpB,IAAI,CAAC,oBAAoB,CAAC,MAAM,WAAW;QAC/C,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACtB,IAAI,CAAC,oBAAoB,CAAC,MAAM,QAAQ;QAC5C,OACK;YACD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO;QACtD;IACJ;IACA,oBAAoB,KAAK,EAAE;QACvB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,IAAI,CAAC,CAAC,MAAM,QAAU,KAAK,QAAQ,IAAI,MAAM,QAAQ,GACnD,wBAAwB,CAAC,KAAK,QAAQ,CAAC,GACrC,wBAAwB,CAAC,MAAM,QAAQ,CAAC,GAC1C;QACN,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAClB;IACA,yBAAyB,iBAAiB,EAAE;QACxC,IAAI,CAAC,WAAW,CAAC;IACrB;IACA;;;KAGC,GACD,6BAA6B;QACzB,OAAO;IACX;AACJ;AACA,MAAM,sBAAsB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,aAAa;IACb,gBAAgB;IAChB,WAAW;IACX,UAAU;IACV,QAAQ;IACR,YAAY;IACZ,UAAU;AACd;AACA,MAAM,2BAA2B,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACpC,aAAa;IACb,gBAAgB;IAChB,WAAW;IACX,UAAU;IACV,QAAQ;IACR,YAAY;IACZ,UAAU;AACd;AACA,MAAM,gBAAgB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB,WAAW;IACX,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,kBAAkB;IAClB,iBAAiB;IACjB,kBAAkB;IAClB,YAAY;IACZ,YAAY;IACZ,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4747, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/sqlite/sqlite-driver.js"], "sourcesContent": ["/// <reference types=\"./sqlite-driver.d.ts\" />\nimport { SelectQueryNode } from '../../operation-node/select-query-node.js';\nimport { parseSavepointCommand } from '../../parser/savepoint-parser.js';\nimport { CompiledQuery } from '../../query-compiler/compiled-query.js';\nimport { freeze, isFunction } from '../../util/object-utils.js';\nimport { createQueryId } from '../../util/query-id.js';\nexport class SqliteDriver {\n    #config;\n    #connectionMutex = new ConnectionMutex();\n    #db;\n    #connection;\n    constructor(config) {\n        this.#config = freeze({ ...config });\n    }\n    async init() {\n        this.#db = isFunction(this.#config.database)\n            ? await this.#config.database()\n            : this.#config.database;\n        this.#connection = new SqliteConnection(this.#db);\n        if (this.#config.onCreateConnection) {\n            await this.#config.onCreateConnection(this.#connection);\n        }\n    }\n    async acquireConnection() {\n        // SQLite only has one single connection. We use a mutex here to wait\n        // until the single connection has been released.\n        await this.#connectionMutex.lock();\n        return this.#connection;\n    }\n    async beginTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('begin'));\n    }\n    async commitTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('commit'));\n    }\n    async rollbackTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('rollback'));\n    }\n    async savepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('savepoint', savepointName), createQueryId()));\n    }\n    async rollbackToSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('rollback to', savepointName), createQueryId()));\n    }\n    async releaseSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('release', savepointName), createQueryId()));\n    }\n    async releaseConnection() {\n        this.#connectionMutex.unlock();\n    }\n    async destroy() {\n        this.#db?.close();\n    }\n}\nclass SqliteConnection {\n    #db;\n    constructor(db) {\n        this.#db = db;\n    }\n    executeQuery(compiledQuery) {\n        const { sql, parameters } = compiledQuery;\n        const stmt = this.#db.prepare(sql);\n        if (stmt.reader) {\n            return Promise.resolve({\n                rows: stmt.all(parameters),\n            });\n        }\n        const { changes, lastInsertRowid } = stmt.run(parameters);\n        return Promise.resolve({\n            numAffectedRows: changes !== undefined && changes !== null ? BigInt(changes) : undefined,\n            insertId: lastInsertRowid !== undefined && lastInsertRowid !== null\n                ? BigInt(lastInsertRowid)\n                : undefined,\n            rows: [],\n        });\n    }\n    async *streamQuery(compiledQuery, _chunkSize) {\n        const { sql, parameters, query } = compiledQuery;\n        const stmt = this.#db.prepare(sql);\n        if (SelectQueryNode.is(query)) {\n            const iter = stmt.iterate(parameters);\n            for (const row of iter) {\n                yield {\n                    rows: [row],\n                };\n            }\n        }\n        else {\n            throw new Error('Sqlite driver only supports streaming of select queries');\n        }\n    }\n}\nclass ConnectionMutex {\n    #promise;\n    #resolve;\n    async lock() {\n        while (this.#promise) {\n            await this.#promise;\n        }\n        this.#promise = new Promise((resolve) => {\n            this.#resolve = resolve;\n        });\n    }\n    unlock() {\n        const resolve = this.#resolve;\n        this.#promise = undefined;\n        this.#resolve = undefined;\n        resolve?.();\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,eAAgB,GAAG,IAAI,kBAAkB;IACzC,CAAA,EAAG,CAAC;IACJ,CAAA,UAAW,CAAC;IACZ,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YAAE,GAAG,MAAM;QAAC;IACtC;IACA,MAAM,OAAO;QACT,IAAI,CAAC,CAAA,EAAG,GAAG,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,CAAA,MAAO,CAAC,QAAQ,IACrC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,QAAQ,KAC3B,IAAI,CAAC,CAAA,MAAO,CAAC,QAAQ;QAC3B,IAAI,CAAC,CAAA,UAAW,GAAG,IAAI,iBAAiB,IAAI,CAAC,CAAA,EAAG;QAChD,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,EAAE;YACjC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,UAAW;QAC1D;IACJ;IACA,MAAM,oBAAoB;QACtB,qEAAqE;QACrE,iDAAiD;QACjD,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI;QAChC,OAAO,IAAI,CAAC,CAAA,UAAW;IAC3B;IACA,MAAM,iBAAiB,UAAU,EAAE;QAC/B,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,oBAAoB,UAAU,EAAE;QAClC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,UAAU,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QACrD,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAC9G;IACA,MAAM,oBAAoB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC/D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAChH;IACA,MAAM,iBAAiB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC5D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAC5G;IACA,MAAM,oBAAoB;QACtB,IAAI,CAAC,CAAA,eAAgB,CAAC,MAAM;IAChC;IACA,MAAM,UAAU;QACZ,IAAI,CAAC,CAAA,EAAG,EAAE;IACd;AACJ;AACA,MAAM;IACF,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA,aAAa,aAAa,EAAE;QACxB,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG;QAC5B,MAAM,OAAO,IAAI,CAAC,CAAA,EAAG,CAAC,OAAO,CAAC;QAC9B,IAAI,KAAK,MAAM,EAAE;YACb,OAAO,QAAQ,OAAO,CAAC;gBACnB,MAAM,KAAK,GAAG,CAAC;YACnB;QACJ;QACA,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,KAAK,GAAG,CAAC;QAC9C,OAAO,QAAQ,OAAO,CAAC;YACnB,iBAAiB,YAAY,aAAa,YAAY,OAAO,OAAO,WAAW;YAC/E,UAAU,oBAAoB,aAAa,oBAAoB,OACzD,OAAO,mBACP;YACN,MAAM,EAAE;QACZ;IACJ;IACA,OAAO,YAAY,aAAa,EAAE,UAAU,EAAE;QAC1C,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG;QACnC,MAAM,OAAO,IAAI,CAAC,CAAA,EAAG,CAAC,OAAO,CAAC;QAC9B,IAAI,2OAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,QAAQ;YAC3B,MAAM,OAAO,KAAK,OAAO,CAAC;YAC1B,KAAK,MAAM,OAAO,KAAM;gBACpB,MAAM;oBACF,MAAM;wBAAC;qBAAI;gBACf;YACJ;QACJ,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ;AACA,MAAM;IACF,CAAA,OAAQ,CAAC;IACT,CAAA,OAAQ,CAAC;IACT,MAAM,OAAO;QACT,MAAO,IAAI,CAAC,CAAA,OAAQ,CAAE;YAClB,MAAM,IAAI,CAAC,CAAA,OAAQ;QACvB;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG,IAAI,QAAQ,CAAC;YACzB,IAAI,CAAC,CAAA,OAAQ,GAAG;QACpB;IACJ;IACA,SAAS;QACL,MAAM,UAAU,IAAI,CAAC,CAAA,OAAQ;QAC7B,IAAI,CAAC,CAAA,OAAQ,GAAG;QAChB,IAAI,CAAC,CAAA,OAAQ,GAAG;QAChB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4870, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/sqlite/sqlite-query-compiler.js"], "sourcesContent": ["/// <reference types=\"./sqlite-query-compiler.d.ts\" />\nimport { DefaultQueryCompiler } from '../../query-compiler/default-query-compiler.js';\nconst ID_WRAP_REGEX = /\"/g;\nexport class SqliteQueryCompiler extends DefaultQueryCompiler {\n    visitOrAction(node) {\n        this.append('or ');\n        this.append(node.action);\n    }\n    getCurrentParameterPlaceholder() {\n        return '?';\n    }\n    getLeftExplainOptionsWrapper() {\n        return '';\n    }\n    getRightExplainOptionsWrapper() {\n        return '';\n    }\n    getLeftIdentifierWrapper() {\n        return '\"';\n    }\n    getRightIdentifierWrapper() {\n        return '\"';\n    }\n    getAutoIncrement() {\n        return 'autoincrement';\n    }\n    sanitizeIdentifier(identifier) {\n        return identifier.replace(ID_WRAP_REGEX, '\"\"');\n    }\n    visitDefaultInsertValue(_) {\n        // sqlite doesn't support the `default` keyword in inserts.\n        this.append('null');\n    }\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD;;AACA,MAAM,gBAAgB;AACf,MAAM,4BAA4B,gPAAA,CAAA,uBAAoB;IACzD,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,CAAC,MAAM,CAAC,KAAK,MAAM;IAC3B;IACA,iCAAiC;QAC7B,OAAO;IACX;IACA,+BAA+B;QAC3B,OAAO;IACX;IACA,gCAAgC;QAC5B,OAAO;IACX;IACA,2BAA2B;QACvB,OAAO;IACX;IACA,4BAA4B;QACxB,OAAO;IACX;IACA,mBAAmB;QACf,OAAO;IACX;IACA,mBAAmB,UAAU,EAAE;QAC3B,OAAO,WAAW,OAAO,CAAC,eAAe;IAC7C;IACA,wBAAwB,CAAC,EAAE;QACvB,2DAA2D;QAC3D,IAAI,CAAC,MAAM,CAAC;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/sqlite/sqlite-introspector.js"], "sourcesContent": ["/// <reference types=\"./sqlite-introspector.d.ts\" />\nimport { DEFAULT_MIGRATION_LOCK_TABLE, DEFAULT_MIGRATION_TABLE, } from '../../migration/migrator.js';\nimport { sql } from '../../raw-builder/sql.js';\nexport class SqliteIntrospector {\n    #db;\n    constructor(db) {\n        this.#db = db;\n    }\n    async getSchemas() {\n        // Sqlite doesn't support schemas.\n        return [];\n    }\n    async getTables(options = { withInternalKyselyTables: false }) {\n        return await this.#getTableMetadata(options);\n    }\n    async getMetadata(options) {\n        return {\n            tables: await this.getTables(options),\n        };\n    }\n    #tablesQuery(qb, options) {\n        let tablesQuery = qb\n            .selectFrom('sqlite_master')\n            .where('type', 'in', ['table', 'view'])\n            .where('name', 'not like', 'sqlite_%')\n            .select(['name', 'sql', 'type'])\n            .orderBy('name');\n        if (!options.withInternalKyselyTables) {\n            tablesQuery = tablesQuery\n                .where('name', '!=', DEFAULT_MIGRATION_TABLE)\n                .where('name', '!=', DEFAULT_MIGRATION_LOCK_TABLE);\n        }\n        return tablesQuery;\n    }\n    async #getTableMetadata(options) {\n        const tablesResult = await this.#tablesQuery(this.#db, options).execute();\n        const tableMetadata = await this.#db\n            .with('table_list', (qb) => this.#tablesQuery(qb, options))\n            .selectFrom([\n            'table_list as tl',\n            sql `pragma_table_info(tl.name)`.as('p'),\n        ])\n            .select([\n            'tl.name as table',\n            'p.cid',\n            'p.name',\n            'p.type',\n            'p.notnull',\n            'p.dflt_value',\n            'p.pk',\n        ])\n            .orderBy('tl.name')\n            .orderBy('p.cid')\n            .execute();\n        const columnsByTable = {};\n        for (const row of tableMetadata) {\n            columnsByTable[row.table] ??= [];\n            columnsByTable[row.table].push(row);\n        }\n        return tablesResult.map(({ name, sql, type }) => {\n            // // Try to find the name of the column that has `autoincrement` 🤦\n            let autoIncrementCol = sql\n                ?.split(/[\\(\\),]/)\n                ?.find((it) => it.toLowerCase().includes('autoincrement'))\n                ?.trimStart()\n                ?.split(/\\s+/)?.[0]\n                ?.replace(/[\"`]/g, '');\n            const columns = columnsByTable[name] ?? [];\n            // Otherwise, check for an INTEGER PRIMARY KEY\n            // https://www.sqlite.org/autoinc.html\n            if (!autoIncrementCol) {\n                const pkCols = columns.filter((r) => r.pk > 0);\n                if (pkCols.length === 1 && pkCols[0].type.toLowerCase() === 'integer') {\n                    autoIncrementCol = pkCols[0].name;\n                }\n            }\n            return {\n                name: name,\n                isView: type === 'view',\n                columns: columns.map((col) => ({\n                    name: col.name,\n                    dataType: col.type,\n                    isNullable: !col.notnull,\n                    isAutoIncrementing: col.name === autoIncrementCol,\n                    hasDefaultValue: col.dflt_value != null,\n                    comment: undefined,\n                })),\n            };\n        });\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AACO,MAAM;IACT,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA,MAAM,aAAa;QACf,kCAAkC;QAClC,OAAO,EAAE;IACb;IACA,MAAM,UAAU,UAAU;QAAE,0BAA0B;IAAM,CAAC,EAAE;QAC3D,OAAO,MAAM,IAAI,CAAC,CAAA,gBAAiB,CAAC;IACxC;IACA,MAAM,YAAY,OAAO,EAAE;QACvB,OAAO;YACH,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;QACjC;IACJ;IACA,CAAA,WAAY,CAAC,EAAE,EAAE,OAAO;QACpB,IAAI,cAAc,GACb,UAAU,CAAC,iBACX,KAAK,CAAC,QAAQ,MAAM;YAAC;YAAS;SAAO,EACrC,KAAK,CAAC,QAAQ,YAAY,YAC1B,MAAM,CAAC;YAAC;YAAQ;YAAO;SAAO,EAC9B,OAAO,CAAC;QACb,IAAI,CAAC,QAAQ,wBAAwB,EAAE;YACnC,cAAc,YACT,KAAK,CAAC,QAAQ,MAAM,oNAAA,CAAA,0BAAuB,EAC3C,KAAK,CAAC,QAAQ,MAAM,oNAAA,CAAA,+BAA4B;QACzD;QACA,OAAO;IACX;IACA,MAAM,CAAA,gBAAiB,CAAC,OAAO;QAC3B,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,WAAY,CAAC,IAAI,CAAC,CAAA,EAAG,EAAE,SAAS,OAAO;QACvE,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAA,EAAG,CAC/B,IAAI,CAAC,cAAc,CAAC,KAAO,IAAI,CAAC,CAAA,WAAY,CAAC,IAAI,UACjD,UAAU,CAAC;YACZ;YACA,oNAAA,CAAA,MAAG,AAAC,CAAC,0BAA0B,CAAC,CAAC,EAAE,CAAC;SACvC,EACI,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACI,OAAO,CAAC,WACR,OAAO,CAAC,SACR,OAAO;QACZ,MAAM,iBAAiB,CAAC;QACxB,KAAK,MAAM,OAAO,cAAe;YAC7B,cAAc,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE;YAChC,cAAc,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;QACnC;QACA,OAAO,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE;YACxC,oEAAoE;YACpE,IAAI,mBAAmB,KACjB,MAAM,YACN,KAAK,CAAC,KAAO,GAAG,WAAW,GAAG,QAAQ,CAAC,mBACvC,aACA,MAAM,QAAQ,CAAC,EAAE,EACjB,QAAQ,SAAS;YACvB,MAAM,UAAU,cAAc,CAAC,KAAK,IAAI,EAAE;YAC1C,8CAA8C;YAC9C,sCAAsC;YACtC,IAAI,CAAC,kBAAkB;gBACnB,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,GAAG;gBAC5C,IAAI,OAAO,MAAM,KAAK,KAAK,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW;oBACnE,mBAAmB,MAAM,CAAC,EAAE,CAAC,IAAI;gBACrC;YACJ;YACA,OAAO;gBACH,MAAM;gBACN,QAAQ,SAAS;gBACjB,SAAS,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;wBAC3B,MAAM,IAAI,IAAI;wBACd,UAAU,IAAI,IAAI;wBAClB,YAAY,CAAC,IAAI,OAAO;wBACxB,oBAAoB,IAAI,IAAI,KAAK;wBACjC,iBAAiB,IAAI,UAAU,IAAI;wBACnC,SAAS;oBACb,CAAC;YACL;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5007, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/dialect-adapter-base.js"], "sourcesContent": ["/// <reference types=\"./dialect-adapter-base.d.ts\" />\n/**\n * A basic implementation of `DialectAdapter` with sensible default values.\n * Third-party dialects can extend this instead of implementing the `DialectAdapter`\n * interface from scratch. That way all new settings will get default values when\n * they are added and there will be less breaking changes.\n */\nexport class DialectAdapterBase {\n    get supportsCreateIfNotExists() {\n        return true;\n    }\n    get supportsTransactionalDdl() {\n        return false;\n    }\n    get supportsReturning() {\n        return false;\n    }\n    get supportsOutput() {\n        return false;\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD;;;;;CAKC;;;AACM,MAAM;IACT,IAAI,4BAA4B;QAC5B,OAAO;IACX;IACA,IAAI,2BAA2B;QAC3B,OAAO;IACX;IACA,IAAI,oBAAoB;QACpB,OAAO;IACX;IACA,IAAI,iBAAiB;QACjB,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/sqlite/sqlite-adapter.js"], "sourcesContent": ["/// <reference types=\"./sqlite-adapter.d.ts\" />\nimport { DialectAdapterBase } from '../dialect-adapter-base.js';\nexport class SqliteAdapter extends DialectAdapterBase {\n    get supportsTransactionalDdl() {\n        return false;\n    }\n    get supportsReturning() {\n        return true;\n    }\n    async acquireMigrationLock(_db, _opt) {\n        // SQLite only has one connection that's reserved by the migration system\n        // for the whole time between acquireMigrationLock and releaseMigrationLock.\n        // We don't need to do anything here.\n    }\n    async releaseMigrationLock(_db, _opt) {\n        // SQLite only has one connection that's reserved by the migration system\n        // for the whole time between acquireMigrationLock and releaseMigrationLock.\n        // We don't need to do anything here.\n    }\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;AAC/C;;AACO,MAAM,sBAAsB,oOAAA,CAAA,qBAAkB;IACjD,IAAI,2BAA2B;QAC3B,OAAO;IACX;IACA,IAAI,oBAAoB;QACpB,OAAO;IACX;IACA,MAAM,qBAAqB,GAAG,EAAE,IAAI,EAAE;IAClC,yEAAyE;IACzE,4EAA4E;IAC5E,qCAAqC;IACzC;IACA,MAAM,qBAAqB,GAAG,EAAE,IAAI,EAAE;IAClC,yEAAyE;IACzE,4EAA4E;IAC5E,qCAAqC;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5066, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/sqlite/sqlite-dialect.js"], "sourcesContent": ["/// <reference types=\"./sqlite-dialect.d.ts\" />\nimport { SqliteDriver } from './sqlite-driver.js';\nimport { SqliteQueryCompiler } from './sqlite-query-compiler.js';\nimport { SqliteIntrospector } from './sqlite-introspector.js';\nimport { SqliteAdapter } from './sqlite-adapter.js';\nimport { freeze } from '../../util/object-utils.js';\n/**\n * SQLite dialect that uses the [better-sqlite3](https://github.com/JoshuaWise/better-sqlite3) library.\n *\n * The constructor takes an instance of {@link SqliteDialectConfig}.\n *\n * ```ts\n * import Database from 'better-sqlite3'\n *\n * new SqliteDialect({\n *   database: new Database('db.sqlite')\n * })\n * ```\n *\n * If you want the pool to only be created once it's first used, `database`\n * can be a function:\n *\n * ```ts\n * import Database from 'better-sqlite3'\n *\n * new SqliteDialect({\n *   database: async () => new Database('db.sqlite')\n * })\n * ```\n */\nexport class SqliteDialect {\n    #config;\n    constructor(config) {\n        this.#config = freeze({ ...config });\n    }\n    createDriver() {\n        return new SqliteDriver(this.#config);\n    }\n    createQueryCompiler() {\n        return new SqliteQueryCompiler();\n    }\n    createAdapter() {\n        return new SqliteAdapter();\n    }\n    createIntrospector(db) {\n        return new SqliteIntrospector(db);\n    }\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;AAC/C;AACA;AACA;AACA;AACA;;;;;;AAyBO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YAAE,GAAG,MAAM;QAAC;IACtC;IACA,eAAe;QACX,OAAO,IAAI,oOAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,MAAO;IACxC;IACA,sBAAsB;QAClB,OAAO,IAAI,+OAAA,CAAA,sBAAmB;IAClC;IACA,gBAAgB;QACZ,OAAO,IAAI,qOAAA,CAAA,gBAAa;IAC5B;IACA,mBAAmB,EAAE,EAAE;QACnB,OAAO,IAAI,0OAAA,CAAA,qBAAkB,CAAC;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mysql/mysql-driver.js"], "sourcesContent": ["/// <reference types=\"./mysql-driver.d.ts\" />\nimport { parseSavepointCommand } from '../../parser/savepoint-parser.js';\nimport { CompiledQuery } from '../../query-compiler/compiled-query.js';\nimport { isFunction, isObject, freeze } from '../../util/object-utils.js';\nimport { createQueryId } from '../../util/query-id.js';\nimport { extendStackTrace } from '../../util/stack-trace-utils.js';\nconst PRIVATE_RELEASE_METHOD = Symbol();\nexport class MysqlDriver {\n    #config;\n    #connections = new WeakMap();\n    #pool;\n    constructor(configOrPool) {\n        this.#config = freeze({ ...configOrPool });\n    }\n    async init() {\n        this.#pool = isFunction(this.#config.pool)\n            ? await this.#config.pool()\n            : this.#config.pool;\n    }\n    async acquireConnection() {\n        const rawConnection = await this.#acquireConnection();\n        let connection = this.#connections.get(rawConnection);\n        if (!connection) {\n            connection = new MysqlConnection(rawConnection);\n            this.#connections.set(rawConnection, connection);\n            // The driver must take care of calling `onCreateConnection` when a new\n            // connection is created. The `mysql2` module doesn't provide an async hook\n            // for the connection creation. We need to call the method explicitly.\n            if (this.#config?.onCreateConnection) {\n                await this.#config.onCreateConnection(connection);\n            }\n        }\n        if (this.#config?.onReserveConnection) {\n            await this.#config.onReserveConnection(connection);\n        }\n        return connection;\n    }\n    async #acquireConnection() {\n        return new Promise((resolve, reject) => {\n            this.#pool.getConnection(async (err, rawConnection) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(rawConnection);\n                }\n            });\n        });\n    }\n    async beginTransaction(connection, settings) {\n        if (settings.isolationLevel || settings.accessMode) {\n            const parts = [];\n            if (settings.isolationLevel) {\n                parts.push(`isolation level ${settings.isolationLevel}`);\n            }\n            if (settings.accessMode) {\n                parts.push(settings.accessMode);\n            }\n            const sql = `set transaction ${parts.join(', ')}`;\n            // On MySQL this sets the isolation level of the next transaction.\n            await connection.executeQuery(CompiledQuery.raw(sql));\n        }\n        await connection.executeQuery(CompiledQuery.raw('begin'));\n    }\n    async commitTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('commit'));\n    }\n    async rollbackTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('rollback'));\n    }\n    async savepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('savepoint', savepointName), createQueryId()));\n    }\n    async rollbackToSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('rollback to', savepointName), createQueryId()));\n    }\n    async releaseSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('release savepoint', savepointName), createQueryId()));\n    }\n    async releaseConnection(connection) {\n        connection[PRIVATE_RELEASE_METHOD]();\n    }\n    async destroy() {\n        return new Promise((resolve, reject) => {\n            this.#pool.end((err) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve();\n                }\n            });\n        });\n    }\n}\nfunction isOkPacket(obj) {\n    return isObject(obj) && 'insertId' in obj && 'affectedRows' in obj;\n}\nclass MysqlConnection {\n    #rawConnection;\n    constructor(rawConnection) {\n        this.#rawConnection = rawConnection;\n    }\n    async executeQuery(compiledQuery) {\n        try {\n            const result = await this.#executeQuery(compiledQuery);\n            if (isOkPacket(result)) {\n                const { insertId, affectedRows, changedRows } = result;\n                return {\n                    insertId: insertId !== undefined &&\n                        insertId !== null &&\n                        insertId.toString() !== '0'\n                        ? BigInt(insertId)\n                        : undefined,\n                    numAffectedRows: affectedRows !== undefined && affectedRows !== null\n                        ? BigInt(affectedRows)\n                        : undefined,\n                    numChangedRows: changedRows !== undefined && changedRows !== null\n                        ? BigInt(changedRows)\n                        : undefined,\n                    rows: [],\n                };\n            }\n            else if (Array.isArray(result)) {\n                return {\n                    rows: result,\n                };\n            }\n            return {\n                rows: [],\n            };\n        }\n        catch (err) {\n            throw extendStackTrace(err, new Error());\n        }\n    }\n    #executeQuery(compiledQuery) {\n        return new Promise((resolve, reject) => {\n            this.#rawConnection.query(compiledQuery.sql, compiledQuery.parameters, (err, result) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(result);\n                }\n            });\n        });\n    }\n    async *streamQuery(compiledQuery, _chunkSize) {\n        const stream = this.#rawConnection\n            .query(compiledQuery.sql, compiledQuery.parameters)\n            .stream({\n            objectMode: true,\n        });\n        try {\n            for await (const row of stream) {\n                yield {\n                    rows: [row],\n                };\n            }\n        }\n        catch (ex) {\n            if (ex &&\n                typeof ex === 'object' &&\n                'code' in ex &&\n                // @ts-ignore\n                ex.code === 'ERR_STREAM_PREMATURE_CLOSE') {\n                // Most likely because of https://github.com/mysqljs/mysql/blob/master/lib/protocol/sequences/Query.js#L220\n                return;\n            }\n            throw ex;\n        }\n    }\n    [PRIVATE_RELEASE_METHOD]() {\n        this.#rawConnection.release();\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM,yBAAyB;AACxB,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,WAAY,GAAG,IAAI,UAAU;IAC7B,CAAA,IAAK,CAAC;IACN,YAAY,YAAY,CAAE;QACtB,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YAAE,GAAG,YAAY;QAAC;IAC5C;IACA,MAAM,OAAO;QACT,IAAI,CAAC,CAAA,IAAK,GAAG,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI,IACnC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI,KACvB,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI;IAC3B;IACA,MAAM,oBAAoB;QACtB,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAA,iBAAkB;QACnD,IAAI,aAAa,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACb,aAAa,IAAI,gBAAgB;YACjC,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC,eAAe;YACrC,uEAAuE;YACvE,2EAA2E;YAC3E,sEAAsE;YACtE,IAAI,IAAI,CAAC,CAAA,MAAO,EAAE,oBAAoB;gBAClC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,CAAC;YAC1C;QACJ;QACA,IAAI,IAAI,CAAC,CAAA,MAAO,EAAE,qBAAqB;YACnC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,CAAC;QAC3C;QACA,OAAO;IACX;IACA,MAAM,CAAA,iBAAkB;QACpB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa,CAAC,OAAO,KAAK;gBACjC,IAAI,KAAK;oBACL,OAAO;gBACX,OACK;oBACD,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,MAAM,iBAAiB,UAAU,EAAE,QAAQ,EAAE;QACzC,IAAI,SAAS,cAAc,IAAI,SAAS,UAAU,EAAE;YAChD,MAAM,QAAQ,EAAE;YAChB,IAAI,SAAS,cAAc,EAAE;gBACzB,MAAM,IAAI,CAAC,CAAC,gBAAgB,EAAE,SAAS,cAAc,EAAE;YAC3D;YACA,IAAI,SAAS,UAAU,EAAE;gBACrB,MAAM,IAAI,CAAC,SAAS,UAAU;YAClC;YACA,MAAM,MAAM,CAAC,gBAAgB,EAAE,MAAM,IAAI,CAAC,OAAO;YACjD,kEAAkE;YAClE,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACpD;QACA,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,oBAAoB,UAAU,EAAE;QAClC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,UAAU,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QACrD,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAC9G;IACA,MAAM,oBAAoB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC/D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAChH;IACA,MAAM,iBAAiB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC5D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,qBAAqB,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IACtH;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,UAAU,CAAC,uBAAuB;IACtC;IACA,MAAM,UAAU;QACZ,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,CAAA,IAAK,CAAC,GAAG,CAAC,CAAC;gBACZ,IAAI,KAAK;oBACL,OAAO;gBACX,OACK;oBACD;gBACJ;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,GAAG;IACnB,OAAO,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,cAAc,OAAO,kBAAkB;AACnE;AACA,MAAM;IACF,CAAA,aAAc,CAAC;IACf,YAAY,aAAa,CAAE;QACvB,IAAI,CAAC,CAAA,aAAc,GAAG;IAC1B;IACA,MAAM,aAAa,aAAa,EAAE;QAC9B,IAAI;YACA,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,YAAa,CAAC;YACxC,IAAI,WAAW,SAAS;gBACpB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG;gBAChD,OAAO;oBACH,UAAU,aAAa,aACnB,aAAa,QACb,SAAS,QAAQ,OAAO,MACtB,OAAO,YACP;oBACN,iBAAiB,iBAAiB,aAAa,iBAAiB,OAC1D,OAAO,gBACP;oBACN,gBAAgB,gBAAgB,aAAa,gBAAgB,OACvD,OAAO,eACP;oBACN,MAAM,EAAE;gBACZ;YACJ,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;gBAC5B,OAAO;oBACH,MAAM;gBACV;YACJ;YACA,OAAO;gBACH,MAAM,EAAE;YACZ;QACJ,EACA,OAAO,KAAK;YACR,MAAM,CAAA,GAAA,8NAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;QACpC;IACJ;IACA,CAAA,YAAa,CAAC,aAAa;QACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,CAAA,aAAc,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,cAAc,UAAU,EAAE,CAAC,KAAK;gBACzE,IAAI,KAAK;oBACL,OAAO;gBACX,OACK;oBACD,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,OAAO,YAAY,aAAa,EAAE,UAAU,EAAE;QAC1C,MAAM,SAAS,IAAI,CAAC,CAAA,aAAc,CAC7B,KAAK,CAAC,cAAc,GAAG,EAAE,cAAc,UAAU,EACjD,MAAM,CAAC;YACR,YAAY;QAChB;QACA,IAAI;YACA,WAAW,MAAM,OAAO,OAAQ;gBAC5B,MAAM;oBACF,MAAM;wBAAC;qBAAI;gBACf;YACJ;QACJ,EACA,OAAO,IAAI;YACP,IAAI,MACA,OAAO,OAAO,YACd,UAAU,MACV,aAAa;YACb,GAAG,IAAI,KAAK,8BAA8B;gBAC1C,2GAA2G;gBAC3G;YACJ;YACA,MAAM;QACV;IACJ;IACA,CAAC,uBAAuB,GAAG;QACvB,IAAI,CAAC,CAAA,aAAc,CAAC,OAAO;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5280, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mysql/mysql-query-compiler.js"], "sourcesContent": ["/// <reference types=\"./mysql-query-compiler.d.ts\" />\nimport { DefaultQueryCompiler } from '../../query-compiler/default-query-compiler.js';\nconst ID_WRAP_REGEX = /`/g;\nexport class MysqlQueryCompiler extends DefaultQueryCompiler {\n    getCurrentParameterPlaceholder() {\n        return '?';\n    }\n    getLeftExplainOptionsWrapper() {\n        return '';\n    }\n    getExplainOptionAssignment() {\n        return '=';\n    }\n    getExplainOptionsDelimiter() {\n        return ' ';\n    }\n    getRightExplainOptionsWrapper() {\n        return '';\n    }\n    getLeftIdentifierWrapper() {\n        return '`';\n    }\n    getRightIdentifierWrapper() {\n        return '`';\n    }\n    sanitizeIdentifier(identifier) {\n        return identifier.replace(ID_WRAP_REGEX, '``');\n    }\n    visitCreateIndex(node) {\n        this.append('create ');\n        if (node.unique) {\n            this.append('unique ');\n        }\n        this.append('index ');\n        if (node.ifNotExists) {\n            this.append('if not exists ');\n        }\n        this.visitNode(node.name);\n        if (node.using) {\n            this.append(' using ');\n            this.visitNode(node.using);\n        }\n        if (node.table) {\n            this.append(' on ');\n            this.visitNode(node.table);\n        }\n        if (node.columns) {\n            this.append(' (');\n            this.compileList(node.columns);\n            this.append(')');\n        }\n        if (node.where) {\n            this.append(' ');\n            this.visitNode(node.where);\n        }\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;;AACA,MAAM,gBAAgB;AACf,MAAM,2BAA2B,gPAAA,CAAA,uBAAoB;IACxD,iCAAiC;QAC7B,OAAO;IACX;IACA,+BAA+B;QAC3B,OAAO;IACX;IACA,6BAA6B;QACzB,OAAO;IACX;IACA,6BAA6B;QACzB,OAAO;IACX;IACA,gCAAgC;QAC5B,OAAO;IACX;IACA,2BAA2B;QACvB,OAAO;IACX;IACA,4BAA4B;QACxB,OAAO;IACX;IACA,mBAAmB,UAAU,EAAE;QAC3B,OAAO,WAAW,OAAO,CAAC,eAAe;IAC7C;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,KAAK,WAAW,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI;QACxB,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,KAAK,OAAO,EAAE;YACd,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,OAAO;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,KAAK,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK;QAC7B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5347, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mysql/mysql-introspector.js"], "sourcesContent": ["/// <reference types=\"./mysql-introspector.d.ts\" />\nimport { DEFAULT_MIGRATION_LOCK_TABLE, DEFAULT_MIGRATION_TABLE, } from '../../migration/migrator.js';\nimport { freeze } from '../../util/object-utils.js';\nimport { sql } from '../../raw-builder/sql.js';\nexport class MysqlIntrospector {\n    #db;\n    constructor(db) {\n        this.#db = db;\n    }\n    async getSchemas() {\n        let rawSchemas = await this.#db\n            .selectFrom('information_schema.schemata')\n            .select('schema_name')\n            .$castTo()\n            .execute();\n        return rawSchemas.map((it) => ({ name: it.SCHEMA_NAME }));\n    }\n    async getTables(options = { withInternalKyselyTables: false }) {\n        let query = this.#db\n            .selectFrom('information_schema.columns as columns')\n            .innerJoin('information_schema.tables as tables', (b) => b\n            .onRef('columns.TABLE_CATALOG', '=', 'tables.TABLE_CATALOG')\n            .onRef('columns.TABLE_SCHEMA', '=', 'tables.TABLE_SCHEMA')\n            .onRef('columns.TABLE_NAME', '=', 'tables.TABLE_NAME'))\n            .select([\n            'columns.COLUMN_NAME',\n            'columns.COLUMN_DEFAULT',\n            'columns.TABLE_NAME',\n            'columns.TABLE_SCHEMA',\n            'tables.TABLE_TYPE',\n            'columns.IS_NULLABLE',\n            'columns.DATA_TYPE',\n            'columns.EXTRA',\n            'columns.COLUMN_COMMENT',\n        ])\n            .where('columns.TABLE_SCHEMA', '=', sql `database()`)\n            .orderBy('columns.TABLE_NAME')\n            .orderBy('columns.ORDINAL_POSITION')\n            .$castTo();\n        if (!options.withInternalKyselyTables) {\n            query = query\n                .where('columns.TABLE_NAME', '!=', DEFAULT_MIGRATION_TABLE)\n                .where('columns.TABLE_NAME', '!=', DEFAULT_MIGRATION_LOCK_TABLE);\n        }\n        const rawColumns = await query.execute();\n        return this.#parseTableMetadata(rawColumns);\n    }\n    async getMetadata(options) {\n        return {\n            tables: await this.getTables(options),\n        };\n    }\n    #parseTableMetadata(columns) {\n        return columns.reduce((tables, it) => {\n            let table = tables.find((tbl) => tbl.name === it.TABLE_NAME);\n            if (!table) {\n                table = freeze({\n                    name: it.TABLE_NAME,\n                    isView: it.TABLE_TYPE === 'VIEW',\n                    schema: it.TABLE_SCHEMA,\n                    columns: [],\n                });\n                tables.push(table);\n            }\n            table.columns.push(freeze({\n                name: it.COLUMN_NAME,\n                dataType: it.DATA_TYPE,\n                isNullable: it.IS_NULLABLE === 'YES',\n                isAutoIncrementing: it.EXTRA.toLowerCase().includes('auto_increment'),\n                hasDefaultValue: it.COLUMN_DEFAULT !== null,\n                comment: it.COLUMN_COMMENT === '' ? undefined : it.COLUMN_COMMENT,\n            }));\n            return tables;\n        }, []);\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA,MAAM,aAAa;QACf,IAAI,aAAa,MAAM,IAAI,CAAC,CAAA,EAAG,CAC1B,UAAU,CAAC,+BACX,MAAM,CAAC,eACP,OAAO,GACP,OAAO;QACZ,OAAO,WAAW,GAAG,CAAC,CAAC,KAAO,CAAC;gBAAE,MAAM,GAAG,WAAW;YAAC,CAAC;IAC3D;IACA,MAAM,UAAU,UAAU;QAAE,0BAA0B;IAAM,CAAC,EAAE;QAC3D,IAAI,QAAQ,IAAI,CAAC,CAAA,EAAG,CACf,UAAU,CAAC,yCACX,SAAS,CAAC,uCAAuC,CAAC,IAAM,EACxD,KAAK,CAAC,yBAAyB,KAAK,wBACpC,KAAK,CAAC,wBAAwB,KAAK,uBACnC,KAAK,CAAC,sBAAsB,KAAK,sBACjC,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACI,KAAK,CAAC,wBAAwB,KAAK,oNAAA,CAAA,MAAG,AAAC,CAAC,UAAU,CAAC,EACnD,OAAO,CAAC,sBACR,OAAO,CAAC,4BACR,OAAO;QACZ,IAAI,CAAC,QAAQ,wBAAwB,EAAE;YACnC,QAAQ,MACH,KAAK,CAAC,sBAAsB,MAAM,oNAAA,CAAA,0BAAuB,EACzD,KAAK,CAAC,sBAAsB,MAAM,oNAAA,CAAA,+BAA4B;QACvE;QACA,MAAM,aAAa,MAAM,MAAM,OAAO;QACtC,OAAO,IAAI,CAAC,CAAA,kBAAmB,CAAC;IACpC;IACA,MAAM,YAAY,OAAO,EAAE;QACvB,OAAO;YACH,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;QACjC;IACJ;IACA,CAAA,kBAAmB,CAAC,OAAO;QACvB,OAAO,QAAQ,MAAM,CAAC,CAAC,QAAQ;YAC3B,IAAI,QAAQ,OAAO,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK,GAAG,UAAU;YAC3D,IAAI,CAAC,OAAO;gBACR,QAAQ,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;oBACX,MAAM,GAAG,UAAU;oBACnB,QAAQ,GAAG,UAAU,KAAK;oBAC1B,QAAQ,GAAG,YAAY;oBACvB,SAAS,EAAE;gBACf;gBACA,OAAO,IAAI,CAAC;YAChB;YACA,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBACtB,MAAM,GAAG,WAAW;gBACpB,UAAU,GAAG,SAAS;gBACtB,YAAY,GAAG,WAAW,KAAK;gBAC/B,oBAAoB,GAAG,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;gBACpD,iBAAiB,GAAG,cAAc,KAAK;gBACvC,SAAS,GAAG,cAAc,KAAK,KAAK,YAAY,GAAG,cAAc;YACrE;YACA,OAAO;QACX,GAAG,EAAE;IACT;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5423, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mysql/mysql-adapter.js"], "sourcesContent": ["/// <reference types=\"./mysql-adapter.d.ts\" />\nimport { sql } from '../../raw-builder/sql.js';\nimport { DialectAdapterBase } from '../dialect-adapter-base.js';\nconst LOCK_ID = 'ea586330-2c93-47c8-908d-981d9d270f9d';\nconst LOCK_TIMEOUT_SECONDS = 60 * 60;\nexport class MysqlAdapter extends DialectAdapterBase {\n    get supportsTransactionalDdl() {\n        return false;\n    }\n    get supportsReturning() {\n        return false;\n    }\n    async acquireMigrationLock(db, _opt) {\n        // <PERSON><PERSON><PERSON> uses a single connection to run the migrations. Because of that, we\n        // can take a lock using `get_lock`. Locks acquired using `get_lock` get\n        // released when the connection is destroyed (session ends) or when the lock\n        // is released using `release_lock`. This way we know that the lock is either\n        // released by us after successfull or failed migrations OR it's released by\n        // MySQL if the process gets killed for some reason.\n        await sql `select get_lock(${sql.lit(LOCK_ID)}, ${sql.lit(LOCK_TIMEOUT_SECONDS)})`.execute(db);\n    }\n    async releaseMigrationLock(db, _opt) {\n        await sql `select release_lock(${sql.lit(LOCK_ID)})`.execute(db);\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;;;AACA,MAAM,UAAU;AAChB,MAAM,uBAAuB,KAAK;AAC3B,MAAM,qBAAqB,oOAAA,CAAA,qBAAkB;IAChD,IAAI,2BAA2B;QAC3B,OAAO;IACX;IACA,IAAI,oBAAoB;QACpB,OAAO;IACX;IACA,MAAM,qBAAqB,EAAE,EAAE,IAAI,EAAE;QACjC,6EAA6E;QAC7E,wEAAwE;QACxE,4EAA4E;QAC5E,6EAA6E;QAC7E,4EAA4E;QAC5E,oDAAoD;QACpD,MAAM,oNAAA,CAAA,MAAG,AAAC,CAAC,gBAAgB,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC;IAC/F;IACA,MAAM,qBAAqB,EAAE,EAAE,IAAI,EAAE;QACjC,MAAM,oNAAA,CAAA,MAAG,AAAC,CAAC,oBAAoB,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IACjE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5459, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mysql/mysql-dialect.js"], "sourcesContent": ["/// <reference types=\"./mysql-dialect.d.ts\" />\nimport { MysqlDriver } from './mysql-driver.js';\nimport { MysqlQueryCompiler } from './mysql-query-compiler.js';\nimport { MysqlIntrospector } from './mysql-introspector.js';\nimport { MysqlAdapter } from './mysql-adapter.js';\n/**\n * MySQL dialect that uses the [mysql2](https://github.com/sidorares/node-mysql2#readme) library.\n *\n * The constructor takes an instance of {@link MysqlDialectConfig}.\n *\n * ```ts\n * import { createPool } from 'mysql2'\n *\n * new MysqlDialect({\n *   pool: createPool({\n *     database: 'some_db',\n *     host: 'localhost',\n *   })\n * })\n * ```\n *\n * If you want the pool to only be created once it's first used, `pool`\n * can be a function:\n *\n * ```ts\n * import { createPool } from 'mysql2'\n *\n * new MysqlDialect({\n *   pool: async () => createPool({\n *     database: 'some_db',\n *     host: 'localhost',\n *   })\n * })\n * ```\n */\nexport class MysqlDialect {\n    #config;\n    constructor(config) {\n        this.#config = config;\n    }\n    createDriver() {\n        return new MysqlDriver(this.#config);\n    }\n    createQueryCompiler() {\n        return new MysqlQueryCompiler();\n    }\n    createAdapter() {\n        return new MysqlAdapter();\n    }\n    createIntrospector(db) {\n        return new MysqlIntrospector(db);\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;;;;;AA+BO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,eAAe;QACX,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,MAAO;IACvC;IACA,sBAAsB;QAClB,OAAO,IAAI,6OAAA,CAAA,qBAAkB;IACjC;IACA,gBAAgB;QACZ,OAAO,IAAI,mOAAA,CAAA,eAAY;IAC3B;IACA,mBAAmB,EAAE,EAAE;QACnB,OAAO,IAAI,wOAAA,CAAA,oBAAiB,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5495, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/postgres/postgres-driver.js"], "sourcesContent": ["/// <reference types=\"./postgres-driver.d.ts\" />\nimport { parseSavepointCommand } from '../../parser/savepoint-parser.js';\nimport { CompiledQuery } from '../../query-compiler/compiled-query.js';\nimport { isFunction, freeze } from '../../util/object-utils.js';\nimport { createQueryId } from '../../util/query-id.js';\nimport { extendStackTrace } from '../../util/stack-trace-utils.js';\nconst PRIVATE_RELEASE_METHOD = Symbol();\nexport class PostgresDriver {\n    #config;\n    #connections = new WeakMap();\n    #pool;\n    constructor(config) {\n        this.#config = freeze({ ...config });\n    }\n    async init() {\n        this.#pool = isFunction(this.#config.pool)\n            ? await this.#config.pool()\n            : this.#config.pool;\n    }\n    async acquireConnection() {\n        const client = await this.#pool.connect();\n        let connection = this.#connections.get(client);\n        if (!connection) {\n            connection = new PostgresConnection(client, {\n                cursor: this.#config.cursor ?? null,\n            });\n            this.#connections.set(client, connection);\n            // The driver must take care of calling `onCreateConnection` when a new\n            // connection is created. The `pg` module doesn't provide an async hook\n            // for the connection creation. We need to call the method explicitly.\n            if (this.#config.onCreateConnection) {\n                await this.#config.onCreateConnection(connection);\n            }\n        }\n        if (this.#config.onReserveConnection) {\n            await this.#config.onReserveConnection(connection);\n        }\n        return connection;\n    }\n    async beginTransaction(connection, settings) {\n        if (settings.isolationLevel || settings.accessMode) {\n            let sql = 'start transaction';\n            if (settings.isolationLevel) {\n                sql += ` isolation level ${settings.isolationLevel}`;\n            }\n            if (settings.accessMode) {\n                sql += ` ${settings.accessMode}`;\n            }\n            await connection.executeQuery(CompiledQuery.raw(sql));\n        }\n        else {\n            await connection.executeQuery(CompiledQuery.raw('begin'));\n        }\n    }\n    async commitTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('commit'));\n    }\n    async rollbackTransaction(connection) {\n        await connection.executeQuery(CompiledQuery.raw('rollback'));\n    }\n    async savepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('savepoint', savepointName), createQueryId()));\n    }\n    async rollbackToSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('rollback to', savepointName), createQueryId()));\n    }\n    async releaseSavepoint(connection, savepointName, compileQuery) {\n        await connection.executeQuery(compileQuery(parseSavepointCommand('release', savepointName), createQueryId()));\n    }\n    async releaseConnection(connection) {\n        connection[PRIVATE_RELEASE_METHOD]();\n    }\n    async destroy() {\n        if (this.#pool) {\n            const pool = this.#pool;\n            this.#pool = undefined;\n            await pool.end();\n        }\n    }\n}\nclass PostgresConnection {\n    #client;\n    #options;\n    constructor(client, options) {\n        this.#client = client;\n        this.#options = options;\n    }\n    async executeQuery(compiledQuery) {\n        try {\n            const { command, rowCount, rows } = await this.#client.query(compiledQuery.sql, [...compiledQuery.parameters]);\n            return {\n                numAffectedRows: command === 'INSERT' ||\n                    command === 'UPDATE' ||\n                    command === 'DELETE' ||\n                    command === 'MERGE'\n                    ? BigInt(rowCount)\n                    : undefined,\n                rows: rows ?? [],\n            };\n        }\n        catch (err) {\n            throw extendStackTrace(err, new Error());\n        }\n    }\n    async *streamQuery(compiledQuery, chunkSize) {\n        if (!this.#options.cursor) {\n            throw new Error(\"'cursor' is not present in your postgres dialect config. It's required to make streaming work in postgres.\");\n        }\n        if (!Number.isInteger(chunkSize) || chunkSize <= 0) {\n            throw new Error('chunkSize must be a positive integer');\n        }\n        const cursor = this.#client.query(new this.#options.cursor(compiledQuery.sql, compiledQuery.parameters.slice()));\n        try {\n            while (true) {\n                const rows = await cursor.read(chunkSize);\n                if (rows.length === 0) {\n                    break;\n                }\n                yield {\n                    rows,\n                };\n            }\n        }\n        finally {\n            await cursor.close();\n        }\n    }\n    [PRIVATE_RELEASE_METHOD]() {\n        this.#client.release();\n    }\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM,yBAAyB;AACxB,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,WAAY,GAAG,IAAI,UAAU;IAC7B,CAAA,IAAK,CAAC;IACN,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YAAE,GAAG,MAAM;QAAC;IACtC;IACA,MAAM,OAAO;QACT,IAAI,CAAC,CAAA,IAAK,GAAG,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI,IACnC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI,KACvB,IAAI,CAAC,CAAA,MAAO,CAAC,IAAI;IAC3B;IACA,MAAM,oBAAoB;QACtB,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,OAAO;QACvC,IAAI,aAAa,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC;QACvC,IAAI,CAAC,YAAY;YACb,aAAa,IAAI,mBAAmB,QAAQ;gBACxC,QAAQ,IAAI,CAAC,CAAA,MAAO,CAAC,MAAM,IAAI;YACnC;YACA,IAAI,CAAC,CAAA,WAAY,CAAC,GAAG,CAAC,QAAQ;YAC9B,uEAAuE;YACvE,uEAAuE;YACvE,sEAAsE;YACtE,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,EAAE;gBACjC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,kBAAkB,CAAC;YAC1C;QACJ;QACA,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,EAAE;YAClC,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,mBAAmB,CAAC;QAC3C;QACA,OAAO;IACX;IACA,MAAM,iBAAiB,UAAU,EAAE,QAAQ,EAAE;QACzC,IAAI,SAAS,cAAc,IAAI,SAAS,UAAU,EAAE;YAChD,IAAI,MAAM;YACV,IAAI,SAAS,cAAc,EAAE;gBACzB,OAAO,CAAC,iBAAiB,EAAE,SAAS,cAAc,EAAE;YACxD;YACA,IAAI,SAAS,UAAU,EAAE;gBACrB,OAAO,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YACpC;YACA,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACpD,OACK;YACD,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACpD;IACJ;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,oBAAoB,UAAU,EAAE;QAClC,MAAM,WAAW,YAAY,CAAC,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACpD;IACA,MAAM,UAAU,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QACrD,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAC9G;IACA,MAAM,oBAAoB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC/D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAChH;IACA,MAAM,iBAAiB,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE;QAC5D,MAAM,WAAW,YAAY,CAAC,aAAa,CAAA,GAAA,4NAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW,gBAAgB,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;IAC5G;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,UAAU,CAAC,uBAAuB;IACtC;IACA,MAAM,UAAU;QACZ,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE;YACZ,MAAM,OAAO,IAAI,CAAC,CAAA,IAAK;YACvB,IAAI,CAAC,CAAA,IAAK,GAAG;YACb,MAAM,KAAK,GAAG;QAClB;IACJ;AACJ;AACA,MAAM;IACF,CAAA,MAAO,CAAC;IACR,CAAA,OAAQ,CAAC;IACT,YAAY,MAAM,EAAE,OAAO,CAAE;QACzB,IAAI,CAAC,CAAA,MAAO,GAAG;QACf,IAAI,CAAC,CAAA,OAAQ,GAAG;IACpB;IACA,MAAM,aAAa,aAAa,EAAE;QAC9B,IAAI;YACA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,CAAA,MAAO,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE;mBAAI,cAAc,UAAU;aAAC;YAC7G,OAAO;gBACH,iBAAiB,YAAY,YACzB,YAAY,YACZ,YAAY,YACZ,YAAY,UACV,OAAO,YACP;gBACN,MAAM,QAAQ,EAAE;YACpB;QACJ,EACA,OAAO,KAAK;YACR,MAAM,CAAA,GAAA,8NAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;QACpC;IACJ;IACA,OAAO,YAAY,aAAa,EAAE,SAAS,EAAE;QACzC,IAAI,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,MAAM,EAAE;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,aAAa,GAAG;YAChD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,SAAS,IAAI,CAAC,CAAA,MAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAA,OAAQ,CAAC,MAAM,CAAC,cAAc,GAAG,EAAE,cAAc,UAAU,CAAC,KAAK;QAC5G,IAAI;YACA,MAAO,KAAM;gBACT,MAAM,OAAO,MAAM,OAAO,IAAI,CAAC;gBAC/B,IAAI,KAAK,MAAM,KAAK,GAAG;oBACnB;gBACJ;gBACA,MAAM;oBACF;gBACJ;YACJ;QACJ,SACQ;YACJ,MAAM,OAAO,KAAK;QACtB;IACJ;IACA,CAAC,uBAAuB,GAAG;QACvB,IAAI,CAAC,CAAA,MAAO,CAAC,OAAO;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/postgres/postgres-introspector.js"], "sourcesContent": ["/// <reference types=\"./postgres-introspector.d.ts\" />\nimport { DEFAULT_MIGRATION_LOCK_TABLE, DEFAULT_MIGRATION_TABLE, } from '../../migration/migrator.js';\nimport { freeze } from '../../util/object-utils.js';\nimport { sql } from '../../raw-builder/sql.js';\nexport class PostgresIntrospector {\n    #db;\n    constructor(db) {\n        this.#db = db;\n    }\n    async getSchemas() {\n        let rawSchemas = await this.#db\n            .selectFrom('pg_catalog.pg_namespace')\n            .select('nspname')\n            .$castTo()\n            .execute();\n        return rawSchemas.map((it) => ({ name: it.nspname }));\n    }\n    async getTables(options = { withInternalKyselyTables: false }) {\n        let query = this.#db\n            // column\n            .selectFrom('pg_catalog.pg_attribute as a')\n            // table\n            .innerJoin('pg_catalog.pg_class as c', 'a.attrelid', 'c.oid')\n            // table schema\n            .innerJoin('pg_catalog.pg_namespace as ns', 'c.relnamespace', 'ns.oid')\n            // column data type\n            .innerJoin('pg_catalog.pg_type as typ', 'a.atttypid', 'typ.oid')\n            // column data type schema\n            .innerJoin('pg_catalog.pg_namespace as dtns', 'typ.typnamespace', 'dtns.oid')\n            .select([\n            'a.attname as column',\n            'a.attnotnull as not_null',\n            'a.atthasdef as has_default',\n            'c.relname as table',\n            'c.relkind as table_type',\n            'ns.nspname as schema',\n            'typ.typname as type',\n            'dtns.nspname as type_schema',\n            sql `col_description(a.attrelid, a.attnum)`.as('column_description'),\n            sql `pg_get_serial_sequence(quote_ident(ns.nspname) || '.' || quote_ident(c.relname), a.attname)`.as('auto_incrementing'),\n        ])\n            .where('c.relkind', 'in', [\n            'r' /*regular table*/,\n            'v' /*view*/,\n            'p' /*partitioned table*/,\n        ])\n            .where('ns.nspname', '!~', '^pg_')\n            .where('ns.nspname', '!=', 'information_schema')\n            // No system columns\n            .where('a.attnum', '>=', 0)\n            .where('a.attisdropped', '!=', true)\n            .orderBy('ns.nspname')\n            .orderBy('c.relname')\n            .orderBy('a.attnum')\n            .$castTo();\n        if (!options.withInternalKyselyTables) {\n            query = query\n                .where('c.relname', '!=', DEFAULT_MIGRATION_TABLE)\n                .where('c.relname', '!=', DEFAULT_MIGRATION_LOCK_TABLE);\n        }\n        const rawColumns = await query.execute();\n        return this.#parseTableMetadata(rawColumns);\n    }\n    async getMetadata(options) {\n        return {\n            tables: await this.getTables(options),\n        };\n    }\n    #parseTableMetadata(columns) {\n        return columns.reduce((tables, it) => {\n            let table = tables.find((tbl) => tbl.name === it.table && tbl.schema === it.schema);\n            if (!table) {\n                table = freeze({\n                    name: it.table,\n                    isView: it.table_type === 'v',\n                    schema: it.schema,\n                    columns: [],\n                });\n                tables.push(table);\n            }\n            table.columns.push(freeze({\n                name: it.column,\n                dataType: it.type,\n                dataTypeSchema: it.type_schema,\n                isNullable: !it.not_null,\n                isAutoIncrementing: it.auto_incrementing !== null,\n                hasDefaultValue: it.has_default,\n                comment: it.column_description ?? undefined,\n            }));\n            return tables;\n        }, []);\n    }\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA,MAAM,aAAa;QACf,IAAI,aAAa,MAAM,IAAI,CAAC,CAAA,EAAG,CAC1B,UAAU,CAAC,2BACX,MAAM,CAAC,WACP,OAAO,GACP,OAAO;QACZ,OAAO,WAAW,GAAG,CAAC,CAAC,KAAO,CAAC;gBAAE,MAAM,GAAG,OAAO;YAAC,CAAC;IACvD;IACA,MAAM,UAAU,UAAU;QAAE,0BAA0B;IAAM,CAAC,EAAE;QAC3D,IAAI,QAAQ,IAAI,CAAC,CAAA,EAAG,AAChB,SAAS;SACR,UAAU,CAAC,+BACZ,QAAQ;SACP,SAAS,CAAC,4BAA4B,cAAc,QACrD,eAAe;SACd,SAAS,CAAC,iCAAiC,kBAAkB,SAC9D,mBAAmB;SAClB,SAAS,CAAC,6BAA6B,cAAc,UACtD,0BAA0B;SACzB,SAAS,CAAC,mCAAmC,oBAAoB,YACjE,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,oNAAA,CAAA,MAAG,AAAC,CAAC,qCAAqC,CAAC,CAAC,EAAE,CAAC;YAC/C,oNAAA,CAAA,MAAG,AAAC,CAAC,2FAA2F,CAAC,CAAC,EAAE,CAAC;SACxG,EACI,KAAK,CAAC,aAAa,MAAM;YAC1B,IAAI,eAAe;YACnB,IAAI,MAAM;YACV,IAAI,mBAAmB;SAC1B,EACI,KAAK,CAAC,cAAc,MAAM,QAC1B,KAAK,CAAC,cAAc,MAAM,qBAC3B,oBAAoB;SACnB,KAAK,CAAC,YAAY,MAAM,GACxB,KAAK,CAAC,kBAAkB,MAAM,MAC9B,OAAO,CAAC,cACR,OAAO,CAAC,aACR,OAAO,CAAC,YACR,OAAO;QACZ,IAAI,CAAC,QAAQ,wBAAwB,EAAE;YACnC,QAAQ,MACH,KAAK,CAAC,aAAa,MAAM,oNAAA,CAAA,0BAAuB,EAChD,KAAK,CAAC,aAAa,MAAM,oNAAA,CAAA,+BAA4B;QAC9D;QACA,MAAM,aAAa,MAAM,MAAM,OAAO;QACtC,OAAO,IAAI,CAAC,CAAA,kBAAmB,CAAC;IACpC;IACA,MAAM,YAAY,OAAO,EAAE;QACvB,OAAO;YACH,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;QACjC;IACJ;IACA,CAAA,kBAAmB,CAAC,OAAO;QACvB,OAAO,QAAQ,MAAM,CAAC,CAAC,QAAQ;YAC3B,IAAI,QAAQ,OAAO,IAAI,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,MAAM;YAClF,IAAI,CAAC,OAAO;gBACR,QAAQ,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;oBACX,MAAM,GAAG,KAAK;oBACd,QAAQ,GAAG,UAAU,KAAK;oBAC1B,QAAQ,GAAG,MAAM;oBACjB,SAAS,EAAE;gBACf;gBACA,OAAO,IAAI,CAAC;YAChB;YACA,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBACtB,MAAM,GAAG,MAAM;gBACf,UAAU,GAAG,IAAI;gBACjB,gBAAgB,GAAG,WAAW;gBAC9B,YAAY,CAAC,GAAG,QAAQ;gBACxB,oBAAoB,GAAG,iBAAiB,KAAK;gBAC7C,iBAAiB,GAAG,WAAW;gBAC/B,SAAS,GAAG,kBAAkB,IAAI;YACtC;YACA,OAAO;QACX,GAAG,EAAE;IACT;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/postgres/postgres-query-compiler.js"], "sourcesContent": ["/// <reference types=\"./postgres-query-compiler.d.ts\" />\nimport { DefaultQueryCompiler } from '../../query-compiler/default-query-compiler.js';\nconst ID_WRAP_REGEX = /\"/g;\nexport class PostgresQueryCompiler extends DefaultQueryCompiler {\n    sanitizeIdentifier(identifier) {\n        return identifier.replace(ID_WRAP_REGEX, '\"\"');\n    }\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACxD;;AACA,MAAM,gBAAgB;AACf,MAAM,8BAA8B,gPAAA,CAAA,uBAAoB;IAC3D,mBAAmB,UAAU,EAAE;QAC3B,OAAO,WAAW,OAAO,CAAC,eAAe;IAC7C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5740, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/postgres/postgres-adapter.js"], "sourcesContent": ["/// <reference types=\"./postgres-adapter.d.ts\" />\nimport { sql } from '../../raw-builder/sql.js';\nimport { DialectAdapterBase } from '../dialect-adapter-base.js';\n// Random id for our transaction lock.\nconst LOCK_ID = BigInt('3853314791062309107');\nexport class PostgresAdapter extends DialectAdapterBase {\n    get supportsTransactionalDdl() {\n        return true;\n    }\n    get supportsReturning() {\n        return true;\n    }\n    async acquireMigrationLock(db, _opt) {\n        // Acquire a transaction level advisory lock.\n        await sql `select pg_advisory_xact_lock(${sql.lit(LOCK_ID)})`.execute(db);\n    }\n    async releaseMigrationLock(_db, _opt) {\n        // Nothing to do here. `pg_advisory_xact_lock` is automatically released at the\n        // end of the transaction and since `supportsTransactionalDdl` true, we know\n        // the `db` instance passed to acquireMigrationLock is actually a transaction.\n    }\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;AACjD;AACA;;;AACA,sCAAsC;AACtC,MAAM,UAAU,OAAO;AAChB,MAAM,wBAAwB,oOAAA,CAAA,qBAAkB;IACnD,IAAI,2BAA2B;QAC3B,OAAO;IACX;IACA,IAAI,oBAAoB;QACpB,OAAO;IACX;IACA,MAAM,qBAAqB,EAAE,EAAE,IAAI,EAAE;QACjC,6CAA6C;QAC7C,MAAM,oNAAA,CAAA,MAAG,AAAC,CAAC,6BAA6B,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;IAC1E;IACA,MAAM,qBAAqB,GAAG,EAAE,IAAI,EAAE;IAClC,+EAA+E;IAC/E,4EAA4E;IAC5E,8EAA8E;IAClF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5773, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/postgres/postgres-dialect.js"], "sourcesContent": ["/// <reference types=\"./postgres-dialect.d.ts\" />\nimport { PostgresDriver } from './postgres-driver.js';\nimport { PostgresIntrospector } from './postgres-introspector.js';\nimport { PostgresQueryCompiler } from './postgres-query-compiler.js';\nimport { PostgresAdapter } from './postgres-adapter.js';\n/**\n * PostgreSQL dialect that uses the [pg](https://node-postgres.com/) library.\n *\n * The constructor takes an instance of {@link PostgresDialectConfig}.\n *\n * ```ts\n * import { Pool } from 'pg'\n *\n * new PostgresDialect({\n *   pool: new Pool({\n *     database: 'some_db',\n *     host: 'localhost',\n *   })\n * })\n * ```\n *\n * If you want the pool to only be created once it's first used, `pool`\n * can be a function:\n *\n * ```ts\n * import { Pool } from 'pg'\n *\n * new PostgresDialect({\n *   pool: async () => new Pool({\n *     database: 'some_db',\n *     host: 'localhost',\n *   })\n * })\n * ```\n */\nexport class PostgresDialect {\n    #config;\n    constructor(config) {\n        this.#config = config;\n    }\n    createDriver() {\n        return new PostgresDriver(this.#config);\n    }\n    createQueryCompiler() {\n        return new PostgresQueryCompiler();\n    }\n    createAdapter() {\n        return new PostgresAdapter();\n    }\n    createIntrospector(db) {\n        return new PostgresIntrospector(db);\n    }\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;AACjD;AACA;AACA;AACA;;;;;AA+BO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,eAAe;QACX,OAAO,IAAI,wOAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,CAAA,MAAO;IAC1C;IACA,sBAAsB;QAClB,OAAO,IAAI,mPAAA,CAAA,wBAAqB;IACpC;IACA,gBAAgB;QACZ,OAAO,IAAI,yOAAA,CAAA,kBAAe;IAC9B;IACA,mBAAmB,EAAE,EAAE;QACnB,OAAO,IAAI,8OAAA,CAAA,uBAAoB,CAAC;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5809, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mssql/mssql-adapter.js"], "sourcesContent": ["/// <reference types=\"./mssql-adapter.d.ts\" />\nimport { DEFAULT_MIGRATION_TABLE } from '../../migration/migrator.js';\nimport { sql } from '../../raw-builder/sql.js';\nimport { DialectAdapterBase } from '../dialect-adapter-base.js';\nexport class MssqlAdapter extends DialectAdapterBase {\n    get supportsCreateIfNotExists() {\n        return false;\n    }\n    get supportsTransactionalDdl() {\n        return true;\n    }\n    get supportsOutput() {\n        return true;\n    }\n    async acquireMigrationLock(db) {\n        // Acquire a transaction-level exclusive lock on the migrations table.\n        // https://learn.microsoft.com/en-us/sql/relational-databases/system-stored-procedures/sp-getapplock-transact-sql?view=sql-server-ver16\n        await sql `exec sp_getapplock @DbPrincipal = ${sql.lit('dbo')}, @Resource = ${sql.lit(DEFAULT_MIGRATION_TABLE)}, @LockMode = ${sql.lit('Exclusive')}`.execute(db);\n    }\n    async releaseMigrationLock() {\n        // Nothing to do here. `sp_getapplock` is automatically released at the\n        // end of the transaction and since `supportsTransactionalDdl` true, we know\n        // the `db` instance passed to acquireMigrationLock is actually a transaction.\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;;;;AACO,MAAM,qBAAqB,oOAAA,CAAA,qBAAkB;IAChD,IAAI,4BAA4B;QAC5B,OAAO;IACX;IACA,IAAI,2BAA2B;QAC3B,OAAO;IACX;IACA,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,MAAM,qBAAqB,EAAE,EAAE;QAC3B,sEAAsE;QACtE,uIAAuI;QACvI,MAAM,oNAAA,CAAA,MAAG,AAAC,CAAC,kCAAkC,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,OAAO,cAAc,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,oNAAA,CAAA,0BAAuB,EAAE,cAAc,EAAE,oNAAA,CAAA,MAAG,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;IAClK;IACA,MAAM,uBAAuB;IACzB,uEAAuE;IACvE,4EAA4E;IAC5E,8EAA8E;IAClF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5846, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mssql/mssql-driver.js"], "sourcesContent": ["/// <reference types=\"./mssql-driver.d.ts\" />\nimport { freeze, isBigInt, isBoolean, isBuffer, isDate, isNull, isNumber, isString, isUndefined, } from '../../util/object-utils.js';\nimport { CompiledQuery } from '../../query-compiler/compiled-query.js';\nimport { extendStackTrace } from '../../util/stack-trace-utils.js';\nimport { randomString } from '../../util/random-string.js';\nimport { Deferred } from '../../util/deferred.js';\nconst PRIVATE_RESET_METHOD = Symbol();\nconst PRIVATE_DESTROY_METHOD = Symbol();\nexport class MssqlDriver {\n    #config;\n    #pool;\n    constructor(config) {\n        this.#config = freeze({ ...config });\n        const { tarn, tedious, validateConnections } = this.#config;\n        const { validateConnections: deprecatedValidateConnections, ...poolOptions } = tarn.options;\n        this.#pool = new tarn.Pool({\n            ...poolOptions,\n            create: async () => {\n                const connection = await tedious.connectionFactory();\n                return await new MssqlConnection(connection, tedious).connect();\n            },\n            destroy: async (connection) => {\n                await connection[PRIVATE_DESTROY_METHOD]();\n            },\n            // @ts-ignore `tarn` accepts a function that returns a promise here, but\n            // the types are not aligned and it type errors.\n            validate: validateConnections === false ||\n                deprecatedValidateConnections === false\n                ? undefined\n                : (connection) => connection.validate(),\n        });\n    }\n    async init() {\n        // noop\n    }\n    async acquireConnection() {\n        return await this.#pool.acquire().promise;\n    }\n    async beginTransaction(connection, settings) {\n        await connection.beginTransaction(settings);\n    }\n    async commitTransaction(connection) {\n        await connection.commitTransaction();\n    }\n    async rollbackTransaction(connection) {\n        await connection.rollbackTransaction();\n    }\n    async savepoint(connection, savepointName) {\n        await connection.savepoint(savepointName);\n    }\n    async rollbackToSavepoint(connection, savepointName) {\n        await connection.rollbackTransaction(savepointName);\n    }\n    async releaseConnection(connection) {\n        if (this.#config.resetConnectionsOnRelease ||\n            this.#config.tedious.resetConnectionOnRelease) {\n            await connection[PRIVATE_RESET_METHOD]();\n        }\n        this.#pool.release(connection);\n    }\n    async destroy() {\n        await this.#pool.destroy();\n    }\n}\nclass MssqlConnection {\n    #connection;\n    #tedious;\n    constructor(connection, tedious) {\n        this.#connection = connection;\n        this.#tedious = tedious;\n        this.#connection.on('error', console.error);\n        this.#connection.once('end', () => {\n            this.#connection.off('error', console.error);\n        });\n    }\n    async beginTransaction(settings) {\n        const { isolationLevel } = settings;\n        await new Promise((resolve, reject) => this.#connection.beginTransaction((error) => {\n            if (error)\n                reject(error);\n            else\n                resolve(undefined);\n        }, isolationLevel ? randomString(8) : undefined, isolationLevel\n            ? this.#getTediousIsolationLevel(isolationLevel)\n            : undefined));\n    }\n    async commitTransaction() {\n        await new Promise((resolve, reject) => this.#connection.commitTransaction((error) => {\n            if (error)\n                reject(error);\n            else\n                resolve(undefined);\n        }));\n    }\n    async connect() {\n        await new Promise((resolve, reject) => {\n            this.#connection.connect((error) => {\n                if (error) {\n                    console.error(error);\n                    reject(error);\n                }\n                else {\n                    resolve(undefined);\n                }\n            });\n        });\n        return this;\n    }\n    async executeQuery(compiledQuery) {\n        try {\n            const deferred = new Deferred();\n            const request = new MssqlRequest({\n                compiledQuery,\n                tedious: this.#tedious,\n                onDone: deferred,\n            });\n            this.#connection.execSql(request.request);\n            const { rowCount, rows } = await deferred.promise;\n            return {\n                numAffectedRows: rowCount !== undefined ? BigInt(rowCount) : undefined,\n                rows,\n            };\n        }\n        catch (err) {\n            throw extendStackTrace(err, new Error());\n        }\n    }\n    async rollbackTransaction(savepointName) {\n        await new Promise((resolve, reject) => this.#connection.rollbackTransaction((error) => {\n            if (error)\n                reject(error);\n            else\n                resolve(undefined);\n        }, savepointName));\n    }\n    async savepoint(savepointName) {\n        await new Promise((resolve, reject) => this.#connection.saveTransaction((error) => {\n            if (error)\n                reject(error);\n            else\n                resolve(undefined);\n        }, savepointName));\n    }\n    async *streamQuery(compiledQuery, chunkSize) {\n        if (!Number.isInteger(chunkSize) || chunkSize <= 0) {\n            throw new Error('chunkSize must be a positive integer');\n        }\n        const request = new MssqlRequest({\n            compiledQuery,\n            streamChunkSize: chunkSize,\n            tedious: this.#tedious,\n        });\n        this.#connection.execSql(request.request);\n        try {\n            while (true) {\n                const rows = await request.readChunk();\n                if (rows.length === 0) {\n                    break;\n                }\n                yield { rows };\n                if (rows.length < chunkSize) {\n                    break;\n                }\n            }\n        }\n        finally {\n            await this.#cancelRequest(request);\n        }\n    }\n    async validate() {\n        try {\n            const deferred = new Deferred();\n            const request = new MssqlRequest({\n                compiledQuery: CompiledQuery.raw('select 1'),\n                onDone: deferred,\n                tedious: this.#tedious,\n            });\n            this.#connection.execSql(request.request);\n            await deferred.promise;\n            return true;\n        }\n        catch {\n            return false;\n        }\n    }\n    #getTediousIsolationLevel(isolationLevel) {\n        const { ISOLATION_LEVEL } = this.#tedious;\n        const mapper = {\n            'read committed': ISOLATION_LEVEL.READ_COMMITTED,\n            'read uncommitted': ISOLATION_LEVEL.READ_UNCOMMITTED,\n            'repeatable read': ISOLATION_LEVEL.REPEATABLE_READ,\n            serializable: ISOLATION_LEVEL.SERIALIZABLE,\n            snapshot: ISOLATION_LEVEL.SNAPSHOT,\n        };\n        const tediousIsolationLevel = mapper[isolationLevel];\n        if (tediousIsolationLevel === undefined) {\n            throw new Error(`Unknown isolation level: ${isolationLevel}`);\n        }\n        return tediousIsolationLevel;\n    }\n    #cancelRequest(request) {\n        return new Promise((resolve) => {\n            request.request.once('requestCompleted', resolve);\n            const wasCanceled = this.#connection.cancel();\n            if (!wasCanceled) {\n                request.request.off('requestCompleted', resolve);\n                resolve(undefined);\n            }\n        });\n    }\n    async [PRIVATE_RESET_METHOD]() {\n        await new Promise((resolve, reject) => {\n            this.#connection.reset((error) => {\n                if (error)\n                    reject(error);\n                else\n                    resolve(undefined);\n            });\n        });\n    }\n    [PRIVATE_DESTROY_METHOD]() {\n        return new Promise((resolve) => {\n            this.#connection.once('end', () => {\n                resolve(undefined);\n            });\n            this.#connection.close();\n        });\n    }\n}\nclass MssqlRequest {\n    #request;\n    #rows;\n    #streamChunkSize;\n    #subscribers;\n    #tedious;\n    #rowCount;\n    constructor(props) {\n        const { compiledQuery, onDone, streamChunkSize, tedious } = props;\n        this.#rows = [];\n        this.#streamChunkSize = streamChunkSize;\n        this.#subscribers = {};\n        this.#tedious = tedious;\n        if (onDone) {\n            const subscriptionKey = 'onDone';\n            this.#subscribers[subscriptionKey] = (event, error) => {\n                if (event === 'chunkReady') {\n                    return;\n                }\n                delete this.#subscribers[subscriptionKey];\n                if (event === 'error') {\n                    onDone.reject(error);\n                }\n                else {\n                    onDone.resolve({\n                        rowCount: this.#rowCount,\n                        rows: this.#rows,\n                    });\n                }\n            };\n        }\n        this.#request = new this.#tedious.Request(compiledQuery.sql, (err, rowCount) => {\n            if (err) {\n                Object.values(this.#subscribers).forEach((subscriber) => subscriber('error', err instanceof AggregateError ? err.errors : err));\n            }\n            else {\n                this.#rowCount = rowCount;\n            }\n        });\n        this.#addParametersToRequest(compiledQuery.parameters);\n        this.#attachListeners();\n    }\n    get request() {\n        return this.#request;\n    }\n    readChunk() {\n        const subscriptionKey = this.readChunk.name;\n        return new Promise((resolve, reject) => {\n            this.#subscribers[subscriptionKey] = (event, error) => {\n                delete this.#subscribers[subscriptionKey];\n                if (event === 'error') {\n                    reject(error);\n                }\n                else {\n                    resolve(this.#rows.splice(0, this.#streamChunkSize));\n                }\n            };\n            this.#request.resume();\n        });\n    }\n    #addParametersToRequest(parameters) {\n        for (let i = 0; i < parameters.length; i++) {\n            const parameter = parameters[i];\n            this.#request.addParameter(String(i + 1), this.#getTediousDataType(parameter), parameter);\n        }\n    }\n    #attachListeners() {\n        const pauseAndEmitChunkReady = this.#streamChunkSize\n            ? () => {\n                if (this.#streamChunkSize <= this.#rows.length) {\n                    this.#request.pause();\n                    Object.values(this.#subscribers).forEach((subscriber) => subscriber('chunkReady'));\n                }\n            }\n            : () => { };\n        const rowListener = (columns) => {\n            const row = {};\n            for (const column of columns) {\n                row[column.metadata.colName] = column.value;\n            }\n            this.#rows.push(row);\n            pauseAndEmitChunkReady();\n        };\n        this.#request.on('row', rowListener);\n        this.#request.once('requestCompleted', () => {\n            Object.values(this.#subscribers).forEach((subscriber) => subscriber('completed'));\n            this.#request.off('row', rowListener);\n        });\n    }\n    #getTediousDataType(value) {\n        if (isNull(value) || isUndefined(value) || isString(value)) {\n            return this.#tedious.TYPES.NVarChar;\n        }\n        if (isBigInt(value) || (isNumber(value) && value % 1 === 0)) {\n            if (value < -2147483648 || value > 2147483647) {\n                return this.#tedious.TYPES.BigInt;\n            }\n            else {\n                return this.#tedious.TYPES.Int;\n            }\n        }\n        if (isNumber(value)) {\n            return this.#tedious.TYPES.Float;\n        }\n        if (isBoolean(value)) {\n            return this.#tedious.TYPES.Bit;\n        }\n        if (isDate(value)) {\n            return this.#tedious.TYPES.DateTime;\n        }\n        if (isBuffer(value)) {\n            return this.#tedious.TYPES.VarBinary;\n        }\n        return this.#tedious.TYPES.NVarChar;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM,uBAAuB;AAC7B,MAAM,yBAAyB;AACxB,MAAM;IACT,CAAA,MAAO,CAAC;IACR,CAAA,IAAK,CAAC;IACN,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YAAE,GAAG,MAAM;QAAC;QAClC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,GAAG,IAAI,CAAC,CAAA,MAAO;QAC3D,MAAM,EAAE,qBAAqB,6BAA6B,EAAE,GAAG,aAAa,GAAG,KAAK,OAAO;QAC3F,IAAI,CAAC,CAAA,IAAK,GAAG,IAAI,KAAK,IAAI,CAAC;YACvB,GAAG,WAAW;YACd,QAAQ;gBACJ,MAAM,aAAa,MAAM,QAAQ,iBAAiB;gBAClD,OAAO,MAAM,IAAI,gBAAgB,YAAY,SAAS,OAAO;YACjE;YACA,SAAS,OAAO;gBACZ,MAAM,UAAU,CAAC,uBAAuB;YAC5C;YACA,wEAAwE;YACxE,gDAAgD;YAChD,UAAU,wBAAwB,SAC9B,kCAAkC,QAChC,YACA,CAAC,aAAe,WAAW,QAAQ;QAC7C;IACJ;IACA,MAAM,OAAO;IACT,OAAO;IACX;IACA,MAAM,oBAAoB;QACtB,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,OAAO,GAAG,OAAO;IAC7C;IACA,MAAM,iBAAiB,UAAU,EAAE,QAAQ,EAAE;QACzC,MAAM,WAAW,gBAAgB,CAAC;IACtC;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,MAAM,WAAW,iBAAiB;IACtC;IACA,MAAM,oBAAoB,UAAU,EAAE;QAClC,MAAM,WAAW,mBAAmB;IACxC;IACA,MAAM,UAAU,UAAU,EAAE,aAAa,EAAE;QACvC,MAAM,WAAW,SAAS,CAAC;IAC/B;IACA,MAAM,oBAAoB,UAAU,EAAE,aAAa,EAAE;QACjD,MAAM,WAAW,mBAAmB,CAAC;IACzC;IACA,MAAM,kBAAkB,UAAU,EAAE;QAChC,IAAI,IAAI,CAAC,CAAA,MAAO,CAAC,yBAAyB,IACtC,IAAI,CAAC,CAAA,MAAO,CAAC,OAAO,CAAC,wBAAwB,EAAE;YAC/C,MAAM,UAAU,CAAC,qBAAqB;QAC1C;QACA,IAAI,CAAC,CAAA,IAAK,CAAC,OAAO,CAAC;IACvB;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,OAAO;IAC5B;AACJ;AACA,MAAM;IACF,CAAA,UAAW,CAAC;IACZ,CAAA,OAAQ,CAAC;IACT,YAAY,UAAU,EAAE,OAAO,CAAE;QAC7B,IAAI,CAAC,CAAA,UAAW,GAAG;QACnB,IAAI,CAAC,CAAA,OAAQ,GAAG;QAChB,IAAI,CAAC,CAAA,UAAW,CAAC,EAAE,CAAC,SAAS,QAAQ,KAAK;QAC1C,IAAI,CAAC,CAAA,UAAW,CAAC,IAAI,CAAC,OAAO;YACzB,IAAI,CAAC,CAAA,UAAW,CAAC,GAAG,CAAC,SAAS,QAAQ,KAAK;QAC/C;IACJ;IACA,MAAM,iBAAiB,QAAQ,EAAE;QAC7B,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,IAAI,QAAQ,CAAC,SAAS,SAAW,IAAI,CAAC,CAAA,UAAW,CAAC,gBAAgB,CAAC,CAAC;gBACtE,IAAI,OACA,OAAO;qBAEP,QAAQ;YAChB,GAAG,iBAAiB,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE,KAAK,WAAW,iBAC3C,IAAI,CAAC,CAAA,wBAAyB,CAAC,kBAC/B;IACV;IACA,MAAM,oBAAoB;QACtB,MAAM,IAAI,QAAQ,CAAC,SAAS,SAAW,IAAI,CAAC,CAAA,UAAW,CAAC,iBAAiB,CAAC,CAAC;gBACvE,IAAI,OACA,OAAO;qBAEP,QAAQ;YAChB;IACJ;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,QAAQ,CAAC,SAAS;YACxB,IAAI,CAAC,CAAA,UAAW,CAAC,OAAO,CAAC,CAAC;gBACtB,IAAI,OAAO;oBACP,QAAQ,KAAK,CAAC;oBACd,OAAO;gBACX,OACK;oBACD,QAAQ;gBACZ;YACJ;QACJ;QACA,OAAO,IAAI;IACf;IACA,MAAM,aAAa,aAAa,EAAE;QAC9B,IAAI;YACA,MAAM,WAAW,IAAI,+MAAA,CAAA,WAAQ;YAC7B,MAAM,UAAU,IAAI,aAAa;gBAC7B;gBACA,SAAS,IAAI,CAAC,CAAA,OAAQ;gBACtB,QAAQ;YACZ;YACA,IAAI,CAAC,CAAA,UAAW,CAAC,OAAO,CAAC,QAAQ,OAAO;YACxC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,SAAS,OAAO;YACjD,OAAO;gBACH,iBAAiB,aAAa,YAAY,OAAO,YAAY;gBAC7D;YACJ;QACJ,EACA,OAAO,KAAK;YACR,MAAM,CAAA,GAAA,8NAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;QACpC;IACJ;IACA,MAAM,oBAAoB,aAAa,EAAE;QACrC,MAAM,IAAI,QAAQ,CAAC,SAAS,SAAW,IAAI,CAAC,CAAA,UAAW,CAAC,mBAAmB,CAAC,CAAC;gBACzE,IAAI,OACA,OAAO;qBAEP,QAAQ;YAChB,GAAG;IACP;IACA,MAAM,UAAU,aAAa,EAAE;QAC3B,MAAM,IAAI,QAAQ,CAAC,SAAS,SAAW,IAAI,CAAC,CAAA,UAAW,CAAC,eAAe,CAAC,CAAC;gBACrE,IAAI,OACA,OAAO;qBAEP,QAAQ;YAChB,GAAG;IACP;IACA,OAAO,YAAY,aAAa,EAAE,SAAS,EAAE;QACzC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,aAAa,GAAG;YAChD,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,UAAU,IAAI,aAAa;YAC7B;YACA,iBAAiB;YACjB,SAAS,IAAI,CAAC,CAAA,OAAQ;QAC1B;QACA,IAAI,CAAC,CAAA,UAAW,CAAC,OAAO,CAAC,QAAQ,OAAO;QACxC,IAAI;YACA,MAAO,KAAM;gBACT,MAAM,OAAO,MAAM,QAAQ,SAAS;gBACpC,IAAI,KAAK,MAAM,KAAK,GAAG;oBACnB;gBACJ;gBACA,MAAM;oBAAE;gBAAK;gBACb,IAAI,KAAK,MAAM,GAAG,WAAW;oBACzB;gBACJ;YACJ;QACJ,SACQ;YACJ,MAAM,IAAI,CAAC,CAAA,aAAc,CAAC;QAC9B;IACJ;IACA,MAAM,WAAW;QACb,IAAI;YACA,MAAM,WAAW,IAAI,+MAAA,CAAA,WAAQ;YAC7B,MAAM,UAAU,IAAI,aAAa;gBAC7B,eAAe,qOAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;gBACjC,QAAQ;gBACR,SAAS,IAAI,CAAC,CAAA,OAAQ;YAC1B;YACA,IAAI,CAAC,CAAA,UAAW,CAAC,OAAO,CAAC,QAAQ,OAAO;YACxC,MAAM,SAAS,OAAO;YACtB,OAAO;QACX,EACA,OAAM;YACF,OAAO;QACX;IACJ;IACA,CAAA,wBAAyB,CAAC,cAAc;QACpC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,CAAA,OAAQ;QACzC,MAAM,SAAS;YACX,kBAAkB,gBAAgB,cAAc;YAChD,oBAAoB,gBAAgB,gBAAgB;YACpD,mBAAmB,gBAAgB,eAAe;YAClD,cAAc,gBAAgB,YAAY;YAC1C,UAAU,gBAAgB,QAAQ;QACtC;QACA,MAAM,wBAAwB,MAAM,CAAC,eAAe;QACpD,IAAI,0BAA0B,WAAW;YACrC,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,gBAAgB;QAChE;QACA,OAAO;IACX;IACA,CAAA,aAAc,CAAC,OAAO;QAClB,OAAO,IAAI,QAAQ,CAAC;YAChB,QAAQ,OAAO,CAAC,IAAI,CAAC,oBAAoB;YACzC,MAAM,cAAc,IAAI,CAAC,CAAA,UAAW,CAAC,MAAM;YAC3C,IAAI,CAAC,aAAa;gBACd,QAAQ,OAAO,CAAC,GAAG,CAAC,oBAAoB;gBACxC,QAAQ;YACZ;QACJ;IACJ;IACA,MAAM,CAAC,qBAAqB,GAAG;QAC3B,MAAM,IAAI,QAAQ,CAAC,SAAS;YACxB,IAAI,CAAC,CAAA,UAAW,CAAC,KAAK,CAAC,CAAC;gBACpB,IAAI,OACA,OAAO;qBAEP,QAAQ;YAChB;QACJ;IACJ;IACA,CAAC,uBAAuB,GAAG;QACvB,OAAO,IAAI,QAAQ,CAAC;YAChB,IAAI,CAAC,CAAA,UAAW,CAAC,IAAI,CAAC,OAAO;gBACzB,QAAQ;YACZ;YACA,IAAI,CAAC,CAAA,UAAW,CAAC,KAAK;QAC1B;IACJ;AACJ;AACA,MAAM;IACF,CAAA,OAAQ,CAAC;IACT,CAAA,IAAK,CAAC;IACN,CAAA,eAAgB,CAAC;IACjB,CAAA,WAAY,CAAC;IACb,CAAA,OAAQ,CAAC;IACT,CAAA,QAAS,CAAC;IACV,YAAY,KAAK,CAAE;QACf,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG;QAC5D,IAAI,CAAC,CAAA,IAAK,GAAG,EAAE;QACf,IAAI,CAAC,CAAA,eAAgB,GAAG;QACxB,IAAI,CAAC,CAAA,WAAY,GAAG,CAAC;QACrB,IAAI,CAAC,CAAA,OAAQ,GAAG;QAChB,IAAI,QAAQ;YACR,MAAM,kBAAkB;YACxB,IAAI,CAAC,CAAA,WAAY,CAAC,gBAAgB,GAAG,CAAC,OAAO;gBACzC,IAAI,UAAU,cAAc;oBACxB;gBACJ;gBACA,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC,gBAAgB;gBACzC,IAAI,UAAU,SAAS;oBACnB,OAAO,MAAM,CAAC;gBAClB,OACK;oBACD,OAAO,OAAO,CAAC;wBACX,UAAU,IAAI,CAAC,CAAA,QAAS;wBACxB,MAAM,IAAI,CAAC,CAAA,IAAK;oBACpB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG,IAAI,IAAI,CAAC,CAAA,OAAQ,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK;YAC/D,IAAI,KAAK;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,WAAY,EAAE,OAAO,CAAC,CAAC,aAAe,WAAW,SAAS,eAAe,iBAAiB,IAAI,MAAM,GAAG;YAC9H,OACK;gBACD,IAAI,CAAC,CAAA,QAAS,GAAG;YACrB;QACJ;QACA,IAAI,CAAC,CAAA,sBAAuB,CAAC,cAAc,UAAU;QACrD,IAAI,CAAC,CAAA,eAAgB;IACzB;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,CAAA,OAAQ;IACxB;IACA,YAAY;QACR,MAAM,kBAAkB,IAAI,CAAC,SAAS,CAAC,IAAI;QAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,CAAC,CAAA,WAAY,CAAC,gBAAgB,GAAG,CAAC,OAAO;gBACzC,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC,gBAAgB;gBACzC,IAAI,UAAU,SAAS;oBACnB,OAAO;gBACX,OACK;oBACD,QAAQ,IAAI,CAAC,CAAA,IAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAA,eAAgB;gBACtD;YACJ;YACA,IAAI,CAAC,CAAA,OAAQ,CAAC,MAAM;QACxB;IACJ;IACA,CAAA,sBAAuB,CAAC,UAAU;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,MAAM,YAAY,UAAU,CAAC,EAAE;YAC/B,IAAI,CAAC,CAAA,OAAQ,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAA,kBAAmB,CAAC,YAAY;QACnF;IACJ;IACA,CAAA,eAAgB;QACZ,MAAM,yBAAyB,IAAI,CAAC,CAAA,eAAgB,GAC9C;YACE,IAAI,IAAI,CAAC,CAAA,eAAgB,IAAI,IAAI,CAAC,CAAA,IAAK,CAAC,MAAM,EAAE;gBAC5C,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK;gBACnB,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,WAAY,EAAE,OAAO,CAAC,CAAC,aAAe,WAAW;YACxE;QACJ,IACE,KAAQ;QACd,MAAM,cAAc,CAAC;YACjB,MAAM,MAAM,CAAC;YACb,KAAK,MAAM,UAAU,QAAS;gBAC1B,GAAG,CAAC,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,OAAO,KAAK;YAC/C;YACA,IAAI,CAAC,CAAA,IAAK,CAAC,IAAI,CAAC;YAChB;QACJ;QACA,IAAI,CAAC,CAAA,OAAQ,CAAC,EAAE,CAAC,OAAO;QACxB,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAI,CAAC,oBAAoB;YACnC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA,WAAY,EAAE,OAAO,CAAC,CAAC,aAAe,WAAW;YACpE,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,CAAC,OAAO;QAC7B;IACJ;IACA,CAAA,kBAAmB,CAAC,KAAK;QACrB,IAAI,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACxD,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,QAAQ;QACvC;QACA,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,UAAW,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,QAAQ,MAAM,GAAI;YACzD,IAAI,QAAQ,CAAC,cAAc,QAAQ,YAAY;gBAC3C,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,MAAM;YACrC,OACK;gBACD,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,GAAG;YAClC;QACJ;QACA,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACjB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,KAAK;QACpC;QACA,IAAI,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAClB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,GAAG;QAClC;QACA,IAAI,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACf,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,QAAQ;QACvC;QACA,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACjB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,SAAS;QACxC;QACA,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,CAAC,QAAQ;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6183, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mssql/mssql-introspector.js"], "sourcesContent": ["/// <reference types=\"./mssql-introspector.d.ts\" />\nimport { DEFAULT_MIGRATION_LOCK_TABLE, DEFAULT_MIGRATION_TABLE, } from '../../migration/migrator.js';\nimport { freeze } from '../../util/object-utils.js';\nexport class MssqlIntrospector {\n    #db;\n    constructor(db) {\n        this.#db = db;\n    }\n    async getSchemas() {\n        return await this.#db.selectFrom('sys.schemas').select('name').execute();\n    }\n    async getTables(options = { withInternalKyselyTables: false }) {\n        const rawColumns = await this.#db\n            .selectFrom('sys.tables as tables')\n            .leftJoin('sys.schemas as table_schemas', 'table_schemas.schema_id', 'tables.schema_id')\n            .innerJoin('sys.columns as columns', 'columns.object_id', 'tables.object_id')\n            .innerJoin('sys.types as types', 'types.user_type_id', 'columns.user_type_id')\n            .leftJoin('sys.schemas as type_schemas', 'type_schemas.schema_id', 'types.schema_id')\n            .leftJoin('sys.extended_properties as comments', (join) => join\n            .onRef('comments.major_id', '=', 'tables.object_id')\n            .onRef('comments.minor_id', '=', 'columns.column_id')\n            .on('comments.name', '=', 'MS_Description'))\n            .$if(!options.withInternalKyselyTables, (qb) => qb\n            .where('tables.name', '!=', DEFAULT_MIGRATION_TABLE)\n            .where('tables.name', '!=', DEFAULT_MIGRATION_LOCK_TABLE))\n            .select([\n            'tables.name as table_name',\n            (eb) => eb\n                .ref('tables.type')\n                .$castTo()\n                .as('table_type'),\n            'table_schemas.name as table_schema_name',\n            'columns.default_object_id as column_default_object_id',\n            'columns.generated_always_type_desc as column_generated_always_type',\n            'columns.is_computed as column_is_computed',\n            'columns.is_identity as column_is_identity',\n            'columns.is_nullable as column_is_nullable',\n            'columns.is_rowguidcol as column_is_rowguidcol',\n            'columns.name as column_name',\n            'types.is_nullable as type_is_nullable',\n            'types.name as type_name',\n            'type_schemas.name as type_schema_name',\n            'comments.value as column_comment',\n        ])\n            .unionAll(this.#db\n            .selectFrom('sys.views as views')\n            .leftJoin('sys.schemas as view_schemas', 'view_schemas.schema_id', 'views.schema_id')\n            .innerJoin('sys.columns as columns', 'columns.object_id', 'views.object_id')\n            .innerJoin('sys.types as types', 'types.user_type_id', 'columns.user_type_id')\n            .leftJoin('sys.schemas as type_schemas', 'type_schemas.schema_id', 'types.schema_id')\n            .leftJoin('sys.extended_properties as comments', (join) => join\n            .onRef('comments.major_id', '=', 'views.object_id')\n            .onRef('comments.minor_id', '=', 'columns.column_id')\n            .on('comments.name', '=', 'MS_Description'))\n            .select([\n            'views.name as table_name',\n            'views.type as table_type',\n            'view_schemas.name as table_schema_name',\n            'columns.default_object_id as column_default_object_id',\n            'columns.generated_always_type_desc as column_generated_always_type',\n            'columns.is_computed as column_is_computed',\n            'columns.is_identity as column_is_identity',\n            'columns.is_nullable as column_is_nullable',\n            'columns.is_rowguidcol as column_is_rowguidcol',\n            'columns.name as column_name',\n            'types.is_nullable as type_is_nullable',\n            'types.name as type_name',\n            'type_schemas.name as type_schema_name',\n            'comments.value as column_comment',\n        ]))\n            .orderBy('table_schema_name')\n            .orderBy('table_name')\n            .orderBy('column_name')\n            .execute();\n        const tableDictionary = {};\n        for (const rawColumn of rawColumns) {\n            const key = `${rawColumn.table_schema_name}.${rawColumn.table_name}`;\n            const table = (tableDictionary[key] =\n                tableDictionary[key] ||\n                    freeze({\n                        columns: [],\n                        isView: rawColumn.table_type === 'V ',\n                        name: rawColumn.table_name,\n                        schema: rawColumn.table_schema_name ?? undefined,\n                    }));\n            table.columns.push(freeze({\n                dataType: rawColumn.type_name,\n                dataTypeSchema: rawColumn.type_schema_name ?? undefined,\n                hasDefaultValue: rawColumn.column_default_object_id > 0 ||\n                    rawColumn.column_generated_always_type !== 'NOT_APPLICABLE' ||\n                    rawColumn.column_is_identity ||\n                    rawColumn.column_is_computed ||\n                    rawColumn.column_is_rowguidcol,\n                isAutoIncrementing: rawColumn.column_is_identity,\n                isNullable: rawColumn.column_is_nullable && rawColumn.type_is_nullable,\n                name: rawColumn.column_name,\n                comment: rawColumn.column_comment ?? undefined,\n            }));\n        }\n        return Object.values(tableDictionary);\n    }\n    async getMetadata(options) {\n        return {\n            tables: await this.getTables(options),\n        };\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;AACA;;;AACO,MAAM;IACT,CAAA,EAAG,CAAC;IACJ,YAAY,EAAE,CAAE;QACZ,IAAI,CAAC,CAAA,EAAG,GAAG;IACf;IACA,MAAM,aAAa;QACf,OAAO,MAAM,IAAI,CAAC,CAAA,EAAG,CAAC,UAAU,CAAC,eAAe,MAAM,CAAC,QAAQ,OAAO;IAC1E;IACA,MAAM,UAAU,UAAU;QAAE,0BAA0B;IAAM,CAAC,EAAE;QAC3D,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,EAAG,CAC5B,UAAU,CAAC,wBACX,QAAQ,CAAC,gCAAgC,2BAA2B,oBACpE,SAAS,CAAC,0BAA0B,qBAAqB,oBACzD,SAAS,CAAC,sBAAsB,sBAAsB,wBACtD,QAAQ,CAAC,+BAA+B,0BAA0B,mBAClE,QAAQ,CAAC,uCAAuC,CAAC,OAAS,KAC1D,KAAK,CAAC,qBAAqB,KAAK,oBAChC,KAAK,CAAC,qBAAqB,KAAK,qBAChC,EAAE,CAAC,iBAAiB,KAAK,mBACzB,GAAG,CAAC,CAAC,QAAQ,wBAAwB,EAAE,CAAC,KAAO,GAC/C,KAAK,CAAC,eAAe,MAAM,oNAAA,CAAA,0BAAuB,EAClD,KAAK,CAAC,eAAe,MAAM,oNAAA,CAAA,+BAA4B,GACvD,MAAM,CAAC;YACR;YACA,CAAC,KAAO,GACH,GAAG,CAAC,eACJ,OAAO,GACP,EAAE,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACI,QAAQ,CAAC,IAAI,CAAC,CAAA,EAAG,CACjB,UAAU,CAAC,sBACX,QAAQ,CAAC,+BAA+B,0BAA0B,mBAClE,SAAS,CAAC,0BAA0B,qBAAqB,mBACzD,SAAS,CAAC,sBAAsB,sBAAsB,wBACtD,QAAQ,CAAC,+BAA+B,0BAA0B,mBAClE,QAAQ,CAAC,uCAAuC,CAAC,OAAS,KAC1D,KAAK,CAAC,qBAAqB,KAAK,mBAChC,KAAK,CAAC,qBAAqB,KAAK,qBAChC,EAAE,CAAC,iBAAiB,KAAK,mBACzB,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,GACI,OAAO,CAAC,qBACR,OAAO,CAAC,cACR,OAAO,CAAC,eACR,OAAO;QACZ,MAAM,kBAAkB,CAAC;QACzB,KAAK,MAAM,aAAa,WAAY;YAChC,MAAM,MAAM,GAAG,UAAU,iBAAiB,CAAC,CAAC,EAAE,UAAU,UAAU,EAAE;YACpE,MAAM,QAAS,eAAe,CAAC,IAAI,GAC/B,eAAe,CAAC,IAAI,IAChB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBACH,SAAS,EAAE;gBACX,QAAQ,UAAU,UAAU,KAAK;gBACjC,MAAM,UAAU,UAAU;gBAC1B,QAAQ,UAAU,iBAAiB,IAAI;YAC3C;YACR,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBACtB,UAAU,UAAU,SAAS;gBAC7B,gBAAgB,UAAU,gBAAgB,IAAI;gBAC9C,iBAAiB,UAAU,wBAAwB,GAAG,KAClD,UAAU,4BAA4B,KAAK,oBAC3C,UAAU,kBAAkB,IAC5B,UAAU,kBAAkB,IAC5B,UAAU,oBAAoB;gBAClC,oBAAoB,UAAU,kBAAkB;gBAChD,YAAY,UAAU,kBAAkB,IAAI,UAAU,gBAAgB;gBACtE,MAAM,UAAU,WAAW;gBAC3B,SAAS,UAAU,cAAc,IAAI;YACzC;QACJ;QACA,OAAO,OAAO,MAAM,CAAC;IACzB;IACA,MAAM,YAAY,OAAO,EAAE;QACvB,OAAO;YACH,QAAQ,MAAM,IAAI,CAAC,SAAS,CAAC;QACjC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6266, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mssql/mssql-query-compiler.js"], "sourcesContent": ["/// <reference types=\"./mssql-query-compiler.d.ts\" />\nimport { DefaultQueryCompiler } from '../../query-compiler/default-query-compiler.js';\nconst COLLATION_CHAR_REGEX = /^[a-z0-9_]$/i;\nexport class MssqlQueryCompiler extends DefaultQueryCompiler {\n    getCurrentParameterPlaceholder() {\n        return `@${this.numParameters}`;\n    }\n    visitOffset(node) {\n        super.visitOffset(node);\n        this.append(' rows');\n    }\n    // mssql allows multi-column alterations in a single statement,\n    // but you can only use the command keyword/s once.\n    // it also doesn't support multiple kinds of commands in the same\n    // alter table statement, but we compile that anyway for the sake\n    // of WYSIWYG.\n    compileColumnAlterations(columnAlterations) {\n        const nodesByKind = {};\n        for (const columnAlteration of columnAlterations) {\n            if (!nodesByKind[columnAlteration.kind]) {\n                nodesByKind[columnAlteration.kind] = [];\n            }\n            nodesByKind[columnAlteration.kind].push(columnAlteration);\n        }\n        let first = true;\n        if (nodesByKind.AddColumnNode) {\n            this.append('add ');\n            this.compileList(nodesByKind.AddColumnNode);\n            first = false;\n        }\n        // multiple of these are not really supported by mssql,\n        // but for the sake of WYSIWYG.\n        if (nodesByKind.AlterColumnNode) {\n            if (!first)\n                this.append(', ');\n            this.compileList(nodesByKind.AlterColumnNode);\n        }\n        if (nodesByKind.DropColumnNode) {\n            if (!first)\n                this.append(', ');\n            this.append('drop column ');\n            this.compileList(nodesByKind.DropColumnNode);\n        }\n        // not really supported by mssql, but for the sake of WYSIWYG.\n        if (nodesByKind.ModifyColumnNode) {\n            if (!first)\n                this.append(', ');\n            this.compileList(nodesByKind.ModifyColumnNode);\n        }\n        // not really supported by mssql, but for the sake of WYSIWYG.\n        if (nodesByKind.RenameColumnNode) {\n            if (!first)\n                this.append(', ');\n            this.compileList(nodesByKind.RenameColumnNode);\n        }\n    }\n    visitAddColumn(node) {\n        this.visitNode(node.column);\n    }\n    visitDropColumn(node) {\n        this.visitNode(node.column);\n    }\n    visitMergeQuery(node) {\n        super.visitMergeQuery(node);\n        this.append(';');\n    }\n    visitCollate(node) {\n        this.append('collate ');\n        const { name } = node.collation;\n        for (const char of name) {\n            if (!COLLATION_CHAR_REGEX.test(char)) {\n                throw new Error(`Invalid collation: ${name}`);\n            }\n        }\n        this.append(name);\n    }\n    announcesNewColumnDataType() {\n        return false;\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;;AACA,MAAM,uBAAuB;AACtB,MAAM,2BAA2B,gPAAA,CAAA,uBAAoB;IACxD,iCAAiC;QAC7B,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE;IACnC;IACA,YAAY,IAAI,EAAE;QACd,KAAK,CAAC,YAAY;QAClB,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,+DAA+D;IAC/D,mDAAmD;IACnD,iEAAiE;IACjE,iEAAiE;IACjE,cAAc;IACd,yBAAyB,iBAAiB,EAAE;QACxC,MAAM,cAAc,CAAC;QACrB,KAAK,MAAM,oBAAoB,kBAAmB;YAC9C,IAAI,CAAC,WAAW,CAAC,iBAAiB,IAAI,CAAC,EAAE;gBACrC,WAAW,CAAC,iBAAiB,IAAI,CAAC,GAAG,EAAE;YAC3C;YACA,WAAW,CAAC,iBAAiB,IAAI,CAAC,CAAC,IAAI,CAAC;QAC5C;QACA,IAAI,QAAQ;QACZ,IAAI,YAAY,aAAa,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,YAAY,aAAa;YAC1C,QAAQ;QACZ;QACA,uDAAuD;QACvD,+BAA+B;QAC/B,IAAI,YAAY,eAAe,EAAE;YAC7B,IAAI,CAAC,OACD,IAAI,CAAC,MAAM,CAAC;YAChB,IAAI,CAAC,WAAW,CAAC,YAAY,eAAe;QAChD;QACA,IAAI,YAAY,cAAc,EAAE;YAC5B,IAAI,CAAC,OACD,IAAI,CAAC,MAAM,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,YAAY,cAAc;QAC/C;QACA,8DAA8D;QAC9D,IAAI,YAAY,gBAAgB,EAAE;YAC9B,IAAI,CAAC,OACD,IAAI,CAAC,MAAM,CAAC;YAChB,IAAI,CAAC,WAAW,CAAC,YAAY,gBAAgB;QACjD;QACA,8DAA8D;QAC9D,IAAI,YAAY,gBAAgB,EAAE;YAC9B,IAAI,CAAC,OACD,IAAI,CAAC,MAAM,CAAC;YAChB,IAAI,CAAC,WAAW,CAAC,YAAY,gBAAgB;QACjD;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,gBAAgB,IAAI,EAAE;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAC9B;IACA,gBAAgB,IAAI,EAAE;QAClB,KAAK,CAAC,gBAAgB;QACtB,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,aAAa,IAAI,EAAE;QACf,IAAI,CAAC,MAAM,CAAC;QACZ,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,SAAS;QAC/B,KAAK,MAAM,QAAQ,KAAM;YACrB,IAAI,CAAC,qBAAqB,IAAI,CAAC,OAAO;gBAClC,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,MAAM;YAChD;QACJ;QACA,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,6BAA6B;QACzB,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6352, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/dialect/mssql/mssql-dialect.js"], "sourcesContent": ["/// <reference types=\"./mssql-dialect.d.ts\" />\nimport { MssqlAdapter } from './mssql-adapter.js';\nimport { MssqlDriver } from './mssql-driver.js';\nimport { MssqlIntrospector } from './mssql-introspector.js';\nimport { MssqlQueryCompiler } from './mssql-query-compiler.js';\n/**\n * MS SQL Server dialect that uses the [tedious](https://tediousjs.github.io/tedious)\n * library.\n *\n * The constructor takes an instance of {@link MssqlDialectConfig}.\n *\n * ```ts\n * import * as Tedious from 'tedious'\n * import * as Tarn from 'tarn'\n *\n * const dialect = new MssqlDialect({\n *   tarn: {\n *     ...Tarn,\n *     options: {\n *       min: 0,\n *       max: 10,\n *     },\n *   },\n *   tedious: {\n *     ...Tedious,\n *     connectionFactory: () => new Tedious.Connection({\n *       authentication: {\n *         options: {\n *           password: 'password',\n *           userName: 'username',\n *         },\n *         type: 'default',\n *       },\n *       options: {\n *         database: 'some_db',\n *         port: 1433,\n *         trustServerCertificate: true,\n *       },\n *       server: 'localhost',\n *     }),\n *   },\n * })\n * ```\n */\nexport class MssqlDialect {\n    #config;\n    constructor(config) {\n        this.#config = config;\n    }\n    createDriver() {\n        return new MssqlDriver(this.#config);\n    }\n    createQueryCompiler() {\n        return new MssqlQueryCompiler();\n    }\n    createAdapter() {\n        return new MssqlAdapter();\n    }\n    createIntrospector(db) {\n        return new MssqlIntrospector(db);\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C;AACA;AACA;AACA;;;;;AAwCO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,eAAe;QACX,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,MAAO;IACvC;IACA,sBAAsB;QAClB,OAAO,IAAI,6OAAA,CAAA,qBAAkB;IACjC;IACA,gBAAgB;QACZ,OAAO,IAAI,mOAAA,CAAA,eAAY;IAC3B;IACA,mBAAmB,EAAE,EAAE;QACnB,OAAO,IAAI,wOAAA,CAAA,oBAAiB,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/migration/migrator.js"], "sourcesContent": ["/// <reference types=\"./migrator.d.ts\" />\nimport { NoopPlugin } from '../plugin/noop-plugin.js';\nimport { WithSchemaPlugin } from '../plugin/with-schema/with-schema-plugin.js';\nimport { freeze, getLast } from '../util/object-utils.js';\nexport const DEFAULT_MIGRATION_TABLE = 'kysely_migration';\nexport const DEFAULT_MIGRATION_LOCK_TABLE = 'kysely_migration_lock';\nexport const DEFAULT_ALLOW_UNORDERED_MIGRATIONS = false;\nexport const MIGRATION_LOCK_ID = 'migration_lock';\nexport const NO_MIGRATIONS = freeze({ __noMigrations__: true });\n/**\n * A class for running migrations.\n *\n * ### Example\n *\n * This example uses the {@link FileMigrationProvider} that reads migrations\n * files from a single folder. You can easily implement your own\n * {@link MigrationProvider} if you want to provide migrations some\n * other way.\n *\n * ```ts\n * import { promises as fs } from 'node:fs'\n * import path from 'node:path'\n * import * as Sqlite from 'better-sqlite3'\n * import {\n *   FileMigrationProvider,\n *   Kysely,\n *   Migrator,\n *   SqliteDialect\n * } from 'kysely'\n *\n * const db = new Kysely<any>({\n *   dialect: new SqliteDialect({\n *     database: Sqlite(':memory:')\n *   })\n * })\n *\n * const migrator = new Migrator({\n *   db,\n *   provider: new FileMigrationProvider({\n *     fs,\n *     // Path to the folder that contains all your migrations.\n *     migrationFolder: 'some/path/to/migrations',\n *     path,\n *   })\n * })\n * ```\n */\nexport class Migrator {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Returns a {@link MigrationInfo} object for each migration.\n     *\n     * The returned array is sorted by migration name.\n     */\n    async getMigrations() {\n        const executedMigrations = (await this.#doesTableExists(this.#migrationTable))\n            ? await this.#props.db\n                .withPlugin(this.#schemaPlugin)\n                .selectFrom(this.#migrationTable)\n                .select(['name', 'timestamp'])\n                .$narrowType()\n                .execute()\n            : [];\n        const migrations = await this.#resolveMigrations();\n        return migrations.map(({ name, ...migration }) => {\n            const executed = executedMigrations.find((it) => it.name === name);\n            return {\n                name,\n                migration,\n                executedAt: executed ? new Date(executed.timestamp) : undefined,\n            };\n        });\n    }\n    /**\n     * Runs all migrations that have not yet been run.\n     *\n     * This method returns a {@link MigrationResultSet} instance and _never_ throws.\n     * {@link MigrationResultSet.error} holds the error if something went wrong.\n     * {@link MigrationResultSet.results} contains information about which migrations\n     * were executed and which failed. See the examples below.\n     *\n     * This method goes through all possible migrations provided by the provider and runs the\n     * ones whose names come alphabetically after the last migration that has been run. If the\n     * list of executed migrations doesn't match the beginning of the list of possible migrations\n     * an error is returned.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { promises as fs } from 'node:fs'\n     * import path from 'node:path'\n     * import * as Sqlite from 'better-sqlite3'\n     * import { FileMigrationProvider, Migrator } from 'kysely'\n     *\n     * const migrator = new Migrator({\n     *   db,\n     *   provider: new FileMigrationProvider({\n     *     fs,\n     *     migrationFolder: 'some/path/to/migrations',\n     *     path,\n     *   })\n     * })\n     *\n     * const { error, results } = await migrator.migrateToLatest()\n     *\n     * results?.forEach((it) => {\n     *   if (it.status === 'Success') {\n     *     console.log(`migration \"${it.migrationName}\" was executed successfully`)\n     *   } else if (it.status === 'Error') {\n     *     console.error(`failed to execute migration \"${it.migrationName}\"`)\n     *   }\n     * })\n     *\n     * if (error) {\n     *   console.error('failed to run `migrateToLatest`')\n     *   console.error(error)\n     * }\n     * ```\n     */\n    async migrateToLatest() {\n        return this.#migrate(() => ({ direction: 'Up', step: Infinity }));\n    }\n    /**\n     * Migrate up/down to a specific migration.\n     *\n     * This method returns a {@link MigrationResultSet} instance and _never_ throws.\n     * {@link MigrationResultSet.error} holds the error if something went wrong.\n     * {@link MigrationResultSet.results} contains information about which migrations\n     * were executed and which failed.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { promises as fs } from 'node:fs'\n     * import path from 'node:path'\n     * import { FileMigrationProvider, Migrator } from 'kysely'\n     *\n     * const migrator = new Migrator({\n     *   db,\n     *   provider: new FileMigrationProvider({\n     *     fs,\n     *     // Path to the folder that contains all your migrations.\n     *     migrationFolder: 'some/path/to/migrations',\n     *     path,\n     *   })\n     * })\n     *\n     * await migrator.migrateTo('some_migration')\n     * ```\n     *\n     * If you specify the name of the first migration, this method migrates\n     * down to the first migration, but doesn't run the `down` method of\n     * the first migration. In case you want to migrate all the way down,\n     * you can use a special constant `NO_MIGRATIONS`:\n     *\n     * ```ts\n     * import { promises as fs } from 'node:fs'\n     * import path from 'node:path'\n     * import { FileMigrationProvider, Migrator, NO_MIGRATIONS } from 'kysely'\n     *\n     * const migrator = new Migrator({\n     *   db,\n     *   provider: new FileMigrationProvider({\n     *     fs,\n     *     // Path to the folder that contains all your migrations.\n     *     migrationFolder: 'some/path/to/migrations',\n     *     path,\n     *   })\n     * })\n     *\n     * await migrator.migrateTo(NO_MIGRATIONS)\n     * ```\n     */\n    async migrateTo(targetMigrationName) {\n        return this.#migrate(({ migrations, executedMigrations, pendingMigrations, }) => {\n            if (targetMigrationName === NO_MIGRATIONS) {\n                return { direction: 'Down', step: Infinity };\n            }\n            if (!migrations.find((m) => m.name === targetMigrationName)) {\n                throw new Error(`migration \"${targetMigrationName}\" doesn't exist`);\n            }\n            const executedIndex = executedMigrations.indexOf(targetMigrationName);\n            const pendingIndex = pendingMigrations.findIndex((m) => m.name === targetMigrationName);\n            if (executedIndex !== -1) {\n                return {\n                    direction: 'Down',\n                    step: executedMigrations.length - executedIndex - 1,\n                };\n            }\n            else if (pendingIndex !== -1) {\n                return { direction: 'Up', step: pendingIndex + 1 };\n            }\n            else {\n                throw new Error(`migration \"${targetMigrationName}\" isn't executed or pending`);\n            }\n        });\n    }\n    /**\n     * Migrate one step up.\n     *\n     * This method returns a {@link MigrationResultSet} instance and _never_ throws.\n     * {@link MigrationResultSet.error} holds the error if something went wrong.\n     * {@link MigrationResultSet.results} contains information about which migrations\n     * were executed and which failed.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { promises as fs } from 'node:fs'\n     * import path from 'node:path'\n     * import { FileMigrationProvider, Migrator } from 'kysely'\n     *\n     * const migrator = new Migrator({\n     *   db,\n     *   provider: new FileMigrationProvider({\n     *     fs,\n     *     // Path to the folder that contains all your migrations.\n     *     migrationFolder: 'some/path/to/migrations',\n     *     path,\n     *   })\n     * })\n     *\n     * await migrator.migrateUp()\n     * ```\n     */\n    async migrateUp() {\n        return this.#migrate(() => ({ direction: 'Up', step: 1 }));\n    }\n    /**\n     * Migrate one step down.\n     *\n     * This method returns a {@link MigrationResultSet} instance and _never_ throws.\n     * {@link MigrationResultSet.error} holds the error if something went wrong.\n     * {@link MigrationResultSet.results} contains information about which migrations\n     * were executed and which failed.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { promises as fs } from 'node:fs'\n     * import path from 'node:path'\n     * import { FileMigrationProvider, Migrator } from 'kysely'\n     *\n     * const migrator = new Migrator({\n     *   db,\n     *   provider: new FileMigrationProvider({\n     *     fs,\n     *     // Path to the folder that contains all your migrations.\n     *     migrationFolder: 'some/path/to/migrations',\n     *     path,\n     *   })\n     * })\n     *\n     * await migrator.migrateDown()\n     * ```\n     */\n    async migrateDown() {\n        return this.#migrate(() => ({ direction: 'Down', step: 1 }));\n    }\n    async #migrate(getMigrationDirectionAndStep) {\n        try {\n            await this.#ensureMigrationTablesExists();\n            return await this.#runMigrations(getMigrationDirectionAndStep);\n        }\n        catch (error) {\n            if (error instanceof MigrationResultSetError) {\n                return error.resultSet;\n            }\n            return { error };\n        }\n    }\n    get #migrationTableSchema() {\n        return this.#props.migrationTableSchema;\n    }\n    get #migrationTable() {\n        return this.#props.migrationTableName ?? DEFAULT_MIGRATION_TABLE;\n    }\n    get #migrationLockTable() {\n        return this.#props.migrationLockTableName ?? DEFAULT_MIGRATION_LOCK_TABLE;\n    }\n    get #allowUnorderedMigrations() {\n        return (this.#props.allowUnorderedMigrations ?? DEFAULT_ALLOW_UNORDERED_MIGRATIONS);\n    }\n    get #schemaPlugin() {\n        if (this.#migrationTableSchema) {\n            return new WithSchemaPlugin(this.#migrationTableSchema);\n        }\n        return new NoopPlugin();\n    }\n    async #ensureMigrationTablesExists() {\n        await this.#ensureMigrationTableSchemaExists();\n        await this.#ensureMigrationTableExists();\n        await this.#ensureMigrationLockTableExists();\n        await this.#ensureLockRowExists();\n    }\n    async #ensureMigrationTableSchemaExists() {\n        if (!this.#migrationTableSchema) {\n            // Use default schema. Nothing to do.\n            return;\n        }\n        if (!(await this.#doesSchemaExists())) {\n            try {\n                await this.#createIfNotExists(this.#props.db.schema.createSchema(this.#migrationTableSchema));\n            }\n            catch (error) {\n                // At least on PostgreSQL, `if not exists` doesn't guarantee the `create schema`\n                // query doesn't throw if the schema already exits. That's why we check if\n                // the schema exist here and ignore the error if it does.\n                if (!(await this.#doesSchemaExists())) {\n                    throw error;\n                }\n            }\n        }\n    }\n    async #ensureMigrationTableExists() {\n        if (!(await this.#doesTableExists(this.#migrationTable))) {\n            try {\n                if (this.#migrationTableSchema) {\n                    await this.#createIfNotExists(this.#props.db.schema.createSchema(this.#migrationTableSchema));\n                }\n                await this.#createIfNotExists(this.#props.db.schema\n                    .withPlugin(this.#schemaPlugin)\n                    .createTable(this.#migrationTable)\n                    .addColumn('name', 'varchar(255)', (col) => col.notNull().primaryKey())\n                    // The migration run time as ISO string. This is not a real date type as we\n                    // can't know which data type is supported by all future dialects.\n                    .addColumn('timestamp', 'varchar(255)', (col) => col.notNull()));\n            }\n            catch (error) {\n                // At least on PostgreSQL, `if not exists` doesn't guarantee the `create table`\n                // query doesn't throw if the table already exits. That's why we check if\n                // the table exist here and ignore the error if it does.\n                if (!(await this.#doesTableExists(this.#migrationTable))) {\n                    throw error;\n                }\n            }\n        }\n    }\n    async #ensureMigrationLockTableExists() {\n        if (!(await this.#doesTableExists(this.#migrationLockTable))) {\n            try {\n                await this.#createIfNotExists(this.#props.db.schema\n                    .withPlugin(this.#schemaPlugin)\n                    .createTable(this.#migrationLockTable)\n                    .addColumn('id', 'varchar(255)', (col) => col.notNull().primaryKey())\n                    .addColumn('is_locked', 'integer', (col) => col.notNull().defaultTo(0)));\n            }\n            catch (error) {\n                // At least on PostgreSQL, `if not exists` doesn't guarantee the `create table`\n                // query doesn't throw if the table already exits. That's why we check if\n                // the table exist here and ignore the error if it does.\n                if (!(await this.#doesTableExists(this.#migrationLockTable))) {\n                    throw error;\n                }\n            }\n        }\n    }\n    async #ensureLockRowExists() {\n        if (!(await this.#doesLockRowExists())) {\n            try {\n                await this.#props.db\n                    .withPlugin(this.#schemaPlugin)\n                    .insertInto(this.#migrationLockTable)\n                    .values({ id: MIGRATION_LOCK_ID, is_locked: 0 })\n                    .execute();\n            }\n            catch (error) {\n                if (!(await this.#doesLockRowExists())) {\n                    throw error;\n                }\n            }\n        }\n    }\n    async #doesSchemaExists() {\n        const schemas = await this.#props.db.introspection.getSchemas();\n        return schemas.some((it) => it.name === this.#migrationTableSchema);\n    }\n    async #doesTableExists(tableName) {\n        const schema = this.#migrationTableSchema;\n        const tables = await this.#props.db.introspection.getTables({\n            withInternalKyselyTables: true,\n        });\n        return tables.some((it) => it.name === tableName && (!schema || it.schema === schema));\n    }\n    async #doesLockRowExists() {\n        const lockRow = await this.#props.db\n            .withPlugin(this.#schemaPlugin)\n            .selectFrom(this.#migrationLockTable)\n            .where('id', '=', MIGRATION_LOCK_ID)\n            .select('id')\n            .executeTakeFirst();\n        return !!lockRow;\n    }\n    async #runMigrations(getMigrationDirectionAndStep) {\n        const adapter = this.#props.db.getExecutor().adapter;\n        const lockOptions = freeze({\n            lockTable: this.#props.migrationLockTableName ?? DEFAULT_MIGRATION_LOCK_TABLE,\n            lockRowId: MIGRATION_LOCK_ID,\n            lockTableSchema: this.#props.migrationTableSchema,\n        });\n        const run = async (db) => {\n            try {\n                await adapter.acquireMigrationLock(db, lockOptions);\n                const state = await this.#getState(db);\n                if (state.migrations.length === 0) {\n                    return { results: [] };\n                }\n                const { direction, step } = getMigrationDirectionAndStep(state);\n                if (step <= 0) {\n                    return { results: [] };\n                }\n                if (direction === 'Down') {\n                    return await this.#migrateDown(db, state, step);\n                }\n                else if (direction === 'Up') {\n                    return await this.#migrateUp(db, state, step);\n                }\n                return { results: [] };\n            }\n            finally {\n                await adapter.releaseMigrationLock(db, lockOptions);\n            }\n        };\n        if (adapter.supportsTransactionalDdl && !this.#props.disableTransactions) {\n            return this.#props.db.transaction().execute(run);\n        }\n        else {\n            return this.#props.db.connection().execute(run);\n        }\n    }\n    async #getState(db) {\n        const migrations = await this.#resolveMigrations();\n        const executedMigrations = await this.#getExecutedMigrations(db);\n        this.#ensureNoMissingMigrations(migrations, executedMigrations);\n        if (!this.#allowUnorderedMigrations) {\n            this.#ensureMigrationsInOrder(migrations, executedMigrations);\n        }\n        const pendingMigrations = this.#getPendingMigrations(migrations, executedMigrations);\n        return freeze({\n            migrations,\n            executedMigrations,\n            lastMigration: getLast(executedMigrations),\n            pendingMigrations,\n        });\n    }\n    #getPendingMigrations(migrations, executedMigrations) {\n        return migrations.filter((migration) => {\n            return !executedMigrations.includes(migration.name);\n        });\n    }\n    async #resolveMigrations() {\n        const allMigrations = await this.#props.provider.getMigrations();\n        return Object.keys(allMigrations)\n            .sort()\n            .map((name) => ({\n            ...allMigrations[name],\n            name,\n        }));\n    }\n    async #getExecutedMigrations(db) {\n        const executedMigrations = await db\n            .withPlugin(this.#schemaPlugin)\n            .selectFrom(this.#migrationTable)\n            .select(['name', 'timestamp'])\n            .$narrowType()\n            .execute();\n        const nameComparator = this.#props.nameComparator || ((a, b) => a.localeCompare(b));\n        return (executedMigrations\n            // https://github.com/kysely-org/kysely/issues/843\n            .sort((a, b) => {\n            if (a.timestamp === b.timestamp) {\n                return nameComparator(a.name, b.name);\n            }\n            return (new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());\n        })\n            .map((it) => it.name));\n    }\n    #ensureNoMissingMigrations(migrations, executedMigrations) {\n        // Ensure all executed migrations exist in the `migrations` list.\n        for (const executed of executedMigrations) {\n            if (!migrations.some((it) => it.name === executed)) {\n                throw new Error(`corrupted migrations: previously executed migration ${executed} is missing`);\n            }\n        }\n    }\n    #ensureMigrationsInOrder(migrations, executedMigrations) {\n        // Ensure the executed migrations are the first ones in the migration list.\n        for (let i = 0; i < executedMigrations.length; ++i) {\n            if (migrations[i].name !== executedMigrations[i]) {\n                throw new Error(`corrupted migrations: expected previously executed migration ${executedMigrations[i]} to be at index ${i} but ${migrations[i].name} was found in its place. New migrations must always have a name that comes alphabetically after the last executed migration.`);\n            }\n        }\n    }\n    async #migrateDown(db, state, step) {\n        const migrationsToRollback = state.executedMigrations\n            .slice()\n            .reverse()\n            .slice(0, step)\n            .map((name) => {\n            return state.migrations.find((it) => it.name === name);\n        });\n        const results = migrationsToRollback.map((migration) => {\n            return {\n                migrationName: migration.name,\n                direction: 'Down',\n                status: 'NotExecuted',\n            };\n        });\n        for (let i = 0; i < results.length; ++i) {\n            const migration = migrationsToRollback[i];\n            try {\n                if (migration.down) {\n                    await migration.down(db);\n                    await db\n                        .withPlugin(this.#schemaPlugin)\n                        .deleteFrom(this.#migrationTable)\n                        .where('name', '=', migration.name)\n                        .execute();\n                    results[i] = {\n                        migrationName: migration.name,\n                        direction: 'Down',\n                        status: 'Success',\n                    };\n                }\n            }\n            catch (error) {\n                results[i] = {\n                    migrationName: migration.name,\n                    direction: 'Down',\n                    status: 'Error',\n                };\n                throw new MigrationResultSetError({\n                    error,\n                    results,\n                });\n            }\n        }\n        return { results };\n    }\n    async #migrateUp(db, state, step) {\n        const migrationsToRun = state.pendingMigrations.slice(0, step);\n        const results = migrationsToRun.map((migration) => {\n            return {\n                migrationName: migration.name,\n                direction: 'Up',\n                status: 'NotExecuted',\n            };\n        });\n        for (let i = 0; i < results.length; i++) {\n            const migration = state.pendingMigrations[i];\n            try {\n                await migration.up(db);\n                await db\n                    .withPlugin(this.#schemaPlugin)\n                    .insertInto(this.#migrationTable)\n                    .values({\n                    name: migration.name,\n                    timestamp: new Date().toISOString(),\n                })\n                    .execute();\n                results[i] = {\n                    migrationName: migration.name,\n                    direction: 'Up',\n                    status: 'Success',\n                };\n            }\n            catch (error) {\n                results[i] = {\n                    migrationName: migration.name,\n                    direction: 'Up',\n                    status: 'Error',\n                };\n                throw new MigrationResultSetError({\n                    error,\n                    results,\n                });\n            }\n        }\n        return { results };\n    }\n    async #createIfNotExists(qb) {\n        if (this.#props.db.getExecutor().adapter.supportsCreateIfNotExists) {\n            qb = qb.ifNotExists();\n        }\n        await qb.execute();\n    }\n}\nclass MigrationResultSetError extends Error {\n    #resultSet;\n    constructor(result) {\n        super();\n        this.#resultSet = result;\n    }\n    get resultSet() {\n        return this.#resultSet;\n    }\n}\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;AACzC;AACA;AACA;;;;AACO,MAAM,0BAA0B;AAChC,MAAM,+BAA+B;AACrC,MAAM,qCAAqC;AAC3C,MAAM,oBAAoB;AAC1B,MAAM,gBAAgB,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IAAE,kBAAkB;AAAK;AAuCtD,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;KAIC,GACD,MAAM,gBAAgB;QAClB,MAAM,qBAAqB,AAAC,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,cAAe,IACtE,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CACjB,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,cAAe,EAC/B,MAAM,CAAC;YAAC;YAAQ;SAAY,EAC5B,WAAW,GACX,OAAO,KACV,EAAE;QACR,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,iBAAkB;QAChD,OAAO,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,WAAW;YACzC,MAAM,WAAW,mBAAmB,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK;YAC7D,OAAO;gBACH;gBACA;gBACA,YAAY,WAAW,IAAI,KAAK,SAAS,SAAS,IAAI;YAC1D;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6CC,GACD,MAAM,kBAAkB;QACpB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAM,CAAC;gBAAE,WAAW;gBAAM,MAAM;YAAS,CAAC;IACnE;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkDC,GACD,MAAM,UAAU,mBAAmB,EAAE;QACjC,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,UAAU,EAAE,kBAAkB,EAAE,iBAAiB,EAAG;YACxE,IAAI,wBAAwB,eAAe;gBACvC,OAAO;oBAAE,WAAW;oBAAQ,MAAM;gBAAS;YAC/C;YACA,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,sBAAsB;gBACzD,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,oBAAoB,eAAe,CAAC;YACtE;YACA,MAAM,gBAAgB,mBAAmB,OAAO,CAAC;YACjD,MAAM,eAAe,kBAAkB,SAAS,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;YACnE,IAAI,kBAAkB,CAAC,GAAG;gBACtB,OAAO;oBACH,WAAW;oBACX,MAAM,mBAAmB,MAAM,GAAG,gBAAgB;gBACtD;YACJ,OACK,IAAI,iBAAiB,CAAC,GAAG;gBAC1B,OAAO;oBAAE,WAAW;oBAAM,MAAM,eAAe;gBAAE;YACrD,OACK;gBACD,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,oBAAoB,2BAA2B,CAAC;YAClF;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,MAAM,YAAY;QACd,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAM,CAAC;gBAAE,WAAW;gBAAM,MAAM;YAAE,CAAC;IAC5D;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,MAAM,cAAc;QAChB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAM,CAAC;gBAAE,WAAW;gBAAQ,MAAM;YAAE,CAAC;IAC9D;IACA,MAAM,CAAA,OAAQ,CAAC,4BAA4B;QACvC,IAAI;YACA,MAAM,IAAI,CAAC,CAAA,2BAA4B;YACvC,OAAO,MAAM,IAAI,CAAC,CAAA,aAAc,CAAC;QACrC,EACA,OAAO,OAAO;YACV,IAAI,iBAAiB,yBAAyB;gBAC1C,OAAO,MAAM,SAAS;YAC1B;YACA,OAAO;gBAAE;YAAM;QACnB;IACJ;IACA,IAAI,CAAA,oBAAqB;QACrB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,oBAAoB;IAC3C;IACA,IAAI,CAAA,cAAe;QACf,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,kBAAkB,IAAI;IAC7C;IACA,IAAI,CAAA,kBAAmB;QACnB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,sBAAsB,IAAI;IACjD;IACA,IAAI,CAAA,wBAAyB;QACzB,OAAQ,IAAI,CAAC,CAAA,KAAM,CAAC,wBAAwB,IAAI;IACpD;IACA,IAAI,CAAA,YAAa;QACb,IAAI,IAAI,CAAC,CAAA,oBAAqB,EAAE;YAC5B,OAAO,IAAI,mPAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,CAAA,oBAAqB;QAC1D;QACA,OAAO,IAAI,uNAAA,CAAA,aAAU;IACzB;IACA,MAAM,CAAA,2BAA4B;QAC9B,MAAM,IAAI,CAAC,CAAA,gCAAiC;QAC5C,MAAM,IAAI,CAAC,CAAA,0BAA2B;QACtC,MAAM,IAAI,CAAC,CAAA,8BAA+B;QAC1C,MAAM,IAAI,CAAC,CAAA,mBAAoB;IACnC;IACA,MAAM,CAAA,gCAAiC;QACnC,IAAI,CAAC,IAAI,CAAC,CAAA,oBAAqB,EAAE;YAC7B,qCAAqC;YACrC;QACJ;QACA,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,gBAAiB,IAAK;YACnC,IAAI;gBACA,MAAM,IAAI,CAAC,CAAA,iBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,oBAAqB;YAC/F,EACA,OAAO,OAAO;gBACV,gFAAgF;gBAChF,0EAA0E;gBAC1E,yDAAyD;gBACzD,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,gBAAiB,IAAK;oBACnC,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,0BAA2B;QAC7B,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,cAAe,GAAI;YACtD,IAAI;gBACA,IAAI,IAAI,CAAC,CAAA,oBAAqB,EAAE;oBAC5B,MAAM,IAAI,CAAC,CAAA,iBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,oBAAqB;gBAC/F;gBACA,MAAM,IAAI,CAAC,CAAA,iBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,MAAM,CAC9C,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,WAAW,CAAC,IAAI,CAAC,CAAA,cAAe,EAChC,SAAS,CAAC,QAAQ,gBAAgB,CAAC,MAAQ,IAAI,OAAO,GAAG,UAAU,GACpE,2EAA2E;gBAC3E,kEAAkE;iBACjE,SAAS,CAAC,aAAa,gBAAgB,CAAC,MAAQ,IAAI,OAAO;YACpE,EACA,OAAO,OAAO;gBACV,+EAA+E;gBAC/E,yEAAyE;gBACzE,wDAAwD;gBACxD,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,cAAe,GAAI;oBACtD,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,8BAA+B;QACjC,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,kBAAmB,GAAI;YAC1D,IAAI;gBACA,MAAM,IAAI,CAAC,CAAA,iBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,MAAM,CAC9C,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,WAAW,CAAC,IAAI,CAAC,CAAA,kBAAmB,EACpC,SAAS,CAAC,MAAM,gBAAgB,CAAC,MAAQ,IAAI,OAAO,GAAG,UAAU,IACjE,SAAS,CAAC,aAAa,WAAW,CAAC,MAAQ,IAAI,OAAO,GAAG,SAAS,CAAC;YAC5E,EACA,OAAO,OAAO;gBACV,+EAA+E;gBAC/E,yEAAyE;gBACzE,wDAAwD;gBACxD,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,kBAAmB,GAAI;oBAC1D,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,mBAAoB;QACtB,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,iBAAkB,IAAK;YACpC,IAAI;gBACA,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CACf,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,kBAAmB,EACnC,MAAM,CAAC;oBAAE,IAAI;oBAAmB,WAAW;gBAAE,GAC7C,OAAO;YAChB,EACA,OAAO,OAAO;gBACV,IAAI,CAAE,MAAM,IAAI,CAAC,CAAA,iBAAkB,IAAK;oBACpC,MAAM;gBACV;YACJ;QACJ;IACJ;IACA,MAAM,CAAA,gBAAiB;QACnB,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU;QAC7D,OAAO,QAAQ,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,IAAI,CAAC,CAAA,oBAAqB;IACtE;IACA,MAAM,CAAA,eAAgB,CAAC,SAAS;QAC5B,MAAM,SAAS,IAAI,CAAC,CAAA,oBAAqB;QACzC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,0BAA0B;QAC9B;QACA,OAAO,OAAO,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,aAAa,CAAC,CAAC,UAAU,GAAG,MAAM,KAAK,MAAM;IACxF;IACA,MAAM,CAAA,iBAAkB;QACpB,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAC/B,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,kBAAmB,EACnC,KAAK,CAAC,MAAM,KAAK,mBACjB,MAAM,CAAC,MACP,gBAAgB;QACrB,OAAO,CAAC,CAAC;IACb;IACA,MAAM,CAAA,aAAc,CAAC,4BAA4B;QAC7C,MAAM,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO;QACpD,MAAM,cAAc,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACvB,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,sBAAsB,IAAI;YACjD,WAAW;YACX,iBAAiB,IAAI,CAAC,CAAA,KAAM,CAAC,oBAAoB;QACrD;QACA,MAAM,MAAM,OAAO;YACf,IAAI;gBACA,MAAM,QAAQ,oBAAoB,CAAC,IAAI;gBACvC,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAA,QAAS,CAAC;gBACnC,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,GAAG;oBAC/B,OAAO;wBAAE,SAAS,EAAE;oBAAC;gBACzB;gBACA,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,6BAA6B;gBACzD,IAAI,QAAQ,GAAG;oBACX,OAAO;wBAAE,SAAS,EAAE;oBAAC;gBACzB;gBACA,IAAI,cAAc,QAAQ;oBACtB,OAAO,MAAM,IAAI,CAAC,CAAA,WAAY,CAAC,IAAI,OAAO;gBAC9C,OACK,IAAI,cAAc,MAAM;oBACzB,OAAO,MAAM,IAAI,CAAC,CAAA,SAAU,CAAC,IAAI,OAAO;gBAC5C;gBACA,OAAO;oBAAE,SAAS,EAAE;gBAAC;YACzB,SACQ;gBACJ,MAAM,QAAQ,oBAAoB,CAAC,IAAI;YAC3C;QACJ;QACA,IAAI,QAAQ,wBAAwB,IAAI,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,mBAAmB,EAAE;YACtE,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC;QAChD,OACK;YACD,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,UAAU,GAAG,OAAO,CAAC;QAC/C;IACJ;IACA,MAAM,CAAA,QAAS,CAAC,EAAE;QACd,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,iBAAkB;QAChD,MAAM,qBAAqB,MAAM,IAAI,CAAC,CAAA,qBAAsB,CAAC;QAC7D,IAAI,CAAC,CAAA,yBAA0B,CAAC,YAAY;QAC5C,IAAI,CAAC,IAAI,CAAC,CAAA,wBAAyB,EAAE;YACjC,IAAI,CAAC,CAAA,uBAAwB,CAAC,YAAY;QAC9C;QACA,MAAM,oBAAoB,IAAI,CAAC,CAAA,oBAAqB,CAAC,YAAY;QACjE,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACV;YACA;YACA,eAAe,CAAA,GAAA,sNAAA,CAAA,UAAO,AAAD,EAAE;YACvB;QACJ;IACJ;IACA,CAAA,oBAAqB,CAAC,UAAU,EAAE,kBAAkB;QAChD,OAAO,WAAW,MAAM,CAAC,CAAC;YACtB,OAAO,CAAC,mBAAmB,QAAQ,CAAC,UAAU,IAAI;QACtD;IACJ;IACA,MAAM,CAAA,iBAAkB;QACpB,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,aAAa;QAC9D,OAAO,OAAO,IAAI,CAAC,eACd,IAAI,GACJ,GAAG,CAAC,CAAC,OAAS,CAAC;gBAChB,GAAG,aAAa,CAAC,KAAK;gBACtB;YACJ,CAAC;IACL;IACA,MAAM,CAAA,qBAAsB,CAAC,EAAE;QAC3B,MAAM,qBAAqB,MAAM,GAC5B,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,cAAe,EAC/B,MAAM,CAAC;YAAC;YAAQ;SAAY,EAC5B,WAAW,GACX,OAAO;QACZ,MAAM,iBAAiB,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,CAAC,EAAE;QAClF,OAAQ,kBACJ,kDAAkD;SACjD,IAAI,CAAC,CAAC,GAAG;YACV,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE;gBAC7B,OAAO,eAAe,EAAE,IAAI,EAAE,EAAE,IAAI;YACxC;YACA,OAAQ,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAC3E,GACK,GAAG,CAAC,CAAC,KAAO,GAAG,IAAI;IAC5B;IACA,CAAA,yBAA0B,CAAC,UAAU,EAAE,kBAAkB;QACrD,iEAAiE;QACjE,KAAK,MAAM,YAAY,mBAAoB;YACvC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,WAAW;gBAChD,MAAM,IAAI,MAAM,CAAC,oDAAoD,EAAE,SAAS,WAAW,CAAC;YAChG;QACJ;IACJ;IACA,CAAA,uBAAwB,CAAC,UAAU,EAAE,kBAAkB;QACnD,2EAA2E;QAC3E,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,MAAM,EAAE,EAAE,EAAG;YAChD,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAC9C,MAAM,IAAI,MAAM,CAAC,6DAA6D,EAAE,kBAAkB,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,4HAA4H,CAAC;YACrR;QACJ;IACJ;IACA,MAAM,CAAA,WAAY,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI;QAC9B,MAAM,uBAAuB,MAAM,kBAAkB,CAChD,KAAK,GACL,OAAO,GACP,KAAK,CAAC,GAAG,MACT,GAAG,CAAC,CAAC;YACN,OAAO,MAAM,UAAU,CAAC,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK;QACrD;QACA,MAAM,UAAU,qBAAqB,GAAG,CAAC,CAAC;YACtC,OAAO;gBACH,eAAe,UAAU,IAAI;gBAC7B,WAAW;gBACX,QAAQ;YACZ;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,EAAE,EAAG;YACrC,MAAM,YAAY,oBAAoB,CAAC,EAAE;YACzC,IAAI;gBACA,IAAI,UAAU,IAAI,EAAE;oBAChB,MAAM,UAAU,IAAI,CAAC;oBACrB,MAAM,GACD,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,cAAe,EAC/B,KAAK,CAAC,QAAQ,KAAK,UAAU,IAAI,EACjC,OAAO;oBACZ,OAAO,CAAC,EAAE,GAAG;wBACT,eAAe,UAAU,IAAI;wBAC7B,WAAW;wBACX,QAAQ;oBACZ;gBACJ;YACJ,EACA,OAAO,OAAO;gBACV,OAAO,CAAC,EAAE,GAAG;oBACT,eAAe,UAAU,IAAI;oBAC7B,WAAW;oBACX,QAAQ;gBACZ;gBACA,MAAM,IAAI,wBAAwB;oBAC9B;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO;YAAE;QAAQ;IACrB;IACA,MAAM,CAAA,SAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI;QAC5B,MAAM,kBAAkB,MAAM,iBAAiB,CAAC,KAAK,CAAC,GAAG;QACzD,MAAM,UAAU,gBAAgB,GAAG,CAAC,CAAC;YACjC,OAAO;gBACH,eAAe,UAAU,IAAI;gBAC7B,WAAW;gBACX,QAAQ;YACZ;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,YAAY,MAAM,iBAAiB,CAAC,EAAE;YAC5C,IAAI;gBACA,MAAM,UAAU,EAAE,CAAC;gBACnB,MAAM,GACD,UAAU,CAAC,IAAI,CAAC,CAAA,YAAa,EAC7B,UAAU,CAAC,IAAI,CAAC,CAAA,cAAe,EAC/B,MAAM,CAAC;oBACR,MAAM,UAAU,IAAI;oBACpB,WAAW,IAAI,OAAO,WAAW;gBACrC,GACK,OAAO;gBACZ,OAAO,CAAC,EAAE,GAAG;oBACT,eAAe,UAAU,IAAI;oBAC7B,WAAW;oBACX,QAAQ;gBACZ;YACJ,EACA,OAAO,OAAO;gBACV,OAAO,CAAC,EAAE,GAAG;oBACT,eAAe,UAAU,IAAI;oBAC7B,WAAW;oBACX,QAAQ;gBACZ;gBACA,MAAM,IAAI,wBAAwB;oBAC9B;oBACA;gBACJ;YACJ;QACJ;QACA,OAAO;YAAE;QAAQ;IACrB;IACA,MAAM,CAAA,iBAAkB,CAAC,EAAE;QACvB,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,EAAE,CAAC,WAAW,GAAG,OAAO,CAAC,yBAAyB,EAAE;YAChE,KAAK,GAAG,WAAW;QACvB;QACA,MAAM,GAAG,OAAO;IACpB;AACJ;AACA,MAAM,gCAAgC;IAClC,CAAA,SAAU,CAAC;IACX,YAAY,MAAM,CAAE;QAChB,KAAK;QACL,IAAI,CAAC,CAAA,SAAU,GAAG;IACtB;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,CAAA,SAAU;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/raw-builder/raw-builder.js"], "sourcesContent": ["/// <reference types=\"./raw-builder.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { freeze } from '../util/object-utils.js';\nimport { NOOP_QUERY_EXECUTOR } from '../query-executor/noop-query-executor.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nclass RawBuilderImpl {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    get expressionType() {\n        return undefined;\n    }\n    get isRawBuilder() {\n        return true;\n    }\n    as(alias) {\n        return new AliasedRawBuilderImpl(this, alias);\n    }\n    $castTo() {\n        return new RawBuilderImpl({ ...this.#props });\n    }\n    $notNull() {\n        return new RawBuilderImpl(this.#props);\n    }\n    withPlugin(plugin) {\n        return new RawBuilderImpl({\n            ...this.#props,\n            plugins: this.#props.plugins !== undefined\n                ? freeze([...this.#props.plugins, plugin])\n                : freeze([plugin]),\n        });\n    }\n    toOperationNode() {\n        return this.#toOperationNode(this.#getExecutor());\n    }\n    compile(executorProvider) {\n        return this.#compile(this.#getExecutor(executorProvider));\n    }\n    async execute(executorProvider) {\n        const executor = this.#getExecutor(executorProvider);\n        return executor.executeQuery(this.#compile(executor), this.#props.queryId);\n    }\n    #getExecutor(executorProvider) {\n        const executor = executorProvider !== undefined\n            ? executorProvider.getExecutor()\n            : NOOP_QUERY_EXECUTOR;\n        return this.#props.plugins !== undefined\n            ? executor.withPlugins(this.#props.plugins)\n            : executor;\n    }\n    #toOperationNode(executor) {\n        return executor.transformQuery(this.#props.rawNode, this.#props.queryId);\n    }\n    #compile(executor) {\n        return executor.compileQuery(this.#toOperationNode(executor), this.#props.queryId);\n    }\n}\nexport function createRawBuilder(props) {\n    return new RawBuilderImpl(props);\n}\nclass AliasedRawBuilderImpl {\n    #rawBuilder;\n    #alias;\n    constructor(rawBuilder, alias) {\n        this.#rawBuilder = rawBuilder;\n        this.#alias = alias;\n    }\n    get expression() {\n        return this.#rawBuilder;\n    }\n    get alias() {\n        return this.#alias;\n    }\n    get rawBuilder() {\n        return this.#rawBuilder;\n    }\n    toOperationNode() {\n        return AliasNode.create(this.#rawBuilder.toOperationNode(), isOperationNodeSource(this.#alias)\n            ? this.#alias.toOperationNode()\n            : IdentifierNode.create(this.#alias));\n    }\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAC5C;AACA;AACA;AACA;AACA;;;;;;AACA,MAAM;IACF,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,IAAI,eAAe;QACf,OAAO;IACX;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,sBAAsB,IAAI,EAAE;IAC3C;IACA,UAAU;QACN,OAAO,IAAI,eAAe;YAAE,GAAG,IAAI,CAAC,CAAA,KAAM;QAAC;IAC/C;IACA,WAAW;QACP,OAAO,IAAI,eAAe,IAAI,CAAC,CAAA,KAAM;IACzC;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,eAAe;YACtB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,KAAK,YAC3B,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;mBAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;gBAAE;aAAO,IACvC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;gBAAC;aAAO;QACzB;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,eAAgB,CAAC,IAAI,CAAC,CAAA,WAAY;IAClD;IACA,QAAQ,gBAAgB,EAAE;QACtB,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,IAAI,CAAC,CAAA,WAAY,CAAC;IAC3C;IACA,MAAM,QAAQ,gBAAgB,EAAE;QAC5B,MAAM,WAAW,IAAI,CAAC,CAAA,WAAY,CAAC;QACnC,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC7E;IACA,CAAA,WAAY,CAAC,gBAAgB;QACzB,MAAM,WAAW,qBAAqB,YAChC,iBAAiB,WAAW,KAC5B,6OAAA,CAAA,sBAAmB;QACzB,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,KAAK,YACzB,SAAS,WAAW,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,IACxC;IACV;IACA,CAAA,eAAgB,CAAC,QAAQ;QACrB,OAAO,SAAS,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC3E;IACA,CAAA,OAAQ,CAAC,QAAQ;QACb,OAAO,SAAS,YAAY,CAAC,IAAI,CAAC,CAAA,eAAgB,CAAC,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACrF;AACJ;AACO,SAAS,iBAAiB,KAAK;IAClC,OAAO,IAAI,eAAe;AAC9B;AACA,MAAM;IACF,CAAA,UAAW,CAAC;IACZ,CAAA,KAAM,CAAC;IACP,YAAY,UAAU,EAAE,KAAK,CAAE;QAC3B,IAAI,CAAC,CAAA,UAAW,GAAG;QACnB,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,UAAW;IAC3B;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,UAAW;IAC3B;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,UAAW,CAAC,eAAe,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,IACvF,IAAI,CAAC,CAAA,KAAM,CAAC,eAAe,KAC3B,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7040, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/raw-builder/sql.js"], "sourcesContent": ["/// <reference types=\"./sql.d.ts\" />\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { ValueNode } from '../operation-node/value-node.js';\nimport { parseStringReference } from '../parser/reference-parser.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { parseValueExpression } from '../parser/value-parser.js';\nimport { createQueryId } from '../util/query-id.js';\nimport { createRawBuilder } from './raw-builder.js';\nexport const sql = Object.assign((sqlFragments, ...parameters) => {\n    return createRawBuilder({\n        queryId: createQueryId(),\n        rawNode: RawNode.create(sqlFragments, parameters?.map(parseParameter) ?? []),\n    });\n}, {\n    ref(columnReference) {\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithChild(parseStringReference(columnReference)),\n        });\n    },\n    val(value) {\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithChild(parseValueExpression(value)),\n        });\n    },\n    value(value) {\n        return this.val(value);\n    },\n    table(tableReference) {\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithChild(parseTable(tableReference)),\n        });\n    },\n    id(...ids) {\n        const fragments = new Array(ids.length + 1).fill('.');\n        fragments[0] = '';\n        fragments[fragments.length - 1] = '';\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.create(fragments, ids.map(IdentifierNode.create)),\n        });\n    },\n    lit(value) {\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithChild(ValueNode.createImmediate(value)),\n        });\n    },\n    literal(value) {\n        return this.lit(value);\n    },\n    raw(sql) {\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithSql(sql),\n        });\n    },\n    join(array, separator = sql `, `) {\n        const nodes = new Array(Math.max(2 * array.length - 1, 0));\n        const sep = separator.toOperationNode();\n        for (let i = 0; i < array.length; ++i) {\n            nodes[2 * i] = parseParameter(array[i]);\n            if (i !== array.length - 1) {\n                nodes[2 * i + 1] = sep;\n            }\n        }\n        return createRawBuilder({\n            queryId: createQueryId(),\n            rawNode: RawNode.createWithChildren(nodes),\n        });\n    },\n});\nfunction parseParameter(param) {\n    if (isOperationNodeSource(param)) {\n        return param.toOperationNode();\n    }\n    return parseValueExpression(param);\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,cAAc,GAAG;IAC/C,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;QACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;QACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,cAAc,YAAY,IAAI,mBAAmB,EAAE;IAC/E;AACJ,GAAG;IACC,KAAI,eAAe;QACf,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,eAAe,CAAC,CAAA,GAAA,4NAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1D;IACJ;IACA,KAAI,KAAK;QACL,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,eAAe,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC1D;IACJ;IACA,OAAM,KAAK;QACP,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA,OAAM,cAAc;QAChB,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,eAAe,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;QAChD;IACJ;IACA,IAAG,GAAG,GAAG;QACL,MAAM,YAAY,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC;QACjD,SAAS,CAAC,EAAE,GAAG;QACf,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,GAAG;QAClC,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC,sOAAA,CAAA,iBAAc,CAAC,MAAM;QACpE;IACJ;IACA,KAAI,KAAK;QACL,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,eAAe,CAAC,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC;QAC/D;IACJ;IACA,SAAQ,KAAK;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB;IACA,KAAI,GAAG;QACH,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QACnC;IACJ;IACA,MAAK,KAAK,EAAE,YAAY,GAAI,CAAC,EAAE,CAAC;QAC5B,MAAM,QAAQ,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,GAAG;QACvD,MAAM,MAAM,UAAU,eAAe;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACnC,KAAK,CAAC,IAAI,EAAE,GAAG,eAAe,KAAK,CAAC,EAAE;YACtC,IAAI,MAAM,MAAM,MAAM,GAAG,GAAG;gBACxB,KAAK,CAAC,IAAI,IAAI,EAAE,GAAG;YACvB;QACJ;QACA,OAAO,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;YACpB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,SAAS,+NAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC;QACxC;IACJ;AACJ;AACA,SAAS,eAAe,KAAK;IACzB,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;QAC9B,OAAO,MAAM,eAAe;IAChC;IACA,OAAO,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;AAChC", "ignoreList": [0], "debugId": null}}]}