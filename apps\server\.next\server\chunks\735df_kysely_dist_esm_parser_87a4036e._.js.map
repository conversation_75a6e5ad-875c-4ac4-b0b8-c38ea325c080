{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/order-by-parser.js"], "sourcesContent": ["/// <reference types=\"./order-by-parser.d.ts\" />\nimport { isDynamicReferenceBuilder, } from '../dynamic/dynamic-reference-builder.js';\nimport { isExpression } from '../expression/expression.js';\nimport { OrderByItemNode } from '../operation-node/order-by-item-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { OrderByItemBuilder } from '../query-builder/order-by-item-builder.js';\nimport { logOnce } from '../util/log-once.js';\nimport { isExpressionOrFactory, parseExpression, } from './expression-parser.js';\nimport { parseStringReference, } from './reference-parser.js';\nexport function isOrderByDirection(thing) {\n    return thing === 'asc' || thing === 'desc';\n}\nexport function parseOrderBy(args) {\n    if (args.length === 2) {\n        return [parseOrderByItem(args[0], args[1])];\n    }\n    if (args.length === 1) {\n        const [orderBy] = args;\n        if (Array.isArray(orderBy)) {\n            logOnce('orderBy(array) is deprecated, use multiple orderBy calls instead.');\n            return orderBy.map((item) => parseOrderByItem(item));\n        }\n        return [parseOrderByItem(orderBy)];\n    }\n    throw new Error(`Invalid number of arguments at order by! expected 1-2, received ${args.length}`);\n}\nexport function parseOrderByItem(expr, modifiers) {\n    const parsedRef = parseOrderByExpression(expr);\n    if (OrderByItemNode.is(parsedRef)) {\n        if (modifiers) {\n            throw new Error('Cannot specify direction twice!');\n        }\n        return parsedRef;\n    }\n    return parseOrderByWithModifiers(parsedRef, modifiers);\n}\nfunction parseOrderByExpression(expr) {\n    if (isExpressionOrFactory(expr)) {\n        return parseExpression(expr);\n    }\n    if (isDynamicReferenceBuilder(expr)) {\n        return expr.toOperationNode();\n    }\n    const [ref, direction] = expr.split(' ');\n    if (direction) {\n        logOnce(\"`orderBy('column asc')` is deprecated. Use `orderBy('column', 'asc')` instead.\");\n        return parseOrderByWithModifiers(parseStringReference(ref), direction);\n    }\n    return parseStringReference(expr);\n}\nfunction parseOrderByWithModifiers(expr, modifiers) {\n    if (typeof modifiers === 'string') {\n        if (!isOrderByDirection(modifiers)) {\n            throw new Error(`Invalid order by direction: ${modifiers}`);\n        }\n        return OrderByItemNode.create(expr, RawNode.createWithSql(modifiers));\n    }\n    if (isExpression(modifiers)) {\n        logOnce(\"`orderBy(..., expr)` is deprecated. Use `orderBy(..., 'asc')` or `orderBy(..., (ob) => ...)` instead.\");\n        return OrderByItemNode.create(expr, modifiers.toOperationNode());\n    }\n    const node = OrderByItemNode.create(expr);\n    if (!modifiers) {\n        return node;\n    }\n    return modifiers(new OrderByItemBuilder({ node })).toOperationNode();\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,SAAS,mBAAmB,KAAK;IACpC,OAAO,UAAU,SAAS,UAAU;AACxC;AACO,SAAS,aAAa,IAAI;IAC7B,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO;YAAC,iBAAiB,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;SAAE;IAC/C;IACA,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,MAAM,CAAC,QAAQ,GAAG;QAClB,IAAI,MAAM,OAAO,CAAC,UAAU;YACxB,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE;YACR,OAAO,QAAQ,GAAG,CAAC,CAAC,OAAS,iBAAiB;QAClD;QACA,OAAO;YAAC,iBAAiB;SAAS;IACtC;IACA,MAAM,IAAI,MAAM,CAAC,gEAAgE,EAAE,KAAK,MAAM,EAAE;AACpG;AACO,SAAS,iBAAiB,IAAI,EAAE,SAAS;IAC5C,MAAM,YAAY,uBAAuB;IACzC,IAAI,+OAAA,CAAA,kBAAe,CAAC,EAAE,CAAC,YAAY;QAC/B,IAAI,WAAW;YACX,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX;IACA,OAAO,0BAA0B,WAAW;AAChD;AACA,SAAS,uBAAuB,IAAI;IAChC,IAAI,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;QAC7B,OAAO,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B;IACA,IAAI,CAAA,GAAA,yOAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;QACjC,OAAO,KAAK,eAAe;IAC/B;IACA,MAAM,CAAC,KAAK,UAAU,GAAG,KAAK,KAAK,CAAC;IACpC,IAAI,WAAW;QACX,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE;QACR,OAAO,0BAA0B,CAAA,GAAA,4NAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM;IAChE;IACA,OAAO,CAAA,GAAA,4NAAA,CAAA,uBAAoB,AAAD,EAAE;AAChC;AACA,SAAS,0BAA0B,IAAI,EAAE,SAAS;IAC9C,IAAI,OAAO,cAAc,UAAU;QAC/B,IAAI,CAAC,mBAAmB,YAAY;YAChC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,WAAW;QAC9D;QACA,OAAO,+OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;IAC9D;IACA,IAAI,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE,YAAY;QACzB,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE;QACR,OAAO,+OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM,UAAU,eAAe;IACjE;IACA,MAAM,OAAO,+OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;IACpC,IAAI,CAAC,WAAW;QACZ,OAAO;IACX;IACA,OAAO,UAAU,IAAI,iPAAA,CAAA,qBAAkB,CAAC;QAAE;IAAK,IAAI,eAAe;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/reference-parser.js"], "sourcesContent": ["/// <reference types=\"./reference-parser.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { ReferenceNode } from '../operation-node/reference-node.js';\nimport { TableNode } from '../operation-node/table-node.js';\nimport { isReadonlyArray, isString } from '../util/object-utils.js';\nimport { parseExpression, isExpressionOrFactory, } from './expression-parser.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isOrderByDirection, parseOrderBy, } from './order-by-parser.js';\nimport { OperatorNode, isJSONOperator, } from '../operation-node/operator-node.js';\nimport { JSONReferenceNode } from '../operation-node/json-reference-node.js';\nimport { JSONOperatorChainNode } from '../operation-node/json-operator-chain-node.js';\nimport { JSONPathNode } from '../operation-node/json-path-node.js';\nexport function parseSimpleReferenceExpression(exp) {\n    if (isString(exp)) {\n        return parseStringReference(exp);\n    }\n    return exp.toOperationNode();\n}\nexport function parseReferenceExpressionOrList(arg) {\n    if (isReadonlyArray(arg)) {\n        return arg.map((it) => parseReferenceExpression(it));\n    }\n    else {\n        return [parseReferenceExpression(arg)];\n    }\n}\nexport function parseReferenceExpression(exp) {\n    if (isExpressionOrFactory(exp)) {\n        return parseExpression(exp);\n    }\n    return parseSimpleReferenceExpression(exp);\n}\nexport function parseJSONReference(ref, op) {\n    const referenceNode = parseStringReference(ref);\n    if (isJSONOperator(op)) {\n        return JSONReferenceNode.create(referenceNode, JSONOperatorChainNode.create(OperatorNode.create(op)));\n    }\n    const opWithoutLastChar = op.slice(0, -1);\n    if (isJSONOperator(opWithoutLastChar)) {\n        return JSONReferenceNode.create(referenceNode, JSONPathNode.create(OperatorNode.create(opWithoutLastChar)));\n    }\n    throw new Error(`Invalid JSON operator: ${op}`);\n}\nexport function parseStringReference(ref) {\n    const COLUMN_SEPARATOR = '.';\n    if (!ref.includes(COLUMN_SEPARATOR)) {\n        return ReferenceNode.create(ColumnNode.create(ref));\n    }\n    const parts = ref.split(COLUMN_SEPARATOR).map(trim);\n    if (parts.length === 3) {\n        return parseStringReferenceWithTableAndSchema(parts);\n    }\n    if (parts.length === 2) {\n        return parseStringReferenceWithTable(parts);\n    }\n    throw new Error(`invalid column reference ${ref}`);\n}\nexport function parseAliasedStringReference(ref) {\n    const ALIAS_SEPARATOR = ' as ';\n    if (ref.includes(ALIAS_SEPARATOR)) {\n        const [columnRef, alias] = ref.split(ALIAS_SEPARATOR).map(trim);\n        return AliasNode.create(parseStringReference(columnRef), IdentifierNode.create(alias));\n    }\n    else {\n        return parseStringReference(ref);\n    }\n}\nexport function parseColumnName(column) {\n    return ColumnNode.create(column);\n}\nexport function parseOrderedColumnName(column) {\n    const ORDER_SEPARATOR = ' ';\n    if (column.includes(ORDER_SEPARATOR)) {\n        const [columnName, order] = column.split(ORDER_SEPARATOR).map(trim);\n        if (!isOrderByDirection(order)) {\n            throw new Error(`invalid order direction \"${order}\" next to \"${columnName}\"`);\n        }\n        return parseOrderBy([columnName, order])[0];\n    }\n    else {\n        return parseColumnName(column);\n    }\n}\nfunction parseStringReferenceWithTableAndSchema(parts) {\n    const [schema, table, column] = parts;\n    return ReferenceNode.create(ColumnNode.create(column), TableNode.createWithSchema(schema, table));\n}\nfunction parseStringReferenceWithTable(parts) {\n    const [table, column] = parts;\n    return ReferenceNode.create(ColumnNode.create(column), TableNode.create(table));\n}\nfunction trim(str) {\n    return str.trim();\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;;;;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACO,SAAS,+BAA+B,GAAG;IAC9C,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,MAAM;QACf,OAAO,qBAAqB;IAChC;IACA,OAAO,IAAI,eAAe;AAC9B;AACO,SAAS,+BAA+B,GAAG;IAC9C,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,MAAM;QACtB,OAAO,IAAI,GAAG,CAAC,CAAC,KAAO,yBAAyB;IACpD,OACK;QACD,OAAO;YAAC,yBAAyB;SAAK;IAC1C;AACJ;AACO,SAAS,yBAAyB,GAAG;IACxC,IAAI,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;QAC5B,OAAO,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B;IACA,OAAO,+BAA+B;AAC1C;AACO,SAAS,mBAAmB,GAAG,EAAE,EAAE;IACtC,MAAM,gBAAgB,qBAAqB;IAC3C,IAAI,CAAA,GAAA,oOAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QACpB,OAAO,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,eAAe,qPAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;IACpG;IACA,MAAM,oBAAoB,GAAG,KAAK,CAAC,GAAG,CAAC;IACvC,IAAI,CAAA,GAAA,oOAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB;QACnC,OAAO,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,eAAe,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;IAC3F;IACA,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,IAAI;AAClD;AACO,SAAS,qBAAqB,GAAG;IACpC,MAAM,mBAAmB;IACzB,IAAI,CAAC,IAAI,QAAQ,CAAC,mBAAmB;QACjC,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAClD;IACA,MAAM,QAAQ,IAAI,KAAK,CAAC,kBAAkB,GAAG,CAAC;IAC9C,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO,uCAAuC;IAClD;IACA,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO,8BAA8B;IACzC;IACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK;AACrD;AACO,SAAS,4BAA4B,GAAG;IAC3C,MAAM,kBAAkB;IACxB,IAAI,IAAI,QAAQ,CAAC,kBAAkB;QAC/B,MAAM,CAAC,WAAW,MAAM,GAAG,IAAI,KAAK,CAAC,iBAAiB,GAAG,CAAC;QAC1D,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,qBAAqB,YAAY,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnF,OACK;QACD,OAAO,qBAAqB;IAChC;AACJ;AACO,SAAS,gBAAgB,MAAM;IAClC,OAAO,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC;AAC7B;AACO,SAAS,uBAAuB,MAAM;IACzC,MAAM,kBAAkB;IACxB,IAAI,OAAO,QAAQ,CAAC,kBAAkB;QAClC,MAAM,CAAC,YAAY,MAAM,GAAG,OAAO,KAAK,CAAC,iBAAiB,GAAG,CAAC;QAC9D,IAAI,CAAC,CAAA,GAAA,8NAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ;YAC5B,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,WAAW,EAAE,WAAW,CAAC,CAAC;QAChF;QACA,OAAO,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;YAAC;YAAY;SAAM,CAAC,CAAC,EAAE;IAC/C,OACK;QACD,OAAO,gBAAgB;IAC3B;AACJ;AACA,SAAS,uCAAuC,KAAK;IACjD,MAAM,CAAC,QAAQ,OAAO,OAAO,GAAG;IAChC,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,QAAQ;AAC9F;AACA,SAAS,8BAA8B,KAAK;IACxC,MAAM,CAAC,OAAO,OAAO,GAAG;IACxB,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,SAAS,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC;AAC5E;AACA,SAAS,KAAK,GAAG;IACb,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/value-parser.js"], "sourcesContent": ["/// <reference types=\"./value-parser.d.ts\" />\nimport { PrimitiveValueListNode } from '../operation-node/primitive-value-list-node.js';\nimport { ValueListNode } from '../operation-node/value-list-node.js';\nimport { ValueNode } from '../operation-node/value-node.js';\nimport { isBoolean, isNull, isNumber, isReadonlyArray, } from '../util/object-utils.js';\nimport { parseExpression, isExpressionOrFactory, } from './expression-parser.js';\nexport function parseValueExpressionOrList(arg) {\n    if (isReadonlyArray(arg)) {\n        return parseValueExpressionList(arg);\n    }\n    return parseValueExpression(arg);\n}\nexport function parseValueExpression(exp) {\n    if (isExpressionOrFactory(exp)) {\n        return parseExpression(exp);\n    }\n    return ValueNode.create(exp);\n}\nexport function isSafeImmediateValue(value) {\n    return isNumber(value) || isBoolean(value) || isNull(value);\n}\nexport function parseSafeImmediateValue(value) {\n    if (!isSafeImmediateValue(value)) {\n        throw new Error(`unsafe immediate value ${JSON.stringify(value)}`);\n    }\n    return ValueNode.createImmediate(value);\n}\nfunction parseValueExpressionList(arg) {\n    if (arg.some(isExpressionOrFactory)) {\n        return ValueListNode.create(arg.map((it) => parseValueExpression(it)));\n    }\n    return PrimitiveValueListNode.create(arg);\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;AAC7C;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,2BAA2B,GAAG;IAC1C,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,MAAM;QACtB,OAAO,yBAAyB;IACpC;IACA,OAAO,qBAAqB;AAChC;AACO,SAAS,qBAAqB,GAAG;IACpC,IAAI,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;QAC5B,OAAO,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B;IACA,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC;AAC5B;AACO,SAAS,qBAAqB,KAAK;IACtC,OAAO,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;AACzD;AACO,SAAS,wBAAwB,KAAK;IACzC,IAAI,CAAC,qBAAqB,QAAQ;QAC9B,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,KAAK,SAAS,CAAC,QAAQ;IACrE;IACA,OAAO,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC;AACrC;AACA,SAAS,yBAAyB,GAAG;IACjC,IAAI,IAAI,IAAI,CAAC,6NAAA,CAAA,wBAAqB,GAAG;QACjC,OAAO,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,KAAO,qBAAqB;IACrE;IACA,OAAO,sPAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/binary-operation-parser.js"], "sourcesContent": ["/// <reference types=\"./binary-operation-parser.d.ts\" />\nimport { BinaryOperationNode } from '../operation-node/binary-operation-node.js';\nimport { isBoolean, isNull, isString, isUndefined, } from '../util/object-utils.js';\nimport { isOperationNodeSource, } from '../operation-node/operation-node-source.js';\nimport { OperatorNode, OPERATORS, } from '../operation-node/operator-node.js';\nimport { parseReferenceExpression, } from './reference-parser.js';\nimport { parseValueExpression, parseValueExpressionOrList, } from './value-parser.js';\nimport { ValueNode } from '../operation-node/value-node.js';\nimport { AndNode } from '../operation-node/and-node.js';\nimport { ParensNode } from '../operation-node/parens-node.js';\nimport { OrNode } from '../operation-node/or-node.js';\nexport function parseValueBinaryOperationOrExpression(args) {\n    if (args.length === 3) {\n        return parseValueBinaryOperation(args[0], args[1], args[2]);\n    }\n    else if (args.length === 1) {\n        return parseValueExpression(args[0]);\n    }\n    throw new Error(`invalid arguments: ${JSON.stringify(args)}`);\n}\nexport function parseValueBinaryOperation(left, operator, right) {\n    if (isIsOperator(operator) && needsIsOperator(right)) {\n        return BinaryOperationNode.create(parseReferenceExpression(left), parseOperator(operator), ValueNode.createImmediate(right));\n    }\n    return BinaryOperationNode.create(parseReferenceExpression(left), parseOperator(operator), parseValueExpressionOrList(right));\n}\nexport function parseReferentialBinaryOperation(left, operator, right) {\n    return BinaryOperationNode.create(parseReferenceExpression(left), parseOperator(operator), parseReferenceExpression(right));\n}\nexport function parseFilterObject(obj, combinator) {\n    return parseFilterList(Object.entries(obj)\n        .filter(([, v]) => !isUndefined(v))\n        .map(([k, v]) => parseValueBinaryOperation(k, needsIsOperator(v) ? 'is' : '=', v)), combinator);\n}\nexport function parseFilterList(list, combinator, withParens = true) {\n    const combine = combinator === 'and' ? AndNode.create : OrNode.create;\n    if (list.length === 0) {\n        return BinaryOperationNode.create(ValueNode.createImmediate(1), OperatorNode.create('='), ValueNode.createImmediate(combinator === 'and' ? 1 : 0));\n    }\n    let node = toOperationNode(list[0]);\n    for (let i = 1; i < list.length; ++i) {\n        node = combine(node, toOperationNode(list[i]));\n    }\n    if (list.length > 1 && withParens) {\n        return ParensNode.create(node);\n    }\n    return node;\n}\nfunction isIsOperator(operator) {\n    return operator === 'is' || operator === 'is not';\n}\nfunction needsIsOperator(value) {\n    return isNull(value) || isBoolean(value);\n}\nfunction parseOperator(operator) {\n    if (isString(operator) && OPERATORS.includes(operator)) {\n        return OperatorNode.create(operator);\n    }\n    if (isOperationNodeSource(operator)) {\n        return operator.toOperationNode();\n    }\n    throw new Error(`invalid operator ${JSON.stringify(operator)}`);\n}\nfunction toOperationNode(nodeOrSource) {\n    return isOperationNodeSource(nodeOrSource)\n        ? nodeOrSource.toOperationNode()\n        : nodeOrSource;\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;;;;;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,SAAS,sCAAsC,IAAI;IACtD,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO,0BAA0B,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAC9D,OACK,IAAI,KAAK,MAAM,KAAK,GAAG;QACxB,OAAO,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,EAAE;IACvC;IACA,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,KAAK,SAAS,CAAC,OAAO;AAChE;AACO,SAAS,0BAA0B,IAAI,EAAE,QAAQ,EAAE,KAAK;IAC3D,IAAI,aAAa,aAAa,gBAAgB,QAAQ;QAClD,OAAO,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,cAAc,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC;IACzH;IACA,OAAO,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,cAAc,WAAW,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE;AAC1H;AACO,SAAS,gCAAgC,IAAI,EAAE,QAAQ,EAAE,KAAK;IACjE,OAAO,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,cAAc,WAAW,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE;AACxH;AACO,SAAS,kBAAkB,GAAG,EAAE,UAAU;IAC7C,OAAO,gBAAgB,OAAO,OAAO,CAAC,KACjC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAK,CAAC,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,IAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,0BAA0B,GAAG,gBAAgB,KAAK,OAAO,KAAK,KAAK;AAC5F;AACO,SAAS,gBAAgB,IAAI,EAAE,UAAU,EAAE,aAAa,IAAI;IAC/D,MAAM,UAAU,eAAe,QAAQ,+NAAA,CAAA,UAAO,CAAC,MAAM,GAAG,8NAAA,CAAA,SAAM,CAAC,MAAM;IACrE,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,eAAe,QAAQ,IAAI;IACnJ;IACA,IAAI,OAAO,gBAAgB,IAAI,CAAC,EAAE;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAClC,OAAO,QAAQ,MAAM,gBAAgB,IAAI,CAAC,EAAE;IAChD;IACA,IAAI,KAAK,MAAM,GAAG,KAAK,YAAY;QAC/B,OAAO,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC7B;IACA,OAAO;AACX;AACA,SAAS,aAAa,QAAQ;IAC1B,OAAO,aAAa,QAAQ,aAAa;AAC7C;AACA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAA,GAAA,sNAAA,CAAA,YAAS,AAAD,EAAE;AACtC;AACA,SAAS,cAAc,QAAQ;IAC3B,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,oOAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,WAAW;QACpD,OAAO,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;IAC/B;IACA,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW;QACjC,OAAO,SAAS,eAAe;IACnC;IACA,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,SAAS,CAAC,WAAW;AAClE;AACA,SAAS,gBAAgB,YAAY;IACjC,OAAO,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,gBACvB,aAAa,eAAe,KAC5B;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/partition-by-parser.js"], "sourcesContent": ["/// <reference types=\"./partition-by-parser.d.ts\" />\nimport { PartitionByItemNode } from '../operation-node/partition-by-item-node.js';\nimport { parseReferenceExpressionOrList, } from './reference-parser.js';\nexport function parsePartitionBy(partitionBy) {\n    return parseReferenceExpressionOrList(partitionBy).map(PartitionByItemNode.create);\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AACO,SAAS,iBAAiB,WAAW;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE,aAAa,GAAG,CAAC,mPAAA,CAAA,sBAAmB,CAAC,MAAM;AACrF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/select-parser.js"], "sourcesContent": ["/// <reference types=\"./select-parser.d.ts\" />\nimport { isFunction, isReadonlyArray, isString } from '../util/object-utils.js';\nimport { SelectionNode } from '../operation-node/selection-node.js';\nimport { parseAliasedStringReference } from './reference-parser.js';\nimport { isDynamicReferenceBuilder, } from '../dynamic/dynamic-reference-builder.js';\nimport { parseAliasedExpression, } from './expression-parser.js';\nimport { parseTable } from './table-parser.js';\nimport { expressionBuilder, } from '../expression/expression-builder.js';\nexport function parseSelectArg(selection) {\n    if (isFunction(selection)) {\n        return parseSelectArg(selection(expressionBuilder()));\n    }\n    else if (isReadonlyArray(selection)) {\n        return selection.map((it) => parseSelectExpression(it));\n    }\n    else {\n        return [parseSelectExpression(selection)];\n    }\n}\nfunction parseSelectExpression(selection) {\n    if (isString(selection)) {\n        return SelectionNode.create(parseAliasedStringReference(selection));\n    }\n    else if (isDynamicReferenceBuilder(selection)) {\n        return SelectionNode.create(selection.toOperationNode());\n    }\n    else {\n        return SelectionNode.create(parseAliasedExpression(selection));\n    }\n}\nexport function parseSelectAll(table) {\n    if (!table) {\n        return [SelectionNode.createSelectAll()];\n    }\n    else if (Array.isArray(table)) {\n        return table.map(parseSelectAllArg);\n    }\n    else {\n        return [parseSelectAllArg(table)];\n    }\n}\nfunction parseSelectAllArg(table) {\n    if (isString(table)) {\n        return SelectionNode.createSelectAllFromTable(parseTable(table));\n    }\n    throw new Error(`invalid value selectAll expression: ${JSON.stringify(table)}`);\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,SAAS,eAAe,SAAS;IACpC,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,YAAY;QACvB,OAAO,eAAe,UAAU,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD;IACpD,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;QACjC,OAAO,UAAU,GAAG,CAAC,CAAC,KAAO,sBAAsB;IACvD,OACK;QACD,OAAO;YAAC,sBAAsB;SAAW;IAC7C;AACJ;AACA,SAAS,sBAAsB,SAAS;IACpC,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QACrB,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,8BAA2B,AAAD,EAAE;IAC5D,OACK,IAAI,CAAA,GAAA,yOAAA,CAAA,4BAAyB,AAAD,EAAE,YAAY;QAC3C,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,eAAe;IACzD,OACK;QACD,OAAO,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE;IACvD;AACJ;AACO,SAAS,eAAe,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO;YAAC,qOAAA,CAAA,gBAAa,CAAC,eAAe;SAAG;IAC5C,OACK,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC3B,OAAO,MAAM,GAAG,CAAC;IACrB,OACK;QACD,OAAO;YAAC,kBAAkB;SAAO;IACrC;AACJ;AACA,SAAS,kBAAkB,KAAK;IAC5B,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QACjB,OAAO,qOAAA,CAAA,gBAAa,CAAC,wBAAwB,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;IAC7D;IACA,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,SAAS,CAAC,QAAQ;AAClF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/insert-values-parser.js"], "sourcesContent": ["/// <reference types=\"./insert-values-parser.d.ts\" />\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { PrimitiveValueListNode } from '../operation-node/primitive-value-list-node.js';\nimport { ValueListNode } from '../operation-node/value-list-node.js';\nimport { freeze, isFunction, isReadonlyArray, isUndefined, } from '../util/object-utils.js';\nimport { parseValueExpression } from './value-parser.js';\nimport { ValuesNode } from '../operation-node/values-node.js';\nimport { isExpressionOrFactory } from './expression-parser.js';\nimport { DefaultInsertValueNode } from '../operation-node/default-insert-value-node.js';\nimport { expressionBuilder, } from '../expression/expression-builder.js';\nexport function parseInsertExpression(arg) {\n    const objectOrList = isFunction(arg) ? arg(expressionBuilder()) : arg;\n    const list = isReadonlyArray(objectOrList)\n        ? objectOrList\n        : freeze([objectOrList]);\n    return parseInsertColumnsAndValues(list);\n}\nfunction parseInsertColumnsAndValues(rows) {\n    const columns = parseColumnNamesAndIndexes(rows);\n    return [\n        freeze([...columns.keys()].map(ColumnNode.create)),\n        ValuesNode.create(rows.map((row) => parseRowValues(row, columns))),\n    ];\n}\nfunction parseColumnNamesAndIndexes(rows) {\n    const columns = new Map();\n    for (const row of rows) {\n        const cols = Object.keys(row);\n        for (const col of cols) {\n            if (!columns.has(col) && row[col] !== undefined) {\n                columns.set(col, columns.size);\n            }\n        }\n    }\n    return columns;\n}\nfunction parseRowValues(row, columns) {\n    const rowColumns = Object.keys(row);\n    const rowValues = Array.from({\n        length: columns.size,\n    });\n    let hasUndefinedOrComplexColumns = false;\n    let indexedRowColumns = rowColumns.length;\n    for (const col of rowColumns) {\n        const columnIdx = columns.get(col);\n        if (isUndefined(columnIdx)) {\n            indexedRowColumns--;\n            continue;\n        }\n        const value = row[col];\n        if (isUndefined(value) || isExpressionOrFactory(value)) {\n            hasUndefinedOrComplexColumns = true;\n        }\n        rowValues[columnIdx] = value;\n    }\n    const hasMissingColumns = indexedRowColumns < columns.size;\n    if (hasMissingColumns || hasUndefinedOrComplexColumns) {\n        const defaultValue = DefaultInsertValueNode.create();\n        return ValueListNode.create(rowValues.map((it) => isUndefined(it) ? defaultValue : parseValueExpression(it)));\n    }\n    return PrimitiveValueListNode.create(rowValues);\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,SAAS,sBAAsB,GAAG;IACrC,MAAM,eAAe,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD,OAAO;IAClE,MAAM,OAAO,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,gBACvB,eACA,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;QAAC;KAAa;IAC3B,OAAO,4BAA4B;AACvC;AACA,SAAS,4BAA4B,IAAI;IACrC,MAAM,UAAU,2BAA2B;IAC3C,OAAO;QACH,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;eAAI,QAAQ,IAAI;SAAG,CAAC,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM;QAChD,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,MAAQ,eAAe,KAAK;KAC3D;AACL;AACA,SAAS,2BAA2B,IAAI;IACpC,MAAM,UAAU,IAAI;IACpB,KAAK,MAAM,OAAO,KAAM;QACpB,MAAM,OAAO,OAAO,IAAI,CAAC;QACzB,KAAK,MAAM,OAAO,KAAM;YACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,KAAK,WAAW;gBAC7C,QAAQ,GAAG,CAAC,KAAK,QAAQ,IAAI;YACjC;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,eAAe,GAAG,EAAE,OAAO;IAChC,MAAM,aAAa,OAAO,IAAI,CAAC;IAC/B,MAAM,YAAY,MAAM,IAAI,CAAC;QACzB,QAAQ,QAAQ,IAAI;IACxB;IACA,IAAI,+BAA+B;IACnC,IAAI,oBAAoB,WAAW,MAAM;IACzC,KAAK,MAAM,OAAO,WAAY;QAC1B,MAAM,YAAY,QAAQ,GAAG,CAAC;QAC9B,IAAI,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,YAAY;YACxB;YACA;QACJ;QACA,MAAM,QAAQ,GAAG,CAAC,IAAI;QACtB,IAAI,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,UAAU,CAAA,GAAA,6NAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;YACpD,+BAA+B;QACnC;QACA,SAAS,CAAC,UAAU,GAAG;IAC3B;IACA,MAAM,oBAAoB,oBAAoB,QAAQ,IAAI;IAC1D,IAAI,qBAAqB,8BAA8B;QACnD,MAAM,eAAe,sPAAA,CAAA,yBAAsB,CAAC,MAAM;QAClD,OAAO,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,KAAO,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,MAAM,eAAe,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;IAC5G;IACA,OAAO,sPAAA,CAAA,yBAAsB,CAAC,MAAM,CAAC;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/update-set-parser.js"], "sourcesContent": ["/// <reference types=\"./update-set-parser.d.ts\" />\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { ColumnUpdateNode } from '../operation-node/column-update-node.js';\nimport { expressionBuilder, } from '../expression/expression-builder.js';\nimport { isFunction } from '../util/object-utils.js';\nimport { parseValueExpression } from './value-parser.js';\nimport { parseReferenceExpression, } from './reference-parser.js';\nexport function parseUpdate(...args) {\n    if (args.length === 2) {\n        return [\n            ColumnUpdateNode.create(parseReferenceExpression(args[0]), parseValueExpression(args[1])),\n        ];\n    }\n    return parseUpdateObjectExpression(args[0]);\n}\nexport function parseUpdateObjectExpression(update) {\n    const updateObj = isFunction(update) ? update(expressionBuilder()) : update;\n    return Object.entries(updateObj)\n        .filter(([_, value]) => value !== undefined)\n        .map(([key, value]) => {\n        return ColumnUpdateNode.create(ColumnNode.create(key), parseValueExpression(value));\n    });\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;AAClD;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,YAAY,GAAG,IAAI;IAC/B,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO;YACH,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,EAAE;SAC1F;IACL;IACA,OAAO,4BAA4B,IAAI,CAAC,EAAE;AAC9C;AACO,SAAS,4BAA4B,MAAM;IAC9C,MAAM,YAAY,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,UAAU,OAAO,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD,OAAO;IACrE,OAAO,OAAO,OAAO,CAAC,WACjB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU,WACjC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAClB,OAAO,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,MAAM,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;IAChF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/top-parser.js"], "sourcesContent": ["/// <reference types=\"./top-parser.d.ts\" />\nimport { TopNode } from '../operation-node/top-node.js';\nimport { isBigInt, isNumber, isUndefined } from '../util/object-utils.js';\nexport function parseTop(expression, modifiers) {\n    if (!isNumber(expression) && !isBigInt(expression)) {\n        throw new Error(`Invalid top expression: ${expression}`);\n    }\n    if (!isUndefined(modifiers) && !isTopModifiers(modifiers)) {\n        throw new Error(`Invalid top modifiers: ${modifiers}`);\n    }\n    return TopNode.create(expression, modifiers);\n}\nfunction isTopModifiers(modifiers) {\n    return (modifiers === 'percent' ||\n        modifiers === 'with ties' ||\n        modifiers === 'percent with ties');\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;AAC3C;AACA;;;AACO,SAAS,SAAS,UAAU,EAAE,SAAS;IAC1C,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,CAAC,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;QAChD,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,YAAY;IAC3D;IACA,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,cAAW,AAAD,EAAE,cAAc,CAAC,eAAe,YAAY;QACvD,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,WAAW;IACzD;IACA,OAAO,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC,YAAY;AACtC;AACA,SAAS,eAAe,SAAS;IAC7B,OAAQ,cAAc,aAClB,cAAc,eACd,cAAc;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/with-parser.js"], "sourcesContent": ["/// <reference types=\"./with-parser.d.ts\" />\nimport { CommonTableExpressionNameNode } from '../operation-node/common-table-expression-name-node.js';\nimport { createQueryCreator } from './parse-utils.js';\nimport { isFunction } from '../util/object-utils.js';\nimport { CTEBuilder } from '../query-builder/cte-builder.js';\nimport { CommonTableExpressionNode } from '../operation-node/common-table-expression-node.js';\nexport function parseCommonTableExpression(nameOrBuilderCallback, expression) {\n    const expressionNode = expression(createQueryCreator()).toOperationNode();\n    if (isFunction(nameOrBuilderCallback)) {\n        return nameOrBuilderCallback(cteBuilderFactory(expressionNode)).toOperationNode();\n    }\n    return CommonTableExpressionNode.create(parseCommonTableExpressionName(nameOrBuilderCallback), expressionNode);\n}\nfunction cteBuilderFactory(expressionNode) {\n    return (name) => {\n        return new CTEBuilder({\n            node: CommonTableExpressionNode.create(parseCommonTableExpressionName(name), expressionNode),\n        });\n    };\n}\nfunction parseCommonTableExpressionName(name) {\n    if (name.includes('(')) {\n        const parts = name.split(/[\\(\\)]/);\n        const table = parts[0];\n        const columns = parts[1].split(',').map((it) => it.trim());\n        return CommonTableExpressionNameNode.create(table, columns);\n    }\n    else {\n        return CommonTableExpressionNameNode.create(name);\n    }\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAC5C;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,2BAA2B,qBAAqB,EAAE,UAAU;IACxE,MAAM,iBAAiB,WAAW,CAAA,GAAA,uNAAA,CAAA,qBAAkB,AAAD,KAAK,eAAe;IACvE,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,wBAAwB;QACnC,OAAO,sBAAsB,kBAAkB,iBAAiB,eAAe;IACnF;IACA,OAAO,yPAAA,CAAA,4BAAyB,CAAC,MAAM,CAAC,+BAA+B,wBAAwB;AACnG;AACA,SAAS,kBAAkB,cAAc;IACrC,OAAO,CAAC;QACJ,OAAO,IAAI,iOAAA,CAAA,aAAU,CAAC;YAClB,MAAM,yPAAA,CAAA,4BAAyB,CAAC,MAAM,CAAC,+BAA+B,OAAO;QACjF;IACJ;AACJ;AACA,SAAS,+BAA+B,IAAI;IACxC,IAAI,KAAK,QAAQ,CAAC,MAAM;QACpB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,MAAM,QAAQ,KAAK,CAAC,EAAE;QACtB,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAO,GAAG,IAAI;QACvD,OAAO,iQAAA,CAAA,gCAA6B,CAAC,MAAM,CAAC,OAAO;IACvD,OACK;QACD,OAAO,iQAAA,CAAA,gCAA6B,CAAC,MAAM,CAAC;IAChD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/merge-parser.js"], "sourcesContent": ["/// <reference types=\"./merge-parser.d.ts\" />\nimport { MatchedNode } from '../operation-node/matched-node.js';\nimport { isOperationNodeSource, } from '../operation-node/operation-node-source.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { WhenNode } from '../operation-node/when-node.js';\nimport { isString } from '../util/object-utils.js';\nimport { parseFilterList, parseReferentialBinaryOperation, parseValueBinaryOperationOrExpression, } from './binary-operation-parser.js';\nexport function parseMergeWhen(type, args, refRight) {\n    return WhenNode.create(parseFilterList([\n        MatchedNode.create(!type.isMatched, type.bySource),\n        ...(args && args.length > 0\n            ? [\n                args.length === 3 && refRight\n                    ? parseReferentialBinaryOperation(args[0], args[1], args[2])\n                    : parseValueBinaryOperationOrExpression(args),\n            ]\n            : []),\n    ], 'and', false));\n}\nexport function parseMergeThen(result) {\n    if (isString(result)) {\n        return RawNode.create([result], []);\n    }\n    if (isOperationNodeSource(result)) {\n        return result.toOperationNode();\n    }\n    return result;\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAC7C;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/C,OAAO,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,sOAAA,CAAA,kBAAe,AAAD,EAAE;QACnC,mOAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAC,KAAK,SAAS,EAAE,KAAK,QAAQ;WAC7C,QAAQ,KAAK,MAAM,GAAG,IACpB;YACE,KAAK,MAAM,KAAK,KAAK,WACf,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,IACzD,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;SAC/C,GACC,EAAE;KACX,EAAE,OAAO;AACd;AACO,SAAS,eAAe,MAAM;IACjC,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;QAClB,OAAO,+NAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YAAC;SAAO,EAAE,EAAE;IACtC;IACA,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS;QAC/B,OAAO,OAAO,eAAe;IACjC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/parse-utils.js"], "sourcesContent": ["/// <reference types=\"./parse-utils.d.ts\" />\nimport { JoinNode } from '../operation-node/join-node.js';\nimport { OverNode } from '../operation-node/over-node.js';\nimport { SelectQueryNode } from '../operation-node/select-query-node.js';\nimport { Join<PERSON>uilder } from '../query-builder/join-builder.js';\nimport { OverBuilder } from '../query-builder/over-builder.js';\nimport { createSelectQueryBuilder as newSelectQueryBuilder, } from '../query-builder/select-query-builder.js';\nimport { QueryCreator } from '../query-creator.js';\nimport { NOOP_QUERY_EXECUTOR } from '../query-executor/noop-query-executor.js';\nimport { createQueryId } from '../util/query-id.js';\nimport { parseTableExpression, parseTableExpressionOrList, } from './table-parser.js';\nexport function createSelectQueryBuilder() {\n    return newSelectQueryBuilder({\n        queryId: createQueryId(),\n        executor: NOOP_QUERY_EXECUTOR,\n        queryNode: SelectQueryNode.createFrom(parseTableExpressionOrList([])),\n    });\n}\nexport function createQueryCreator() {\n    return new QueryCreator({\n        executor: NOOP_QUERY_EXECUTOR,\n    });\n}\nexport function createJoinBuilder(joinType, table) {\n    return new JoinBuilder({\n        joinNode: JoinNode.create(joinType, parseTableExpression(table)),\n    });\n}\nexport function createOverBuilder() {\n    return new OverBuilder({\n        overNode: OverNode.create(),\n    });\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,SAAS;IACZ,OAAO,CAAA,GAAA,6OAAA,CAAA,2BAAqB,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;QACrB,UAAU,6OAAA,CAAA,sBAAmB;QAC7B,WAAW,2OAAA,CAAA,kBAAe,CAAC,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE,EAAE;IACvE;AACJ;AACO,SAAS;IACZ,OAAO,IAAI,+MAAA,CAAA,eAAY,CAAC;QACpB,UAAU,6OAAA,CAAA,sBAAmB;IACjC;AACJ;AACO,SAAS,kBAAkB,QAAQ,EAAE,KAAK;IAC7C,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC;QACnB,UAAU,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,UAAU,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;IAC7D;AACJ;AACO,SAAS;IACZ,OAAO,IAAI,kOAAA,CAAA,cAAW,CAAC;QACnB,UAAU,gOAAA,CAAA,WAAQ,CAAC,MAAM;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/join-parser.js"], "sourcesContent": ["/// <reference types=\"./join-parser.d.ts\" />\nimport { JoinNode } from '../operation-node/join-node.js';\nimport { parseReferentialBinaryOperation } from './binary-operation-parser.js';\nimport { createJoinBuilder } from './parse-utils.js';\nimport { parseTableExpression, } from './table-parser.js';\nexport function parseJoin(joinType, args) {\n    if (args.length === 3) {\n        return parseSingleOnJoin(joinType, args[0], args[1], args[2]);\n    }\n    else if (args.length === 2) {\n        return parseCallbackJoin(joinType, args[0], args[1]);\n    }\n    else if (args.length === 1) {\n        return parseOnlessJoin(joinType, args[0]);\n    }\n    else {\n        throw new Error('not implemented');\n    }\n}\nfunction parseCallbackJoin(joinType, from, callback) {\n    return callback(createJoin<PERSON>uilder(joinType, from)).toOperationNode();\n}\nfunction parseSingleOnJoin(joinType, from, lhsColumn, rhsColumn) {\n    return JoinNode.createWithOn(joinType, parseTableExpression(from), parseReferentialBinaryOperation(lhsColumn, '=', rhsColumn));\n}\nfunction parseOnlessJoin(joinType, from) {\n    return JoinNode.create(joinType, parseTableExpression(from));\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAC5C;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,QAAQ,EAAE,IAAI;IACpC,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO,kBAAkB,UAAU,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAChE,OACK,IAAI,KAAK,MAAM,KAAK,GAAG;QACxB,OAAO,kBAAkB,UAAU,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IACvD,OACK,IAAI,KAAK,MAAM,KAAK,GAAG;QACxB,OAAO,gBAAgB,UAAU,IAAI,CAAC,EAAE;IAC5C,OACK;QACD,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,SAAS,kBAAkB,QAAQ,EAAE,IAAI,EAAE,QAAQ;IAC/C,OAAO,SAAS,CAAA,GAAA,uNAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,OAAO,eAAe;AACtE;AACA,SAAS,kBAAkB,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS;IAC3D,OAAO,gOAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,UAAU,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,WAAW,KAAK;AACvH;AACA,SAAS,gBAAgB,QAAQ,EAAE,IAAI;IACnC,OAAO,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,UAAU,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/group-by-parser.js"], "sourcesContent": ["/// <reference types=\"./group-by-parser.d.ts\" />\nimport { GroupByItemNode } from '../operation-node/group-by-item-node.js';\nimport { expressionBuilder, } from '../expression/expression-builder.js';\nimport { isFunction } from '../util/object-utils.js';\nimport { parseReferenceExpressionOrList, } from './reference-parser.js';\nexport function parseGroupBy(groupBy) {\n    groupBy = isFunction(groupBy) ? groupBy(expressionBuilder()) : groupBy;\n    return parseReferenceExpressionOrList(groupBy).map(GroupByItemNode.create);\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;AACA;AACA;AACA;;;;;AACO,SAAS,aAAa,OAAO;IAChC,UAAU,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,WAAW,QAAQ,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD,OAAO;IAC/D,OAAO,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE,SAAS,GAAG,CAAC,+OAAA,CAAA,kBAAe,CAAC,MAAM;AAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/set-operation-parser.js"], "sourcesContent": ["/// <reference types=\"./set-operation-parser.d.ts\" />\nimport { createExpressionBuilder, } from '../expression/expression-builder.js';\nimport { SetOperationNode, } from '../operation-node/set-operation-node.js';\nimport { isFunction, isReadonlyArray } from '../util/object-utils.js';\nimport { parseExpression } from './expression-parser.js';\nexport function parseSetOperations(operator, expression, all) {\n    if (isFunction(expression)) {\n        expression = expression(createExpressionBuilder());\n    }\n    if (!isReadonlyArray(expression)) {\n        expression = [expression];\n    }\n    return expression.map((expr) => SetOperationNode.create(operator, parseExpression(expr), all));\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;;;;;AACO,SAAS,mBAAmB,QAAQ,EAAE,UAAU,EAAE,GAAG;IACxD,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QACxB,aAAa,WAAW,CAAA,GAAA,kOAAA,CAAA,0BAAuB,AAAD;IAClD;IACA,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;QAC9B,aAAa;YAAC;SAAW;IAC7B;IACA,OAAO,WAAW,GAAG,CAAC,CAAC,OAAS,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,UAAU,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/fetch-parser.js"], "sourcesContent": ["/// <reference types=\"./fetch-parser.d.ts\" />\nimport { FetchNode } from '../operation-node/fetch-node.js';\nimport { isBigInt, isNumber } from '../util/object-utils.js';\nexport function parseFetch(rowCount, modifier) {\n    if (!isNumber(rowCount) && !isBigInt(rowCount)) {\n        throw new Error(`Invalid fetch row count: ${rowCount}`);\n    }\n    if (!isFetchModifier(modifier)) {\n        throw new Error(`Invalid fetch modifier: ${modifier}`);\n    }\n    return FetchNode.create(rowCount, modifier);\n}\nfunction isFetchModifier(value) {\n    return value === 'only' || value === 'with ties';\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C;AACA;;;AACO,SAAS,WAAW,QAAQ,EAAE,QAAQ;IACzC,IAAI,CAAC,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,CAAC,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QAC5C,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,UAAU;IAC1D;IACA,IAAI,CAAC,gBAAgB,WAAW;QAC5B,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,UAAU;IACzD;IACA,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,UAAU;AACtC;AACA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,UAAU,UAAU,UAAU;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/unary-operation-parser.js"], "sourcesContent": ["/// <reference types=\"./unary-operation-parser.d.ts\" />\nimport { OperatorNode } from '../operation-node/operator-node.js';\nimport { UnaryOperationNode } from '../operation-node/unary-operation-node.js';\nimport { parseReferenceExpression, } from './reference-parser.js';\nexport function parseExists(operand) {\n    return parseUnaryOperation('exists', operand);\n}\nexport function parseNotExists(operand) {\n    return parseUnaryOperation('not exists', operand);\n}\nexport function parseUnaryOperation(operator, operand) {\n    return UnaryOperationNode.create(OperatorNode.create(operator), parseReferenceExpression(operand));\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;AACvD;AACA;AACA;;;;AACO,SAAS,YAAY,OAAO;IAC/B,OAAO,oBAAoB,UAAU;AACzC;AACO,SAAS,eAAe,OAAO;IAClC,OAAO,oBAAoB,cAAc;AAC7C;AACO,SAAS,oBAAoB,QAAQ,EAAE,OAAO;IACjD,OAAO,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,WAAW,CAAA,GAAA,4NAAA,CAAA,2BAAwB,AAAD,EAAE;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/data-type-parser.js"], "sourcesContent": ["/// <reference types=\"./data-type-parser.d.ts\" />\nimport { DataTypeNode, isColumnDataType, } from '../operation-node/data-type-node.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nexport function parseDataTypeExpression(dataType) {\n    if (isOperationNodeSource(dataType)) {\n        return dataType.toOperationNode();\n    }\n    if (isColumnDataType(dataType)) {\n        return DataTypeNode.create(dataType);\n    }\n    throw new Error(`invalid column data type ${JSON.stringify(dataType)}`);\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;AACjD;AACA;;;AACO,SAAS,wBAAwB,QAAQ;IAC5C,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW;QACjC,OAAO,SAAS,eAAe;IACnC;IACA,IAAI,CAAA,GAAA,wOAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;QAC5B,OAAO,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;IAC/B;IACA,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK,SAAS,CAAC,WAAW;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/expression-parser.js"], "sourcesContent": ["/// <reference types=\"./expression-parser.d.ts\" />\nimport { isAliasedExpression, isExpression, } from '../expression/expression.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nimport { expressionBuilder, } from '../expression/expression-builder.js';\nimport { isFunction } from '../util/object-utils.js';\nexport function parseExpression(exp) {\n    if (isOperationNodeSource(exp)) {\n        return exp.toOperationNode();\n    }\n    else if (isFunction(exp)) {\n        return exp(expressionBuilder()).toOperationNode();\n    }\n    throw new Error(`invalid expression: ${JSON.stringify(exp)}`);\n}\nexport function parseAliasedExpression(exp) {\n    if (isOperationNodeSource(exp)) {\n        return exp.toOperationNode();\n    }\n    else if (isFunction(exp)) {\n        return exp(expressionBuilder()).toOperationNode();\n    }\n    throw new Error(`invalid aliased expression: ${JSON.stringify(exp)}`);\n}\nexport function isExpressionOrFactory(obj) {\n    return isExpression(obj) || isAliasedExpression(obj) || isFunction(obj);\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;AACA;;;;;AACO,SAAS,gBAAgB,GAAG;IAC/B,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;QAC5B,OAAO,IAAI,eAAe;IAC9B,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,MAAM;QACtB,OAAO,IAAI,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD,KAAK,eAAe;IACnD;IACA,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,KAAK,SAAS,CAAC,MAAM;AAChE;AACO,SAAS,uBAAuB,GAAG;IACtC,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,MAAM;QAC5B,OAAO,IAAI,eAAe;IAC9B,OACK,IAAI,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE,MAAM;QACtB,OAAO,IAAI,CAAA,GAAA,kOAAA,CAAA,oBAAiB,AAAD,KAAK,eAAe;IACnD;IACA,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,MAAM;AACxE;AACO,SAAS,sBAAsB,GAAG;IACrC,OAAO,CAAA,GAAA,uNAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,CAAA,GAAA,uNAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,sNAAA,CAAA,aAAU,AAAD,EAAE;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/table-parser.js"], "sourcesContent": ["/// <reference types=\"./table-parser.d.ts\" />\nimport { isReadonlyArray, isString } from '../util/object-utils.js';\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { TableNode } from '../operation-node/table-node.js';\nimport { parseAliasedExpression, } from './expression-parser.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { isAliasedDynamicTableBuilder, } from '../dynamic/dynamic-table-builder.js';\nexport function parseTableExpressionOrList(table) {\n    if (isReadonlyArray(table)) {\n        return table.map((it) => parseTableExpression(it));\n    }\n    else {\n        return [parseTableExpression(table)];\n    }\n}\nexport function parseTableExpression(table) {\n    if (isString(table)) {\n        return parseAliasedTable(table);\n    }\n    else if (isAliasedDynamicTableBuilder(table)) {\n        return table.toOperationNode();\n    }\n    else {\n        return parseAliasedExpression(table);\n    }\n}\nexport function parseAliasedTable(from) {\n    const ALIAS_SEPARATOR = ' as ';\n    if (from.includes(ALIAS_SEPARATOR)) {\n        const [table, alias] = from.split(ALIAS_SEPARATOR).map(trim);\n        return AliasNode.create(parseTable(table), IdentifierNode.create(alias));\n    }\n    else {\n        return parseTable(from);\n    }\n}\nexport function parseTable(from) {\n    const SCHEMA_SEPARATOR = '.';\n    if (from.includes(SCHEMA_SEPARATOR)) {\n        const [schema, table] = from.split(SCHEMA_SEPARATOR).map(trim);\n        return TableNode.createWithSchema(schema, table);\n    }\n    else {\n        return TableNode.create(from);\n    }\n}\nfunction trim(str) {\n    return str.trim();\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;AAC7C;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,2BAA2B,KAAK;IAC5C,IAAI,CAAA,GAAA,sNAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;QACxB,OAAO,MAAM,GAAG,CAAC,CAAC,KAAO,qBAAqB;IAClD,OACK;QACD,OAAO;YAAC,qBAAqB;SAAO;IACxC;AACJ;AACO,SAAS,qBAAqB,KAAK;IACtC,IAAI,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;QACjB,OAAO,kBAAkB;IAC7B,OACK,IAAI,CAAA,GAAA,qOAAA,CAAA,+BAA4B,AAAD,EAAE,QAAQ;QAC1C,OAAO,MAAM,eAAe;IAChC,OACK;QACD,OAAO,CAAA,GAAA,6NAAA,CAAA,yBAAsB,AAAD,EAAE;IAClC;AACJ;AACO,SAAS,kBAAkB,IAAI;IAClC,MAAM,kBAAkB;IACxB,IAAI,KAAK,QAAQ,CAAC,kBAAkB;QAChC,MAAM,CAAC,OAAO,MAAM,GAAG,KAAK,KAAK,CAAC,iBAAiB,GAAG,CAAC;QACvD,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,WAAW,QAAQ,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACrE,OACK;QACD,OAAO,WAAW;IACtB;AACJ;AACO,SAAS,WAAW,IAAI;IAC3B,MAAM,mBAAmB;IACzB,IAAI,KAAK,QAAQ,CAAC,mBAAmB;QACjC,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC,kBAAkB,GAAG,CAAC;QACzD,OAAO,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,QAAQ;IAC9C,OACK;QACD,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC;IAC5B;AACJ;AACA,SAAS,KAAK,GAAG;IACb,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/default-value-parser.js"], "sourcesContent": ["/// <reference types=\"./default-value-parser.d.ts\" />\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nimport { ValueNode } from '../operation-node/value-node.js';\nexport function parseDefaultValueExpression(value) {\n    return isOperationNodeSource(value)\n        ? value.toOperationNode()\n        : ValueNode.createImmediate(value);\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;;;AACO,SAAS,4BAA4B,KAAK;IAC7C,OAAO,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,SACvB,MAAM,eAAe,KACrB,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/on-modify-action-parser.js"], "sourcesContent": ["/// <reference types=\"./on-modify-action-parser.d.ts\" />\nimport { ON_MODIFY_FOREIGN_ACTIONS, } from '../operation-node/references-node.js';\nexport function parseOnModifyForeignAction(action) {\n    if (ON_MODIFY_FOREIGN_ACTIONS.includes(action)) {\n        return action;\n    }\n    throw new Error(`invalid OnModifyForeignAction ${action}`);\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;AACxD;;AACO,SAAS,2BAA2B,MAAM;IAC7C,IAAI,sOAAA,CAAA,4BAAyB,CAAC,QAAQ,CAAC,SAAS;QAC5C,OAAO;IACX;IACA,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,QAAQ;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/on-commit-action-parse.js"], "sourcesContent": ["/// <reference types=\"./on-commit-action-parse.d.ts\" />\nimport { ON_COMMIT_ACTIONS, } from '../operation-node/create-table-node.js';\nexport function parseOnCommitAction(action) {\n    if (ON_COMMIT_ACTIONS.includes(action)) {\n        return action;\n    }\n    throw new Error(`invalid OnCommitAction ${action}`);\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;AACvD;;AACO,SAAS,oBAAoB,MAAM;IACtC,IAAI,2OAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,SAAS;QACpC,OAAO;IACX;IACA,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,QAAQ;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/identifier-parser.js"], "sourcesContent": ["/// <reference types=\"./identifier-parser.d.ts\" />\nimport { SchemableIdentifierNode } from '../operation-node/schemable-identifier-node.js';\nexport function parseSchemableIdentifier(id) {\n    const SCHEMA_SEPARATOR = '.';\n    if (id.includes(SCHEMA_SEPARATOR)) {\n        const parts = id.split(SCHEMA_SEPARATOR).map(trim);\n        if (parts.length === 2) {\n            return SchemableIdentifierNode.createWithSchema(parts[0], parts[1]);\n        }\n        else {\n            throw new Error(`invalid schemable identifier ${id}`);\n        }\n    }\n    else {\n        return SchemableIdentifierNode.create(id);\n    }\n}\nfunction trim(str) {\n    return str.trim();\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;AAClD;;AACO,SAAS,yBAAyB,EAAE;IACvC,MAAM,mBAAmB;IACzB,IAAI,GAAG,QAAQ,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,kBAAkB,GAAG,CAAC;QAC7C,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB,OAAO,mPAAA,CAAA,0BAAuB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QACtE,OACK;YACD,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,IAAI;QACxD;IACJ,OACK;QACD,OAAO,mPAAA,CAAA,0BAAuB,CAAC,MAAM,CAAC;IAC1C;AACJ;AACA,SAAS,KAAK,GAAG;IACb,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/parser/savepoint-parser.js"], "sourcesContent": ["/// <reference types=\"./savepoint-parser.d.ts\" />\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nexport function parseSavepointCommand(command, savepointName) {\n    return RawNode.createWithChildren([\n        RawNode.createWithSql(`${command} `),\n        IdentifierNode.create(savepointName), // ensures savepointName gets sanitized\n    ]);\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;AACjD;AACA;;;AACO,SAAS,sBAAsB,OAAO,EAAE,aAAa;IACxD,OAAO,+NAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC;QAC9B,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC;QACnC,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;KACzB;AACL", "ignoreList": [0], "debugId": null}}]}