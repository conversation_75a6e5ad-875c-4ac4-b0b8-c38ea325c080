{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/order-by-item-builder.js"], "sourcesContent": ["/// <reference types=\"./order-by-item-builder.d.ts\" />\nimport { CollateNode } from '../operation-node/collate-node.js';\nimport { OrderByItemNode } from '../operation-node/order-by-item-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class OrderByItemBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Adds `desc` to the `order by` item.\n     *\n     * See {@link asc} for the opposite.\n     */\n    desc() {\n        return new OrderByItemBuilder({\n            node: OrderByItemNode.cloneWith(this.#props.node, {\n                direction: RawNode.createWithSql('desc'),\n            }),\n        });\n    }\n    /**\n     * Adds `asc` to the `order by` item.\n     *\n     * See {@link desc} for the opposite.\n     */\n    asc() {\n        return new OrderByItemBuilder({\n            node: OrderByItemNode.cloneWith(this.#props.node, {\n                direction: RawNode.createWithSql('asc'),\n            }),\n        });\n    }\n    /**\n     * Adds `nulls last` to the `order by` item.\n     *\n     * This is only supported by some dialects like PostgreSQL and SQLite.\n     *\n     * See {@link nullsFirst} for the opposite.\n     */\n    nullsLast() {\n        return new OrderByItemBuilder({\n            node: OrderByItemNode.cloneWith(this.#props.node, { nulls: 'last' }),\n        });\n    }\n    /**\n     * Adds `nulls first` to the `order by` item.\n     *\n     * This is only supported by some dialects like PostgreSQL and SQLite.\n     *\n     * See {@link nullsLast} for the opposite.\n     */\n    nullsFirst() {\n        return new OrderByItemBuilder({\n            node: OrderByItemNode.cloneWith(this.#props.node, { nulls: 'first' }),\n        });\n    }\n    /**\n     * Adds `collate <collationName>` to the `order by` item.\n     */\n    collate(collation) {\n        return new OrderByItemBuilder({\n            node: OrderByItemNode.cloneWith(this.#props.node, {\n                collation: CollateNode.create(collation),\n            }),\n        });\n    }\n    toOperationNode() {\n        return this.#props.node;\n    }\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD;AACA;AACA;AACA;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;KAIC,GACD,OAAO;QACH,OAAO,IAAI,mBAAmB;YAC1B,MAAM,+OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,WAAW,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACrC;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,OAAO,IAAI,mBAAmB;YAC1B,MAAM,+OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,WAAW,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACrC;QACJ;IACJ;IACA;;;;;;KAMC,GACD,YAAY;QACR,OAAO,IAAI,mBAAmB;YAC1B,MAAM,+OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAAE,OAAO;YAAO;QACtE;IACJ;IACA;;;;;;KAMC,GACD,aAAa;QACT,OAAO,IAAI,mBAAmB;YAC1B,MAAM,+OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAAE,OAAO;YAAQ;QACvE;IACJ;IACA;;KAEC,GACD,QAAQ,SAAS,EAAE;QACf,OAAO,IAAI,mBAAmB;YAC1B,MAAM,+OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,WAAW,mOAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI;IAC3B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/join-builder.js"], "sourcesContent": ["/// <reference types=\"./join-builder.d.ts\" />\nimport { JoinNode } from '../operation-node/join-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { parseValueBinaryOperationOrExpression, parseReferentialBinaryOperation, } from '../parser/binary-operation-parser.js';\nimport { freeze } from '../util/object-utils.js';\nexport class JoinBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    on(...args) {\n        return new JoinBuilder({\n            ...this.#props,\n            joinNode: JoinNode.cloneWithOn(this.#props.joinNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    /**\n     * Just like {@link WhereInterface.whereRef} but adds an item to the join's\n     * `on` clause instead.\n     *\n     * See {@link WhereInterface.whereRef} for documentation and examples.\n     */\n    onRef(lhs, op, rhs) {\n        return new JoinBuilder({\n            ...this.#props,\n            joinNode: JoinNode.cloneWithOn(this.#props.joinNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    /**\n     * Adds `on true`.\n     */\n    onTrue() {\n        return new JoinBuilder({\n            ...this.#props,\n            joinNode: JoinNode.cloneWithOn(this.#props.joinNode, RawNode.createWithSql('true')),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.joinNode;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C;AACA;AACA;AACA;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,GAAG,GAAG,IAAI,EAAE;QACR,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,gOAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QAC/F;IACJ;IACA;;;;;KAKC,GACD,MAAM,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QAChB,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,gOAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QAClG;IACJ;IACA;;KAEC,GACD,SAAS;QACL,OAAO,IAAI,YAAY;YACnB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,gOAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;QAC/E;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/over-builder.js"], "sourcesContent": ["/// <reference types=\"./over-builder.d.ts\" />\nimport { OverNode } from '../operation-node/over-node.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { parseOrderBy, } from '../parser/order-by-parser.js';\nimport { parsePartitionBy, } from '../parser/partition-by-parser.js';\nimport { freeze } from '../util/object-utils.js';\nexport class OverBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    orderBy(...args) {\n        return new OverBuilder({\n            overNode: OverNode.cloneWithOrderByItems(this.#props.overNode, parseOrderBy(args)),\n        });\n    }\n    clearOrderBy() {\n        return new OverBuilder({\n            overNode: QueryNode.cloneWithoutOrderBy(this.#props.overNode),\n        });\n    }\n    partitionBy(partitionBy) {\n        return new OverBuilder({\n            overNode: OverNode.cloneWithPartitionByItems(this.#props.overNode, parsePartitionBy(partitionBy)),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.overNode;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAC7C;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,YAAY;YACnB,UAAU,gOAAA,CAAA,WAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QAChF;IACJ;IACA,eAAe;QACX,OAAO,IAAI,YAAY;YACnB,UAAU,iOAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QAChE;IACJ;IACA,YAAY,WAAW,EAAE;QACrB,OAAO,IAAI,YAAY;YACnB,UAAU,gOAAA,CAAA,WAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,EAAE,CAAA,GAAA,kOAAA,CAAA,mBAAgB,AAAD,EAAE;QACxF;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;IAC/B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/insert-result.js"], "sourcesContent": ["/// <reference types=\"./insert-result.d.ts\" />\n/**\n * The result of an insert query.\n *\n * If the table has an auto incrementing primary key {@link insertId} will hold\n * the generated id on dialects that support it. For example PostgreSQL doesn't\n * return the id by default and {@link insertId} is undefined. On PostgreSQL you\n * need to use {@link ReturningInterface.returning} or {@link ReturningInterface.returningAll}\n * to get out the inserted id.\n *\n * {@link numInsertedOrUpdatedRows} holds the number of (actually) inserted rows.\n * On MySQL, updated rows are counted twice when using `on duplicate key update`.\n *\n * ### Examples\n *\n * ```ts\n * import type { <PERSON><PERSON><PERSON> } from 'type-editor' // imaginary module\n *\n * async function insertPerson(person: <PERSON><PERSON><PERSON>) {\n *   const result = await db\n *     .insertInto('person')\n *     .values(person)\n *     .executeTakeFirstOrThrow()\n *\n *   console.log(result.insertId) // relevant on MySQL\n *   console.log(result.numInsertedOrUpdatedRows) // always relevant\n * }\n * ```\n */\nexport class InsertResult {\n    /**\n     * The auto incrementing primary key of the inserted row.\n     *\n     * This property can be undefined when the query contains an `on conflict`\n     * clause that makes the query succeed even when nothing gets inserted.\n     *\n     * This property is always undefined on dialects like PostgreSQL that\n     * don't return the inserted id by default. On those dialects you need\n     * to use the {@link ReturningInterface.returning | returning} method.\n     */\n    insertId;\n    /**\n     * Affected rows count.\n     */\n    numInsertedOrUpdatedRows;\n    constructor(insertId, numInsertedOrUpdatedRows) {\n        this.insertId = insertId;\n        this.numInsertedOrUpdatedRows = numInsertedOrUpdatedRows;\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;AAC9C;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC;;;AACM,MAAM;IACT;;;;;;;;;KASC,GACD,SAAS;IACT;;KAEC,GACD,yBAAyB;IACzB,YAAY,QAAQ,EAAE,wBAAwB,CAAE;QAC5C,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,wBAAwB,GAAG;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/no-result-error.js"], "sourcesContent": ["/// <reference types=\"./no-result-error.d.ts\" />\nexport class NoResultError extends Error {\n    /**\n     * The operation node tree of the query that was executed.\n     */\n    node;\n    constructor(node) {\n        super('no result');\n        this.node = node;\n    }\n}\nexport function isNoResultErrorConstructor(fn) {\n    return Object.prototype.hasOwnProperty.call(fn, 'prototype');\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AACzC,MAAM,sBAAsB;IAC/B;;KAEC,GACD,KAAK;IACL,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACO,SAAS,2BAA2B,EAAE;IACzC,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/on-conflict-builder.js"], "sourcesContent": ["/// <reference types=\"./on-conflict-builder.d.ts\" />\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { OnConflictNode } from '../operation-node/on-conflict-node.js';\nimport { parseValueBinaryOperationOrExpression, parseReferentialBinaryOperation, } from '../parser/binary-operation-parser.js';\nimport { parseUpdateObjectExpression, } from '../parser/update-set-parser.js';\nimport { freeze } from '../util/object-utils.js';\nexport class OnConflictBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Specify a single column as the conflict target.\n     *\n     * Also see the {@link columns}, {@link constraint} and {@link expression}\n     * methods for alternative ways to specify the conflict target.\n     */\n    column(column) {\n        const columnNode = ColumnNode.create(column);\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                columns: this.#props.onConflictNode.columns\n                    ? freeze([...this.#props.onConflictNode.columns, columnNode])\n                    : freeze([columnNode]),\n            }),\n        });\n    }\n    /**\n     * Specify a list of columns as the conflict target.\n     *\n     * Also see the {@link column}, {@link constraint} and {@link expression}\n     * methods for alternative ways to specify the conflict target.\n     */\n    columns(columns) {\n        const columnNodes = columns.map(ColumnNode.create);\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                columns: this.#props.onConflictNode.columns\n                    ? freeze([...this.#props.onConflictNode.columns, ...columnNodes])\n                    : freeze(columnNodes),\n            }),\n        });\n    }\n    /**\n     * Specify a specific constraint by name as the conflict target.\n     *\n     * Also see the {@link column}, {@link columns} and {@link expression}\n     * methods for alternative ways to specify the conflict target.\n     */\n    constraint(constraintName) {\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                constraint: IdentifierNode.create(constraintName),\n            }),\n        });\n    }\n    /**\n     * Specify an expression as the conflict target.\n     *\n     * This can be used if the unique index is an expression index.\n     *\n     * Also see the {@link column}, {@link columns} and {@link constraint}\n     * methods for alternative ways to specify the conflict target.\n     */\n    expression(expression) {\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                indexExpression: expression.toOperationNode(),\n            }),\n        });\n    }\n    where(...args) {\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithIndexWhere(this.#props.onConflictNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    whereRef(lhs, op, rhs) {\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithIndexWhere(this.#props.onConflictNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    clearWhere() {\n        return new OnConflictBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithoutIndexWhere(this.#props.onConflictNode),\n        });\n    }\n    /**\n     * Adds the \"do nothing\" conflict action.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const id = 1\n     * const first_name = 'John'\n     *\n     * await db\n     *   .insertInto('person')\n     *   .values({ first_name, id })\n     *   .onConflict((oc) => oc\n     *     .column('id')\n     *     .doNothing()\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"id\")\n     * values ($1, $2)\n     * on conflict (\"id\") do nothing\n     * ```\n     */\n    doNothing() {\n        return new OnConflictDoNothingBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                doNothing: true,\n            }),\n        });\n    }\n    /**\n     * Adds the \"do update set\" conflict action.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const id = 1\n     * const first_name = 'John'\n     *\n     * await db\n     *   .insertInto('person')\n     *   .values({ first_name, id })\n     *   .onConflict((oc) => oc\n     *     .column('id')\n     *     .doUpdateSet({ first_name })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"id\")\n     * values ($1, $2)\n     * on conflict (\"id\")\n     * do update set \"first_name\" = $3\n     * ```\n     *\n     * In the next example we use the `ref` method to reference\n     * columns of the virtual table `excluded` in a type-safe way\n     * to create an upsert operation:\n     *\n     * ```ts\n     * import type { NewPerson } from 'type-editor' // imaginary module\n     *\n     * async function upsertPerson(person: NewPerson): Promise<void> {\n     *   await db.insertInto('person')\n     *     .values(person)\n     *     .onConflict((oc) => oc\n     *       .column('id')\n     *       .doUpdateSet((eb) => ({\n     *         first_name: eb.ref('excluded.first_name'),\n     *         last_name: eb.ref('excluded.last_name')\n     *       })\n     *     )\n     *   )\n     *   .execute()\n     * }\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"last_name\")\n     * values ($1, $2)\n     * on conflict (\"id\")\n     * do update set\n     *  \"first_name\" = excluded.\"first_name\",\n     *  \"last_name\" = excluded.\"last_name\"\n     * ```\n     */\n    doUpdateSet(update) {\n        return new OnConflictUpdateBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWith(this.#props.onConflictNode, {\n                updates: parseUpdateObjectExpression(update),\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n}\nexport class OnConflictDoNothingBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    toOperationNode() {\n        return this.#props.onConflictNode;\n    }\n}\nexport class OnConflictUpdateBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    where(...args) {\n        return new OnConflictUpdateBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithUpdateWhere(this.#props.onConflictNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    /**\n     * Specify a where condition for the update operation.\n     *\n     * See {@link WhereInterface.whereRef} for more info.\n     */\n    whereRef(lhs, op, rhs) {\n        return new OnConflictUpdateBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithUpdateWhere(this.#props.onConflictNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    clearWhere() {\n        return new OnConflictUpdateBuilder({\n            ...this.#props,\n            onConflictNode: OnConflictNode.cloneWithoutUpdateWhere(this.#props.onConflictNode),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.onConflictNode;\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;AACpD;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;KAKC,GACD,OAAO,MAAM,EAAE;QACX,MAAM,aAAa,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC;QACrC,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,CAAC,OAAO,GACrC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;uBAAI,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,CAAC,OAAO;oBAAE;iBAAW,IAC1D,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;oBAAC;iBAAW;YAC7B;QACJ;IACJ;IACA;;;;;KAKC,GACD,QAAQ,OAAO,EAAE;QACb,MAAM,cAAc,QAAQ,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM;QACjD,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,CAAC,OAAO,GACrC,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;uBAAI,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,CAAC,OAAO;uBAAK;iBAAY,IAC9D,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;YACjB;QACJ;IACJ;IACA;;;;;KAKC,GACD,WAAW,cAAc,EAAE;QACvB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,YAAY,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;YACtC;QACJ;IACJ;IACA;;;;;;;KAOC,GACD,WAAW,UAAU,EAAE;QACnB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,iBAAiB,WAAW,eAAe;YAC/C;QACJ;IACJ;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACzH;IACJ;IACA,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QAC5H;IACJ;IACA,aAAa;QACT,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc;QACpF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;KA0BC,GACD,YAAY;QACR,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,WAAW;YACf;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4DC,GACD,YAAY,MAAM,EAAE;QAChB,OAAO,IAAI,wBAAwB;YAC/B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE;gBACjE,SAAS,CAAA,GAAA,gOAAA,CAAA,8BAA2B,AAAD,EAAE;YACzC;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc;IACrC;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,wBAAwB;YAC/B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QAC1H;IACJ;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,wBAAwB;YAC/B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QAC7H;IACJ;IACA,aAAa;QACT,OAAO,IAAI,wBAAwB;YAC/B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc;QACrF;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,cAAc;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/insert-query-builder.js"], "sourcesContent": ["/// <reference types=\"./insert-query-builder.d.ts\" />\nimport { parseSelectArg, parseSelectAll, } from '../parser/select-parser.js';\nimport { parseInsertExpression, } from '../parser/insert-values-parser.js';\nimport { InsertQueryNode } from '../operation-node/insert-query-node.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { parseUpdateObjectExpression, } from '../parser/update-set-parser.js';\nimport { freeze } from '../util/object-utils.js';\nimport { OnDuplicateKeyNode } from '../operation-node/on-duplicate-key-node.js';\nimport { InsertResult } from './insert-result.js';\nimport { isNoResultErrorConstructor, NoResultError, } from './no-result-error.js';\nimport { parseExpression, } from '../parser/expression-parser.js';\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { OnConflictBuilder, } from './on-conflict-builder.js';\nimport { OnConflictNode } from '../operation-node/on-conflict-node.js';\nimport { parseTop } from '../parser/top-parser.js';\nimport { OrActionNode } from '../operation-node/or-action-node.js';\nexport class InsertQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Sets the values to insert for an {@link Kysely.insertInto | insert} query.\n     *\n     * This method takes an object whose keys are column names and values are\n     * values to insert. In addition to the column's type, the values can be\n     * raw {@link sql} snippets or select queries.\n     *\n     * You must provide all fields you haven't explicitly marked as nullable\n     * or optional using {@link Generated} or {@link ColumnType}.\n     *\n     * The return value of an `insert` query is an instance of {@link InsertResult}. The\n     * {@link InsertResult.insertId | insertId} field holds the auto incremented primary\n     * key if the database returned one.\n     *\n     * On PostgreSQL and some other dialects, you need to call `returning` to get\n     * something out of the query.\n     *\n     * Also see the {@link expression} method for inserting the result of a select\n     * query or any other expression.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"insert\", \"Single row\", 10) -->\n     *\n     * Insert a single row:\n     *\n     * ```ts\n     * const result = await db\n     *   .insertInto('person')\n     *   .values({\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston',\n     *     age: 40\n     *   })\n     *   .executeTakeFirst()\n     *\n     * // `insertId` is only available on dialects that\n     * // automatically return the id of the inserted row\n     * // such as MySQL and SQLite. On PostgreSQL, for example,\n     * // you need to add a `returning` clause to the query to\n     * // get anything out. See the \"returning data\" example.\n     * console.log(result.insertId)\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * insert into `person` (`first_name`, `last_name`, `age`) values (?, ?, ?)\n     * ```\n     *\n     * <!-- siteExample(\"insert\", \"Multiple rows\", 20) -->\n     *\n     * On dialects that support it (for example PostgreSQL) you can insert multiple\n     * rows by providing an array. Note that the return value is once again very\n     * dialect-specific. Some databases may only return the id of the *last* inserted\n     * row and some return nothing at all unless you call `returning`.\n     *\n     * ```ts\n     * await db\n     *   .insertInto('person')\n     *   .values([{\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston',\n     *     age: 40,\n     *   }, {\n     *     first_name: 'Arnold',\n     *     last_name: 'Schwarzenegger',\n     *     age: 70,\n     *   }])\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"last_name\", \"age\") values (($1, $2, $3), ($4, $5, $6))\n     * ```\n     *\n     * <!-- siteExample(\"insert\", \"Returning data\", 30) -->\n     *\n     * On supported dialects like PostgreSQL you need to chain `returning` to the query to get\n     * the inserted row's columns (or any other expression) as the return value. `returning`\n     * works just like `select`. Refer to `select` method's examples and documentation for\n     * more info.\n     *\n     * ```ts\n     * const result = await db\n     *   .insertInto('person')\n     *   .values({\n     *     first_name: 'Jennifer',\n     *     last_name: 'Aniston',\n     *     age: 40,\n     *   })\n     *   .returning(['id', 'first_name as name'])\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"last_name\", \"age\") values ($1, $2, $3) returning \"id\", \"first_name\" as \"name\"\n     * ```\n     *\n     * <!-- siteExample(\"insert\", \"Complex values\", 40) -->\n     *\n     * In addition to primitives, the values can also be arbitrary expressions.\n     * You can build the expressions by using a callback and calling the methods\n     * on the expression builder passed to it:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * const ani = \"Ani\"\n     * const ston = \"ston\"\n     *\n     * const result = await db\n     *   .insertInto('person')\n     *   .values(({ ref, selectFrom, fn }) => ({\n     *     first_name: 'Jennifer',\n     *     last_name: sql<string>`concat(${ani}, ${ston})`,\n     *     middle_name: ref('first_name'),\n     *     age: selectFrom('person')\n     *       .select(fn.avg<number>('age').as('avg_age')),\n     *   }))\n     *   .executeTakeFirst()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\n     *   \"first_name\",\n     *   \"last_name\",\n     *   \"middle_name\",\n     *   \"age\"\n     * )\n     * values (\n     *   $1,\n     *   concat($2, $3),\n     *   \"first_name\",\n     *   (select avg(\"age\") as \"avg_age\" from \"person\")\n     * )\n     * ```\n     *\n     * You can also use the callback version of subqueries or raw expressions:\n     *\n     * ```ts\n     * await db.with('jennifer', (db) => db\n     *   .selectFrom('person')\n     *   .where('first_name', '=', 'Jennifer')\n     *   .select(['id', 'first_name', 'gender'])\n     *   .limit(1)\n     * ).insertInto('pet').values((eb) => ({\n     *   owner_id: eb.selectFrom('jennifer').select('id'),\n     *   name: eb.selectFrom('jennifer').select('first_name'),\n     *   species: 'cat',\n     * }))\n     * .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * with \"jennifer\" as (\n     *   select \"id\", \"first_name\", \"gender\"\n     *   from \"person\"\n     *   where \"first_name\" = $1\n     *   limit $2\n     * )\n     * insert into \"pet\" (\"owner_id\", \"name\", \"species\")\n     * values (\n     *  (select \"id\" from \"jennifer\"),\n     *  (select \"first_name\" from \"jennifer\"),\n     *  $3\n     * )\n     * ```\n     */\n    values(insert) {\n        const [columns, values] = parseInsertExpression(insert);\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                columns,\n                values,\n            }),\n        });\n    }\n    /**\n     * Sets the columns to insert.\n     *\n     * The {@link values} method sets both the columns and the values and this method\n     * is not needed. But if you are using the {@link expression} method, you can use\n     * this method to set the columns to insert.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .columns(['first_name'])\n     *   .expression((eb) => eb.selectFrom('pet').select('pet.name'))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\")\n     * select \"pet\".\"name\" from \"pet\"\n     * ```\n     */\n    columns(columns) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                columns: freeze(columns.map(ColumnNode.create)),\n            }),\n        });\n    }\n    /**\n     * Insert an arbitrary expression. For example the result of a select query.\n     *\n     * ### Examples\n     *\n     * <!-- siteExample(\"insert\", \"Insert subquery\", 50) -->\n     *\n     * You can create an `INSERT INTO SELECT FROM` query using the `expression` method.\n     * This API doesn't follow our WYSIWYG principles and might be a bit difficult to\n     * remember. The reasons for this design stem from implementation difficulties.\n     *\n     * ```ts\n     * const result = await db.insertInto('person')\n     *   .columns(['first_name', 'last_name', 'age'])\n     *   .expression((eb) => eb\n     *     .selectFrom('pet')\n     *     .select((eb) => [\n     *       'pet.name',\n     *       eb.val('Petson').as('last_name'),\n     *       eb.lit(7).as('age'),\n     *     ])\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"last_name\", \"age\")\n     * select \"pet\".\"name\", $1 as \"last_name\", 7 as \"age from \"pet\"\n     * ```\n     */\n    expression(expression) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                values: parseExpression(expression),\n            }),\n        });\n    }\n    /**\n     * Creates an `insert into \"person\" default values` query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .defaultValues()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" default values\n     * ```\n     */\n    defaultValues() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                defaultValues: true,\n            }),\n        });\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.insertInto('person')\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'male',\n     *   })\n     *   .modifyEnd(sql`-- This is a comment`)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * insert into `person` (\"first_name\", \"last_name\", \"gender\")\n     * values (?, ?, ?) -- This is a comment\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert ignore into` query.\n     *\n     * This is only supported by some dialects like MySQL.\n     *\n     * To avoid a footgun, when invoked with the SQLite dialect, this method will\n     * be handled like {@link orIgnore}. See also, {@link orAbort}, {@link orFail},\n     * {@link orReplace}, and {@link orRollback}.\n     *\n     * If you use the ignore modifier, ignorable errors that occur while executing the\n     * insert statement are ignored. For example, without ignore, a row that duplicates\n     * an existing unique index or primary key value in the table causes a duplicate-key\n     * error and the statement is aborted. With ignore, the row is discarded and no error\n     * occurs.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .ignore()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * insert ignore into `person` (`first_name`, `last_name`, `gender`) values (?, ?, ?)\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or ignore into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     */\n    ignore() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('ignore'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert or ignore into` query.\n     *\n     * This is only supported by some dialects like SQLite.\n     *\n     * To avoid a footgun, when invoked with the MySQL dialect, this method will\n     * be handled like {@link ignore}.\n     *\n     * See also, {@link orAbort}, {@link orFail}, {@link orReplace}, and {@link orRollback}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .orIgnore()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or ignore into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * insert ignore into `person` (`first_name`, `last_name`, `gender`) values (?, ?, ?)\n     * ```\n     */\n    orIgnore() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('ignore'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert or abort into` query.\n     *\n     * This is only supported by some dialects like SQLite.\n     *\n     * See also, {@link orIgnore}, {@link orFail}, {@link orReplace}, and {@link orRollback}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .orAbort()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or abort into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     */\n    orAbort() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('abort'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert or fail into` query.\n     *\n     * This is only supported by some dialects like SQLite.\n     *\n     * See also, {@link orIgnore}, {@link orAbort}, {@link orReplace}, and {@link orRollback}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .orFail()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or fail into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     */\n    orFail() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('fail'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert or replace into` query.\n     *\n     * This is only supported by some dialects like SQLite.\n     *\n     * You can also use {@link Kysely.replaceInto} to achieve the same result.\n     *\n     * See also, {@link orIgnore}, {@link orAbort}, {@link orFail}, and {@link orRollback}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .orReplace()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or replace into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     */\n    orReplace() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('replace'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert or rollback into` query.\n     *\n     * This is only supported by some dialects like SQLite.\n     *\n     * See also, {@link orIgnore}, {@link orAbort}, {@link orFail}, and {@link orReplace}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .orRollback()\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'female',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * insert or rollback into \"person\" (\"first_name\", \"last_name\", \"gender\") values (?, ?, ?)\n     * ```\n     */\n    orRollback() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                orAction: OrActionNode.create('rollback'),\n            }),\n        });\n    }\n    /**\n     * Changes an `insert into` query to an `insert top into` query.\n     *\n     * `top` clause is only supported by some dialects like MS SQL Server.\n     *\n     * ### Examples\n     *\n     * Insert the first 5 rows:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.insertInto('person')\n     *   .top(5)\n     *   .columns(['first_name', 'gender'])\n     *   .expression(\n     *     (eb) => eb.selectFrom('pet').select(['name', sql.lit('other').as('gender')])\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * insert top(5) into \"person\" (\"first_name\", \"gender\") select \"name\", 'other' as \"gender\" from \"pet\"\n     * ```\n     *\n     * Insert the first 50 percent of rows:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.insertInto('person')\n     *   .top(50, 'percent')\n     *   .columns(['first_name', 'gender'])\n     *   .expression(\n     *     (eb) => eb.selectFrom('pet').select(['name', sql.lit('other').as('gender')])\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * insert top(50) percent into \"person\" (\"first_name\", \"gender\") select \"name\", 'other' as \"gender\" from \"pet\"\n     * ```\n     */\n    top(expression, modifiers) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    /**\n     * Adds an `on conflict` clause to the query.\n     *\n     * `on conflict` is only supported by some dialects like PostgreSQL and SQLite. On MySQL\n     * you can use {@link ignore} and {@link onDuplicateKeyUpdate} to achieve similar results.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db\n     *   .insertInto('pet')\n     *   .values({\n     *     name: 'Catto',\n     *     species: 'cat',\n     *     owner_id: 3,\n     *   })\n     *   .onConflict((oc) => oc\n     *     .column('name')\n     *     .doUpdateSet({ species: 'hamster' })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"pet\" (\"name\", \"species\", \"owner_id\")\n     * values ($1, $2, $3)\n     * on conflict (\"name\")\n     * do update set \"species\" = $4\n     * ```\n     *\n     * You can provide the name of the constraint instead of a column name:\n     *\n     * ```ts\n     * await db\n     *   .insertInto('pet')\n     *   .values({\n     *     name: 'Catto',\n     *     species: 'cat',\n     *     owner_id: 3,\n     *   })\n     *   .onConflict((oc) => oc\n     *     .constraint('pet_name_key')\n     *     .doUpdateSet({ species: 'hamster' })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"pet\" (\"name\", \"species\", \"owner_id\")\n     * values ($1, $2, $3)\n     * on conflict on constraint \"pet_name_key\"\n     * do update set \"species\" = $4\n     * ```\n     *\n     * You can also specify an expression as the conflict target in case\n     * the unique index is an expression index:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db\n     *   .insertInto('pet')\n     *   .values({\n     *     name: 'Catto',\n     *     species: 'cat',\n     *     owner_id: 3,\n     *   })\n     *   .onConflict((oc) => oc\n     *     .expression(sql<string>`lower(name)`)\n     *     .doUpdateSet({ species: 'hamster' })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"pet\" (\"name\", \"species\", \"owner_id\")\n     * values ($1, $2, $3)\n     * on conflict (lower(name))\n     * do update set \"species\" = $4\n     * ```\n     *\n     * You can add a filter for the update statement like this:\n     *\n     * ```ts\n     * await db\n     *   .insertInto('pet')\n     *   .values({\n     *     name: 'Catto',\n     *     species: 'cat',\n     *     owner_id: 3,\n     *   })\n     *   .onConflict((oc) => oc\n     *     .column('name')\n     *     .doUpdateSet({ species: 'hamster' })\n     *     .where('excluded.name', '!=', 'Catto')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"pet\" (\"name\", \"species\", \"owner_id\")\n     * values ($1, $2, $3)\n     * on conflict (\"name\")\n     * do update set \"species\" = $4\n     * where \"excluded\".\"name\" != $5\n     * ```\n     *\n     * You can create an `on conflict do nothing` clauses like this:\n     *\n     * ```ts\n     * await db\n     *   .insertInto('pet')\n     *   .values({\n     *     name: 'Catto',\n     *     species: 'cat',\n     *     owner_id: 3,\n     *   })\n     *   .onConflict((oc) => oc\n     *     .column('name')\n     *     .doNothing()\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"pet\" (\"name\", \"species\", \"owner_id\")\n     * values ($1, $2, $3)\n     * on conflict (\"name\") do nothing\n     * ```\n     *\n     * You can refer to the columns of the virtual `excluded` table\n     * in a type-safe way using a callback and the `ref` method of\n     * `ExpressionBuilder`:\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .values({\n     *     id: 1,\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'male',\n     *   })\n     *   .onConflict(oc => oc\n     *     .column('id')\n     *     .doUpdateSet({\n     *       first_name: (eb) => eb.ref('excluded.first_name'),\n     *       last_name: (eb) => eb.ref('excluded.last_name')\n     *     })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"id\", \"first_name\", \"last_name\", \"gender\")\n     * values ($1, $2, $3, $4)\n     * on conflict (\"id\")\n     * do update set\n     *  \"first_name\" = \"excluded\".\"first_name\",\n     *  \"last_name\" = \"excluded\".\"last_name\"\n     * ```\n     */\n    onConflict(callback) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                onConflict: callback(new OnConflictBuilder({\n                    onConflictNode: OnConflictNode.create(),\n                })).toOperationNode(),\n            }),\n        });\n    }\n    /**\n     * Adds `on duplicate key update` to the query.\n     *\n     * If you specify `on duplicate key update`, and a row is inserted that would cause\n     * a duplicate value in a unique index or primary key, an update of the old row occurs.\n     *\n     * This is only implemented by some dialects like MySQL. On most dialects you should\n     * use {@link onConflict} instead.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db\n     *   .insertInto('person')\n     *   .values({\n     *     id: 1,\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'male',\n     *   })\n     *   .onDuplicateKeyUpdate({ updated_at: new Date().toISOString() })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * insert into `person` (`id`, `first_name`, `last_name`, `gender`)\n     * values (?, ?, ?, ?)\n     * on duplicate key update `updated_at` = ?\n     * ```\n     */\n    onDuplicateKeyUpdate(update) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: InsertQueryNode.cloneWith(this.#props.queryNode, {\n                onDuplicateKey: OnDuplicateKeyNode.create(parseUpdateObjectExpression(update)),\n            }),\n        });\n    }\n    returning(selection) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectArg(selection)),\n        });\n    }\n    returningAll() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectAll()),\n        });\n    }\n    output(args) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    outputAll(table) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    /**\n     * Clears all `returning` clauses from the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.insertInto('person')\n     *   .values({ first_name: 'James', last_name: 'Smith', gender: 'male' })\n     *   .returning(['first_name'])\n     *   .clearReturning()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL(PostgreSQL):\n     *\n     * ```sql\n     * insert into \"person\" (\"first_name\", \"last_name\", \"gender\") values ($1, $2, $3)\n     * ```\n     */\n    clearReturning() {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutReturning(this.#props.queryNode),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     *\n     * If you want to conditionally call a method on `this`, see\n     * the {@link $if} method.\n     *\n     * ### Examples\n     *\n     * The next example uses a helper function `log` to log a query:\n     *\n     * ```ts\n     * import type { Compilable } from 'kysely'\n     *\n     * function log<T extends Compilable>(qb: T): T {\n     *   console.log(qb.compile())\n     *   return qb\n     * }\n     *\n     * await db.insertInto('person')\n     *   .values({ first_name: 'John', last_name: 'Doe', gender: 'male' })\n     *   .$call(log)\n     *   .execute()\n     * ```\n     */\n    $call(func) {\n        return func(this);\n    }\n    /**\n     * Call `func(this)` if `condition` is true.\n     *\n     * This method is especially handy with optional selects. Any `returning` or `returningAll`\n     * method calls add columns as optional fields to the output type when called inside\n     * the `func` callback. This is because we can't know if those selections were actually\n     * made before running the code.\n     *\n     * You can also call any other methods inside the callback.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { NewPerson } from 'type-editor' // imaginary module\n     *\n     * async function insertPerson(values: NewPerson, returnLastName: boolean) {\n     *   return await db\n     *     .insertInto('person')\n     *     .values(values)\n     *     .returning(['id', 'first_name'])\n     *     .$if(returnLastName, (qb) => qb.returning('last_name'))\n     *     .executeTakeFirstOrThrow()\n     * }\n     * ```\n     *\n     * Any selections added inside the `if` callback will be added as optional fields to the\n     * output type since we can't know if the selections were actually made before running\n     * the code. In the example above the return type of the `insertPerson` function is:\n     *\n     * ```ts\n     * Promise<{\n     *   id: number\n     *   first_name: string\n     *   last_name?: string\n     * }>\n     * ```\n     */\n    $if(condition, func) {\n        if (condition) {\n            return func(this);\n        }\n        return new InsertQueryBuilder({\n            ...this.#props,\n        });\n    }\n    /**\n     * Change the output type of the query.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `InsertQueryBuilder` with a new output type.\n     */\n    $castTo() {\n        return new InsertQueryBuilder(this.#props);\n    }\n    /**\n     * Narrows (parts of) the output type of the query.\n     *\n     * Kysely tries to be as type-safe as possible, but in some cases we have to make\n     * compromises for better maintainability and compilation performance. At present,\n     * Kysely doesn't narrow the output type of the query based on {@link values} input\n     * when using {@link returning} or {@link returningAll}.\n     *\n     * This utility method is very useful for these situations, as it removes unncessary\n     * runtime assertion/guard code. Its input type is limited to the output type\n     * of the query, so you can't add a column that doesn't exist, or change a column's\n     * type to something that doesn't exist in its union type.\n     *\n     * ### Examples\n     *\n     * Turn this code:\n     *\n     * ```ts\n     * import type { Person } from 'type-editor' // imaginary module\n     *\n     * const person = await db.insertInto('person')\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'male',\n     *     nullable_column: 'hell yeah!'\n     *   })\n     *   .returningAll()\n     *   .executeTakeFirstOrThrow()\n     *\n     * if (isWithNoNullValue(person)) {\n     *   functionThatExpectsPersonWithNonNullValue(person)\n     * }\n     *\n     * function isWithNoNullValue(person: Person): person is Person & { nullable_column: string } {\n     *   return person.nullable_column != null\n     * }\n     * ```\n     *\n     * Into this:\n     *\n     * ```ts\n     * import type { NotNull } from 'kysely'\n     *\n     * const person = await db.insertInto('person')\n     *   .values({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *     gender: 'male',\n     *     nullable_column: 'hell yeah!'\n     *   })\n     *   .returningAll()\n     *   .$narrowType<{ nullable_column: NotNull }>()\n     *   .executeTakeFirstOrThrow()\n     *\n     * functionThatExpectsPersonWithNonNullValue(person)\n     * ```\n     */\n    $narrowType() {\n        return new InsertQueryBuilder(this.#props);\n    }\n    /**\n     * Asserts that query's output row type equals the given type `T`.\n     *\n     * This method can be used to simplify excessively complex types to make TypeScript happy\n     * and much faster.\n     *\n     * Kysely uses complex type magic to achieve its type safety. This complexity is sometimes too much\n     * for TypeScript and you get errors like this:\n     *\n     * ```\n     * error TS2589: Type instantiation is excessively deep and possibly infinite.\n     * ```\n     *\n     * In these case you can often use this method to help TypeScript a little bit. When you use this\n     * method to assert the output type of a query, Kysely can drop the complex output type that\n     * consists of multiple nested helper types and replace it with the simple asserted type.\n     *\n     * Using this method doesn't reduce type safety at all. You have to pass in a type that is\n     * structurally equal to the current type.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { NewPerson, NewPet, Species } from 'type-editor' // imaginary module\n     *\n     * async function insertPersonAndPet(person: NewPerson, pet: Omit<NewPet, 'owner_id'>) {\n     *   return await db\n     *     .with('new_person', (qb) => qb\n     *       .insertInto('person')\n     *       .values(person)\n     *       .returning('id')\n     *       .$assertType<{ id: number }>()\n     *     )\n     *     .with('new_pet', (qb) => qb\n     *       .insertInto('pet')\n     *       .values((eb) => ({\n     *         owner_id: eb.selectFrom('new_person').select('id'),\n     *         ...pet\n     *       }))\n     *       .returning(['name as pet_name', 'species'])\n     *       .$assertType<{ pet_name: string, species: Species }>()\n     *     )\n     *     .selectFrom(['new_person', 'new_pet'])\n     *     .selectAll()\n     *     .executeTakeFirstOrThrow()\n     * }\n     * ```\n     */\n    $assertType() {\n        return new InsertQueryBuilder(this.#props);\n    }\n    /**\n     * Returns a copy of this InsertQueryBuilder instance with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new InsertQueryBuilder({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.queryNode, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    /**\n     * Executes the query and returns an array of rows.\n     *\n     * Also see the {@link executeTakeFirst} and {@link executeTakeFirstOrThrow} methods.\n     */\n    async execute() {\n        const compiledQuery = this.compile();\n        const result = await this.#props.executor.executeQuery(compiledQuery, this.#props.queryId);\n        const { adapter } = this.#props.executor;\n        const query = compiledQuery.query;\n        if ((query.returning && adapter.supportsReturning) ||\n            (query.output && adapter.supportsOutput)) {\n            return result.rows;\n        }\n        return [\n            new InsertResult(result.insertId, result.numAffectedRows ?? BigInt(0)),\n        ];\n    }\n    /**\n     * Executes the query and returns the first result or undefined if\n     * the query returned no result.\n     */\n    async executeTakeFirst() {\n        const [result] = await this.execute();\n        return result;\n    }\n    /**\n     * Executes the query and returns the first result or throws if\n     * the query returned no result.\n     *\n     * By default an instance of {@link NoResultError} is thrown, but you can\n     * provide a custom error class, or callback as the only argument to throw a different\n     * error.\n     */\n    async executeTakeFirstOrThrow(errorConstructor = NoResultError) {\n        const result = await this.executeTakeFirst();\n        if (result === undefined) {\n            const error = isNoResultErrorConstructor(errorConstructor)\n                ? new errorConstructor(this.toOperationNode())\n                : errorConstructor(this.toOperationNode());\n            throw error;\n        }\n        return result;\n    }\n    async *stream(chunkSize = 100) {\n        const compiledQuery = this.compile();\n        const stream = this.#props.executor.stream(compiledQuery, chunkSize, this.#props.queryId);\n        for await (const item of stream) {\n            yield* item.rows;\n        }\n    }\n    async explain(format, options) {\n        const builder = new InsertQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithExplain(this.#props.queryNode, format, options),\n        });\n        return await builder.execute();\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgLC,GACD,OAAO,MAAM,EAAE;QACX,MAAM,CAAC,SAAS,OAAO,GAAG,CAAA,GAAA,mOAAA,CAAA,wBAAqB,AAAD,EAAE;QAChD,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD;gBACA;YACJ;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,QAAQ,OAAO,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,SAAS,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM;YACjD;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BC,GACD,WAAW,UAAU,EAAE;QACnB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,QAAQ,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;YAC5B;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,gBAAgB;QACZ,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,eAAe;YACnB;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;KAwBC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,SAAS,eAAe;QAC7F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuCC,GACD,SAAS;QACL,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkCC,GACD,WAAW;QACP,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,UAAU;QACN,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,SAAS;QACL,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,YAAY;QACR,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,aAAa;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8CC,GACD,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4KC,GACD,WAAW,QAAQ,EAAE;QACjB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,YAAY,SAAS,IAAI,4OAAA,CAAA,oBAAiB,CAAC;oBACvC,gBAAgB,0OAAA,CAAA,iBAAc,CAAC,MAAM;gBACzC,IAAI,eAAe;YACvB;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BC,GACD,qBAAqB,MAAM,EAAE;QACzB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE;gBACxD,gBAAgB,kPAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA,GAAA,gOAAA,CAAA,8BAA2B,AAAD,EAAE;YAC1E;QACJ;IACJ;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,eAAe;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD;QAChF;IACJ;IACA,OAAO,IAAI,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA;;;;;;;;;;;;;;;;;;KAkBC,GACD,iBAAiB;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACpE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;KAwBC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoCC,GACD,IAAI,SAAS,EAAE,IAAI,EAAE;QACjB,IAAI,WAAW;YACX,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;QAClB;IACJ;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyDC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+CC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACzF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA;;;;KAIC,GACD,MAAM,UAAU;QACZ,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACzF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACxC,MAAM,QAAQ,cAAc,KAAK;QACjC,IAAI,AAAC,MAAM,SAAS,IAAI,QAAQ,iBAAiB,IAC5C,MAAM,MAAM,IAAI,QAAQ,cAAc,EAAG;YAC1C,OAAO,OAAO,IAAI;QACtB;QACA,OAAO;YACH,IAAI,mOAAA,CAAA,eAAY,CAAC,OAAO,QAAQ,EAAE,OAAO,eAAe,IAAI,OAAO;SACtE;IACL;IACA;;;KAGC,GACD,MAAM,mBAAmB;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;QACnC,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,wBAAwB,mBAAmB,wOAAA,CAAA,gBAAa,EAAE;QAC5D,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;QAC1C,IAAI,WAAW,WAAW;YACtB,MAAM,QAAQ,CAAA,GAAA,wOAAA,CAAA,6BAA0B,AAAD,EAAE,oBACnC,IAAI,iBAAiB,IAAI,CAAC,eAAe,MACzC,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM;QACV;QACA,OAAO;IACX;IACA,OAAO,OAAO,YAAY,GAAG,EAAE;QAC3B,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACxF,WAAW,MAAM,QAAQ,OAAQ;YAC7B,OAAO,KAAK,IAAI;QACpB;IACJ;IACA,MAAM,QAAQ,MAAM,EAAE,OAAO,EAAE;QAC3B,MAAM,UAAU,IAAI,mBAAmB;YACnC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,QAAQ;QACzE;QACA,OAAO,MAAM,QAAQ,OAAO;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/delete-result.js"], "sourcesContent": ["/// <reference types=\"./delete-result.d.ts\" />\nexport class DeleteResult {\n    numDeletedRows;\n    constructor(numDeletedRows) {\n        this.numDeletedRows = numDeletedRows;\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AACvC,MAAM;IACT,eAAe;IACf,YAAY,cAAc,CAAE;QACxB,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/delete-query-builder.js"], "sourcesContent": ["/// <reference types=\"./delete-query-builder.d.ts\" />\nimport { parseJoin, } from '../parser/join-parser.js';\nimport { parseTableExpressionOrList, } from '../parser/table-parser.js';\nimport { parseSelectArg, parseSelectAll, } from '../parser/select-parser.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { freeze } from '../util/object-utils.js';\nimport { isNoResultErrorConstructor, NoResultError, } from './no-result-error.js';\nimport { DeleteResult } from './delete-result.js';\nimport { DeleteQueryNode } from '../operation-node/delete-query-node.js';\nimport { LimitNode } from '../operation-node/limit-node.js';\nimport { parseOrderBy, } from '../parser/order-by-parser.js';\nimport { parseValueBinaryOperationOrExpression, parseReferentialBinaryOperation, } from '../parser/binary-operation-parser.js';\nimport { parseValueExpression, } from '../parser/value-parser.js';\nimport { parseTop } from '../parser/top-parser.js';\nexport class DeleteQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    where(...args) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    whereRef(lhs, op, rhs) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    clearWhere() {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutWhere(this.#props.queryNode),\n        });\n    }\n    /**\n     * Changes a `delete from` query into a `delete top from` query.\n     *\n     * `top` clause is only supported by some dialects like MS SQL Server.\n     *\n     * ### Examples\n     *\n     * Delete the first 5 rows:\n     *\n     * ```ts\n     * await db\n     *   .deleteFrom('person')\n     *   .top(5)\n     *   .where('age', '>', 18)\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * delete top(5) from \"person\" where \"age\" > @1\n     * ```\n     *\n     * Delete the first 50% of rows:\n     *\n     * ```ts\n     * await db\n     *   .deleteFrom('person')\n     *   .top(50, 'percent')\n     *   .where('age', '>', 18)\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * delete top(50) percent from \"person\" where \"age\" > @1\n     * ```\n     */\n    top(expression, modifiers) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    using(tables) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: DeleteQueryNode.cloneWithUsing(this.#props.queryNode, parseTableExpressionOrList(tables)),\n        });\n    }\n    innerJoin(...args) {\n        return this.#join('InnerJoin', args);\n    }\n    leftJoin(...args) {\n        return this.#join('LeftJoin', args);\n    }\n    rightJoin(...args) {\n        return this.#join('RightJoin', args);\n    }\n    fullJoin(...args) {\n        return this.#join('FullJoin', args);\n    }\n    #join(joinType, args) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithJoin(this.#props.queryNode, parseJoin(joinType, args)),\n        });\n    }\n    returning(selection) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectArg(selection)),\n        });\n    }\n    returningAll(table) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    output(args) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    outputAll(table) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    /**\n     * Clears all `returning` clauses from the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.deleteFrom('pet')\n     *   .returningAll()\n     *   .where('name', '=', 'Max')\n     *   .clearReturning()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL(PostgreSQL):\n     *\n     * ```sql\n     * delete from \"pet\" where \"name\" = \"Max\"\n     * ```\n     */\n    clearReturning() {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutReturning(this.#props.queryNode),\n        });\n    }\n    /**\n     * Clears the `limit` clause from the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.deleteFrom('pet')\n     *   .returningAll()\n     *   .where('name', '=', 'Max')\n     *   .limit(5)\n     *   .clearLimit()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL(PostgreSQL):\n     *\n     * ```sql\n     * delete from \"pet\" where \"name\" = \"Max\" returning *\n     * ```\n     */\n    clearLimit() {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: DeleteQueryNode.cloneWithoutLimit(this.#props.queryNode),\n        });\n    }\n    orderBy(...args) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOrderByItems(this.#props.queryNode, parseOrderBy(args)),\n        });\n    }\n    clearOrderBy() {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutOrderBy(this.#props.queryNode),\n        });\n    }\n    /**\n     * Adds a limit clause to the query.\n     *\n     * A limit clause in a delete query is only supported by some dialects\n     * like MySQL.\n     *\n     * ### Examples\n     *\n     * Delete 5 oldest items in a table:\n     *\n     * ```ts\n     * await db\n     *   .deleteFrom('pet')\n     *   .orderBy('created_at')\n     *   .limit(5)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * delete from `pet` order by `created_at` limit ?\n     * ```\n     */\n    limit(limit) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: DeleteQueryNode.cloneWithLimit(this.#props.queryNode, LimitNode.create(parseValueExpression(limit))),\n        });\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.deleteFrom('person')\n     *   .where('first_name', '=', 'John')\n     *   .modifyEnd(sql`-- This is a comment`)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * delete from `person`\n     * where `first_name` = \"John\" -- This is a comment\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     *\n     * If you want to conditionally call a method on `this`, see\n     * the {@link $if} method.\n     *\n     * ### Examples\n     *\n     * The next example uses a helper function `log` to log a query:\n     *\n     * ```ts\n     * import type { Compilable } from 'kysely'\n     *\n     * function log<T extends Compilable>(qb: T): T {\n     *   console.log(qb.compile())\n     *   return qb\n     * }\n     *\n     * await db.deleteFrom('person')\n     *   .$call(log)\n     *   .execute()\n     * ```\n     */\n    $call(func) {\n        return func(this);\n    }\n    /**\n     * Call `func(this)` if `condition` is true.\n     *\n     * This method is especially handy with optional selects. Any `returning` or `returningAll`\n     * method calls add columns as optional fields to the output type when called inside\n     * the `func` callback. This is because we can't know if those selections were actually\n     * made before running the code.\n     *\n     * You can also call any other methods inside the callback.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * async function deletePerson(id: number, returnLastName: boolean) {\n     *   return await db\n     *     .deleteFrom('person')\n     *     .where('id', '=', id)\n     *     .returning(['id', 'first_name'])\n     *     .$if(returnLastName, (qb) => qb.returning('last_name'))\n     *     .executeTakeFirstOrThrow()\n     * }\n     * ```\n     *\n     * Any selections added inside the `if` callback will be added as optional fields to the\n     * output type since we can't know if the selections were actually made before running\n     * the code. In the example above the return type of the `deletePerson` function is:\n     *\n     * ```ts\n     * Promise<{\n     *   id: number\n     *   first_name: string\n     *   last_name?: string\n     * }>\n     * ```\n     */\n    $if(condition, func) {\n        if (condition) {\n            return func(this);\n        }\n        return new DeleteQueryBuilder({\n            ...this.#props,\n        });\n    }\n    /**\n     * Change the output type of the query.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `DeleteQueryBuilder` with a new output type.\n     */\n    $castTo() {\n        return new DeleteQueryBuilder(this.#props);\n    }\n    /**\n     * Narrows (parts of) the output type of the query.\n     *\n     * Kysely tries to be as type-safe as possible, but in some cases we have to make\n     * compromises for better maintainability and compilation performance. At present,\n     * Kysely doesn't narrow the output type of the query when using {@link where} and {@link returning} or {@link returningAll}.\n     *\n     * This utility method is very useful for these situations, as it removes unncessary\n     * runtime assertion/guard code. Its input type is limited to the output type\n     * of the query, so you can't add a column that doesn't exist, or change a column's\n     * type to something that doesn't exist in its union type.\n     *\n     * ### Examples\n     *\n     * Turn this code:\n     *\n     * ```ts\n     * import type { Person } from 'type-editor' // imaginary module\n     *\n     * const person = await db.deleteFrom('person')\n     *   .where('id', '=', 3)\n     *   .where('nullable_column', 'is not', null)\n     *   .returningAll()\n     *   .executeTakeFirstOrThrow()\n     *\n     * if (isWithNoNullValue(person)) {\n     *   functionThatExpectsPersonWithNonNullValue(person)\n     * }\n     *\n     * function isWithNoNullValue(person: Person): person is Person & { nullable_column: string } {\n     *   return person.nullable_column != null\n     * }\n     * ```\n     *\n     * Into this:\n     *\n     * ```ts\n     * import type { NotNull } from 'kysely'\n     *\n     * const person = await db.deleteFrom('person')\n     *   .where('id', '=', 3)\n     *   .where('nullable_column', 'is not', null)\n     *   .returningAll()\n     *   .$narrowType<{ nullable_column: NotNull }>()\n     *   .executeTakeFirstOrThrow()\n     *\n     * functionThatExpectsPersonWithNonNullValue(person)\n     * ```\n     */\n    $narrowType() {\n        return new DeleteQueryBuilder(this.#props);\n    }\n    /**\n     * Asserts that query's output row type equals the given type `T`.\n     *\n     * This method can be used to simplify excessively complex types to make TypeScript happy\n     * and much faster.\n     *\n     * Kysely uses complex type magic to achieve its type safety. This complexity is sometimes too much\n     * for TypeScript and you get errors like this:\n     *\n     * ```\n     * error TS2589: Type instantiation is excessively deep and possibly infinite.\n     * ```\n     *\n     * In these case you can often use this method to help TypeScript a little bit. When you use this\n     * method to assert the output type of a query, Kysely can drop the complex output type that\n     * consists of multiple nested helper types and replace it with the simple asserted type.\n     *\n     * Using this method doesn't reduce type safety at all. You have to pass in a type that is\n     * structurally equal to the current type.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { Species } from 'type-editor' // imaginary module\n     *\n     * async function deletePersonAndPets(personId: number) {\n     *   return await db\n     *     .with('deleted_person', (qb) => qb\n     *        .deleteFrom('person')\n     *        .where('id', '=', personId)\n     *        .returning('first_name')\n     *        .$assertType<{ first_name: string }>()\n     *     )\n     *     .with('deleted_pets', (qb) => qb\n     *       .deleteFrom('pet')\n     *       .where('owner_id', '=', personId)\n     *       .returning(['name as pet_name', 'species'])\n     *       .$assertType<{ pet_name: string, species: Species }>()\n     *     )\n     *     .selectFrom(['deleted_person', 'deleted_pets'])\n     *     .selectAll()\n     *     .execute()\n     * }\n     * ```\n     */\n    $assertType() {\n        return new DeleteQueryBuilder(this.#props);\n    }\n    /**\n     * Returns a copy of this DeleteQueryBuilder instance with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new DeleteQueryBuilder({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.queryNode, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    /**\n     * Executes the query and returns an array of rows.\n     *\n     * Also see the {@link executeTakeFirst} and {@link executeTakeFirstOrThrow} methods.\n     */\n    async execute() {\n        const compiledQuery = this.compile();\n        const result = await this.#props.executor.executeQuery(compiledQuery, this.#props.queryId);\n        const { adapter } = this.#props.executor;\n        const query = compiledQuery.query;\n        if ((query.returning && adapter.supportsReturning) ||\n            (query.output && adapter.supportsOutput)) {\n            return result.rows;\n        }\n        return [new DeleteResult(result.numAffectedRows ?? BigInt(0))];\n    }\n    /**\n     * Executes the query and returns the first result or undefined if\n     * the query returned no result.\n     */\n    async executeTakeFirst() {\n        const [result] = await this.execute();\n        return result;\n    }\n    /**\n     * Executes the query and returns the first result or throws if\n     * the query returned no result.\n     *\n     * By default an instance of {@link NoResultError} is thrown, but you can\n     * provide a custom error class, or callback as the only argument to throw a different\n     * error.\n     */\n    async executeTakeFirstOrThrow(errorConstructor = NoResultError) {\n        const result = await this.executeTakeFirst();\n        if (result === undefined) {\n            const error = isNoResultErrorConstructor(errorConstructor)\n                ? new errorConstructor(this.toOperationNode())\n                : errorConstructor(this.toOperationNode());\n            throw error;\n        }\n        return result;\n    }\n    async *stream(chunkSize = 100) {\n        const compiledQuery = this.compile();\n        const stream = this.#props.executor.stream(compiledQuery, chunkSize, this.#props.queryId);\n        for await (const item of stream) {\n            yield* item.rows;\n        }\n    }\n    async explain(format, options) {\n        const builder = new DeleteQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithExplain(this.#props.queryNode, format, options),\n        });\n        return await builder.execute();\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACrG;IACJ;IACA,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QACxG;IACJ;IACA,aAAa;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAChE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsCC,GACD,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA,MAAM,MAAM,EAAE;QACV,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE;QAChG;IACJ;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,CAAA,IAAK,CAAC,QAAQ,EAAE,IAAI;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,uNAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAClF;IACJ;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,OAAO,IAAI,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA;;;;;;;;;;;;;;;;;;KAkBC,GACD,iBAAiB;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACpE;IACJ;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,aAAa;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACtE;IACJ;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QACnF;IACJ;IACA,eAAe;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAClE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3G;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,SAAS,eAAe;QAC7F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkCC,GACD,IAAI,SAAS,EAAE,IAAI,EAAE;QACjB,IAAI,WAAW;YACX,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;QAClB;IACJ;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgDC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACzF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA;;;;KAIC,GACD,MAAM,UAAU;QACZ,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACzF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACxC,MAAM,QAAQ,cAAc,KAAK;QACjC,IAAI,AAAC,MAAM,SAAS,IAAI,QAAQ,iBAAiB,IAC5C,MAAM,MAAM,IAAI,QAAQ,cAAc,EAAG;YAC1C,OAAO,OAAO,IAAI;QACtB;QACA,OAAO;YAAC,IAAI,mOAAA,CAAA,eAAY,CAAC,OAAO,eAAe,IAAI,OAAO;SAAI;IAClE;IACA;;;KAGC,GACD,MAAM,mBAAmB;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;QACnC,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,wBAAwB,mBAAmB,wOAAA,CAAA,gBAAa,EAAE;QAC5D,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;QAC1C,IAAI,WAAW,WAAW;YACtB,MAAM,QAAQ,CAAA,GAAA,wOAAA,CAAA,6BAA0B,AAAD,EAAE,oBACnC,IAAI,iBAAiB,IAAI,CAAC,eAAe,MACzC,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM;QACV;QACA,OAAO;IACX;IACA,OAAO,OAAO,YAAY,GAAG,EAAE;QAC3B,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACxF,WAAW,MAAM,QAAQ,OAAQ;YAC7B,OAAO,KAAK,IAAI;QACpB;IACJ;IACA,MAAM,QAAQ,MAAM,EAAE,OAAO,EAAE;QAC3B,MAAM,UAAU,IAAI,mBAAmB;YACnC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,QAAQ;QACzE;QACA,OAAO,MAAM,QAAQ,OAAO;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2222, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/update-result.js"], "sourcesContent": ["/// <reference types=\"./update-result.d.ts\" />\nexport class UpdateResult {\n    /**\n     * The number of rows the update query updated (even if not changed).\n     */\n    numUpdatedRows;\n    /**\n     * The number of rows the update query changed.\n     *\n     * This is **optional** and only supported in dialects such as MySQL.\n     * You would probably use {@link numUpdatedRows} in most cases.\n     */\n    numChangedRows;\n    constructor(numUpdatedRows, numChangedRows) {\n        this.numUpdatedRows = numUpdatedRows;\n        this.numChangedRows = numChangedRows;\n    }\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AACvC,MAAM;IACT;;KAEC,GACD,eAAe;IACf;;;;;KAKC,GACD,eAAe;IACf,YAAY,cAAc,EAAE,cAAc,CAAE;QACxC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2247, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/update-query-builder.js"], "sourcesContent": ["/// <reference types=\"./update-query-builder.d.ts\" />\nimport { parseJoin, } from '../parser/join-parser.js';\nimport { parseTableExpressionOrList, } from '../parser/table-parser.js';\nimport { parseSelectArg, parseSelectAll, } from '../parser/select-parser.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { UpdateQueryNode } from '../operation-node/update-query-node.js';\nimport { parseUpdate, } from '../parser/update-set-parser.js';\nimport { freeze } from '../util/object-utils.js';\nimport { UpdateResult } from './update-result.js';\nimport { isNoResultErrorConstructor, NoResultError, } from './no-result-error.js';\nimport { parseReferentialBinaryOperation, parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nimport { parseValueExpression, } from '../parser/value-parser.js';\nimport { LimitNode } from '../operation-node/limit-node.js';\nimport { parseTop } from '../parser/top-parser.js';\nimport { parseOrderBy, } from '../parser/order-by-parser.js';\nexport class UpdateQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    where(...args) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    whereRef(lhs, op, rhs) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    clearWhere() {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutWhere(this.#props.queryNode),\n        });\n    }\n    /**\n     * Changes an `update` query into a `update top` query.\n     *\n     * `top` clause is only supported by some dialects like MS SQL Server.\n     *\n     * ### Examples\n     *\n     * Update the first row:\n     *\n     * ```ts\n     * await db.updateTable('person')\n     *   .top(1)\n     *   .set({ first_name: 'Foo' })\n     *   .where('age', '>', 18)\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * update top(1) \"person\" set \"first_name\" = @1 where \"age\" > @2\n     * ```\n     *\n     * Update the 50% first rows:\n     *\n     * ```ts\n     * await db.updateTable('person')\n     *   .top(50, 'percent')\n     *   .set({ first_name: 'Foo' })\n     *   .where('age', '>', 18)\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * update top(50) percent \"person\" set \"first_name\" = @1 where \"age\" > @2\n     * ```\n     */\n    top(expression, modifiers) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    from(from) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: UpdateQueryNode.cloneWithFromItems(this.#props.queryNode, parseTableExpressionOrList(from)),\n        });\n    }\n    innerJoin(...args) {\n        return this.#join('InnerJoin', args);\n    }\n    leftJoin(...args) {\n        return this.#join('LeftJoin', args);\n    }\n    rightJoin(...args) {\n        return this.#join('RightJoin', args);\n    }\n    fullJoin(...args) {\n        return this.#join('FullJoin', args);\n    }\n    #join(joinType, args) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithJoin(this.#props.queryNode, parseJoin(joinType, args)),\n        });\n    }\n    orderBy(...args) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOrderByItems(this.#props.queryNode, parseOrderBy(args)),\n        });\n    }\n    clearOrderBy() {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutOrderBy(this.#props.queryNode),\n        });\n    }\n    /**\n     * Adds a limit clause to the update query for supported databases, such as MySQL.\n     *\n     * ### Examples\n     *\n     * Update the first 2 rows in the 'person' table:\n     *\n     * ```ts\n     * await db\n     *   .updateTable('person')\n     *   .set({ first_name: 'Foo' })\n     *   .limit(2)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * update `person` set `first_name` = ? limit ?\n     * ```\n     */\n    limit(limit) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: UpdateQueryNode.cloneWithLimit(this.#props.queryNode, LimitNode.create(parseValueExpression(limit))),\n        });\n    }\n    set(...args) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: UpdateQueryNode.cloneWithUpdates(this.#props.queryNode, parseUpdate(...args)),\n        });\n    }\n    returning(selection) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectArg(selection)),\n        });\n    }\n    returningAll(table) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    output(args) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    outputAll(table) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.updateTable('person')\n     *   .set({ age: 39 })\n     *   .where('first_name', '=', 'John')\n     *   .modifyEnd(sql.raw('-- This is a comment'))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * update `person`\n     * set `age` = 39\n     * where `first_name` = \"John\" -- This is a comment\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * Clears all `returning` clauses from the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.updateTable('person')\n     *   .returningAll()\n     *   .set({ age: 39 })\n     *   .where('first_name', '=', 'John')\n     *   .clearReturning()\n     * ```\n     *\n     * The generated SQL(PostgreSQL):\n     *\n     * ```sql\n     * update \"person\" set \"age\" = 39 where \"first_name\" = \"John\"\n     * ```\n     */\n    clearReturning() {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutReturning(this.#props.queryNode),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     *\n     * If you want to conditionally call a method on `this`, see\n     * the {@link $if} method.\n     *\n     * ### Examples\n     *\n     * The next example uses a helper function `log` to log a query:\n     *\n     * ```ts\n     * import type { Compilable } from 'kysely'\n     * import type { PersonUpdate } from 'type-editor' // imaginary module\n     *\n     * function log<T extends Compilable>(qb: T): T {\n     *   console.log(qb.compile())\n     *   return qb\n     * }\n     *\n     * const values = {\n     *   first_name: 'John',\n     * } satisfies PersonUpdate\n     *\n     * db.updateTable('person')\n     *   .set(values)\n     *   .$call(log)\n     *   .execute()\n     * ```\n     */\n    $call(func) {\n        return func(this);\n    }\n    /**\n     * Call `func(this)` if `condition` is true.\n     *\n     * This method is especially handy with optional selects. Any `returning` or `returningAll`\n     * method calls add columns as optional fields to the output type when called inside\n     * the `func` callback. This is because we can't know if those selections were actually\n     * made before running the code.\n     *\n     * You can also call any other methods inside the callback.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { PersonUpdate } from 'type-editor' // imaginary module\n     *\n     * async function updatePerson(id: number, updates: PersonUpdate, returnLastName: boolean) {\n     *   return await db\n     *     .updateTable('person')\n     *     .set(updates)\n     *     .where('id', '=', id)\n     *     .returning(['id', 'first_name'])\n     *     .$if(returnLastName, (qb) => qb.returning('last_name'))\n     *     .executeTakeFirstOrThrow()\n     * }\n     * ```\n     *\n     * Any selections added inside the `if` callback will be added as optional fields to the\n     * output type since we can't know if the selections were actually made before running\n     * the code. In the example above the return type of the `updatePerson` function is:\n     *\n     * ```ts\n     * Promise<{\n     *   id: number\n     *   first_name: string\n     *   last_name?: string\n     * }>\n     * ```\n     */\n    $if(condition, func) {\n        if (condition) {\n            return func(this);\n        }\n        return new UpdateQueryBuilder({\n            ...this.#props,\n        });\n    }\n    /**\n     * Change the output type of the query.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `UpdateQueryBuilder` with a new output type.\n     */\n    $castTo() {\n        return new UpdateQueryBuilder(this.#props);\n    }\n    /**\n     * Narrows (parts of) the output type of the query.\n     *\n     * Kysely tries to be as type-safe as possible, but in some cases we have to make\n     * compromises for better maintainability and compilation performance. At present,\n     * Kysely doesn't narrow the output type of the query based on {@link set} input\n     * when using {@link where} and/or {@link returning} or {@link returningAll}.\n     *\n     * This utility method is very useful for these situations, as it removes unncessary\n     * runtime assertion/guard code. Its input type is limited to the output type\n     * of the query, so you can't add a column that doesn't exist, or change a column's\n     * type to something that doesn't exist in its union type.\n     *\n     * ### Examples\n     *\n     * Turn this code:\n     *\n     * ```ts\n     * import type { Person } from 'type-editor' // imaginary module\n     *\n     * const id = 1\n     * const now = new Date().toISOString()\n     *\n     * const person = await db.updateTable('person')\n     *   .set({ deleted_at: now })\n     *   .where('id', '=', id)\n     *   .where('nullable_column', 'is not', null)\n     *   .returningAll()\n     *   .executeTakeFirstOrThrow()\n     *\n     * if (isWithNoNullValue(person)) {\n     *   functionThatExpectsPersonWithNonNullValue(person)\n     * }\n     *\n     * function isWithNoNullValue(person: Person): person is Person & { nullable_column: string } {\n     *   return person.nullable_column != null\n     * }\n     * ```\n     *\n     * Into this:\n     *\n     * ```ts\n     * import type { NotNull } from 'kysely'\n     *\n     * const id = 1\n     * const now = new Date().toISOString()\n     *\n     * const person = await db.updateTable('person')\n     *   .set({ deleted_at: now })\n     *   .where('id', '=', id)\n     *   .where('nullable_column', 'is not', null)\n     *   .returningAll()\n     *   .$narrowType<{ deleted_at: Date; nullable_column: NotNull }>()\n     *   .executeTakeFirstOrThrow()\n     *\n     * functionThatExpectsPersonWithNonNullValue(person)\n     * ```\n     */\n    $narrowType() {\n        return new UpdateQueryBuilder(this.#props);\n    }\n    /**\n     * Asserts that query's output row type equals the given type `T`.\n     *\n     * This method can be used to simplify excessively complex types to make TypeScript happy\n     * and much faster.\n     *\n     * Kysely uses complex type magic to achieve its type safety. This complexity is sometimes too much\n     * for TypeScript and you get errors like this:\n     *\n     * ```\n     * error TS2589: Type instantiation is excessively deep and possibly infinite.\n     * ```\n     *\n     * In these case you can often use this method to help TypeScript a little bit. When you use this\n     * method to assert the output type of a query, Kysely can drop the complex output type that\n     * consists of multiple nested helper types and replace it with the simple asserted type.\n     *\n     * Using this method doesn't reduce type safety at all. You have to pass in a type that is\n     * structurally equal to the current type.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { PersonUpdate, PetUpdate, Species } from 'type-editor' // imaginary module\n     *\n     * const person = {\n     *   id: 1,\n     *   gender: 'other',\n     * } satisfies PersonUpdate\n     *\n     * const pet = {\n     *   name: 'Fluffy',\n     * } satisfies PetUpdate\n     *\n     * const result = await db\n     *   .with('updated_person', (qb) => qb\n     *     .updateTable('person')\n     *     .set(person)\n     *     .where('id', '=', person.id)\n     *     .returning('first_name')\n     *     .$assertType<{ first_name: string }>()\n     *   )\n     *   .with('updated_pet', (qb) => qb\n     *     .updateTable('pet')\n     *     .set(pet)\n     *     .where('owner_id', '=', person.id)\n     *     .returning(['name as pet_name', 'species'])\n     *     .$assertType<{ pet_name: string, species: Species }>()\n     *   )\n     *   .selectFrom(['updated_person', 'updated_pet'])\n     *   .selectAll()\n     *   .executeTakeFirstOrThrow()\n     * ```\n     */\n    $assertType() {\n        return new UpdateQueryBuilder(this.#props);\n    }\n    /**\n     * Returns a copy of this UpdateQueryBuilder instance with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new UpdateQueryBuilder({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.queryNode, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    /**\n     * Executes the query and returns an array of rows.\n     *\n     * Also see the {@link executeTakeFirst} and {@link executeTakeFirstOrThrow} methods.\n     */\n    async execute() {\n        const compiledQuery = this.compile();\n        const result = await this.#props.executor.executeQuery(compiledQuery, this.#props.queryId);\n        const { adapter } = this.#props.executor;\n        const query = compiledQuery.query;\n        if ((query.returning && adapter.supportsReturning) ||\n            (query.output && adapter.supportsOutput)) {\n            return result.rows;\n        }\n        return [\n            new UpdateResult(result.numAffectedRows ?? BigInt(0), result.numChangedRows),\n        ];\n    }\n    /**\n     * Executes the query and returns the first result or undefined if\n     * the query returned no result.\n     */\n    async executeTakeFirst() {\n        const [result] = await this.execute();\n        return result;\n    }\n    /**\n     * Executes the query and returns the first result or throws if\n     * the query returned no result.\n     *\n     * By default an instance of {@link NoResultError} is thrown, but you can\n     * provide a custom error class, or callback as the only argument to throw a different\n     * error.\n     */\n    async executeTakeFirstOrThrow(errorConstructor = NoResultError) {\n        const result = await this.executeTakeFirst();\n        if (result === undefined) {\n            const error = isNoResultErrorConstructor(errorConstructor)\n                ? new errorConstructor(this.toOperationNode())\n                : errorConstructor(this.toOperationNode());\n            throw error;\n        }\n        return result;\n    }\n    async *stream(chunkSize = 100) {\n        const compiledQuery = this.compile();\n        const stream = this.#props.executor.stream(compiledQuery, chunkSize, this.#props.queryId);\n        for await (const item of stream) {\n            yield* item.rows;\n        }\n    }\n    async explain(format, options) {\n        const builder = new UpdateQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithExplain(this.#props.queryNode, format, options),\n        });\n        return await builder.execute();\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACrG;IACJ;IACA,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QACxG;IACJ;IACA,aAAa;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAChE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsCC,GACD,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA,KAAK,IAAI,EAAE;QACP,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,6BAA0B,AAAD,EAAE;QACpG;IACJ;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,CAAA,IAAK,CAAC,QAAQ,EAAE,IAAI;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,uNAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAClF;IACJ;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QACnF;IACJ;IACA,eAAe;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAClE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3G;IACJ;IACA,IAAI,GAAG,IAAI,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,gOAAA,CAAA,cAAW,AAAD,KAAK;QACtF;IACJ;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,OAAO,IAAI,EAAE;QACT,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,SAAS,eAAe;QAC7F;IACJ;IACA;;;;;;;;;;;;;;;;;;KAkBC,GACD,iBAAiB;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACpE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqCC,GACD,IAAI,SAAS,EAAE,IAAI,EAAE;QACjB,IAAI,WAAW;YACX,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;QAClB;IACJ;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyDC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqDC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB,IAAI,CAAC,CAAA,KAAM;IAC7C;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACzF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA;;;;KAIC,GACD,MAAM,UAAU;QACZ,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACzF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACxC,MAAM,QAAQ,cAAc,KAAK;QACjC,IAAI,AAAC,MAAM,SAAS,IAAI,QAAQ,iBAAiB,IAC5C,MAAM,MAAM,IAAI,QAAQ,cAAc,EAAG;YAC1C,OAAO,OAAO,IAAI;QACtB;QACA,OAAO;YACH,IAAI,mOAAA,CAAA,eAAY,CAAC,OAAO,eAAe,IAAI,OAAO,IAAI,OAAO,cAAc;SAC9E;IACL;IACA;;;KAGC,GACD,MAAM,mBAAmB;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;QACnC,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,wBAAwB,mBAAmB,wOAAA,CAAA,gBAAa,EAAE;QAC5D,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;QAC1C,IAAI,WAAW,WAAW;YACtB,MAAM,QAAQ,CAAA,GAAA,wOAAA,CAAA,6BAA0B,AAAD,EAAE,oBACnC,IAAI,iBAAiB,IAAI,CAAC,eAAe,MACzC,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM;QACV;QACA,OAAO;IACX;IACA,OAAO,OAAO,YAAY,GAAG,EAAE;QAC3B,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACxF,WAAW,MAAM,QAAQ,OAAQ;YAC7B,OAAO,KAAK,IAAI;QACpB;IACJ;IACA,MAAM,QAAQ,MAAM,EAAE,OAAO,EAAE;QAC3B,MAAM,UAAU,IAAI,mBAAmB;YACnC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,QAAQ;QACzE;QACA,OAAO,MAAM,QAAQ,OAAO;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2764, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/cte-builder.js"], "sourcesContent": ["/// <reference types=\"./cte-builder.d.ts\" />\nimport { CommonTableExpressionNode } from '../operation-node/common-table-expression-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class CTEBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Makes the common table expression materialized.\n     */\n    materialized() {\n        return new CTEBuilder({\n            ...this.#props,\n            node: CommonTableExpressionNode.cloneWith(this.#props.node, {\n                materialized: true,\n            }),\n        });\n    }\n    /**\n     * Makes the common table expression not materialized.\n     */\n    notMaterialized() {\n        return new CTEBuilder({\n            ...this.#props,\n            node: CommonTableExpressionNode.cloneWith(this.#props.node, {\n                materialized: false,\n            }),\n        });\n    }\n    toOperationNode() {\n        return this.#props.node;\n    }\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAC5C;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;KAEC,GACD,eAAe;QACX,OAAO,IAAI,WAAW;YAClB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yPAAA,CAAA,4BAAyB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACxD,cAAc;YAClB;QACJ;IACJ;IACA;;KAEC,GACD,kBAAkB;QACd,OAAO,IAAI,WAAW;YAClB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yPAAA,CAAA,4BAAyB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACxD,cAAc;YAClB;QACJ;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI;IAC3B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/merge-result.js"], "sourcesContent": ["/// <reference types=\"./merge-result.d.ts\" />\nexport class MergeResult {\n    numChangedRows;\n    constructor(numChangedRows) {\n        this.numChangedRows = numChangedRows;\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AACtC,MAAM;IACT,eAAe;IACf,YAAY,cAAc,CAAE;QACxB,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2823, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/merge-query-builder.js"], "sourcesContent": ["/// <reference types=\"./merge-query-builder.d.ts\" />\nimport { InsertQueryNode } from '../operation-node/insert-query-node.js';\nimport { MergeQueryNode } from '../operation-node/merge-query-node.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { UpdateQueryNode } from '../operation-node/update-query-node.js';\nimport { parseInsertExpression, } from '../parser/insert-values-parser.js';\nimport { parseJoin, } from '../parser/join-parser.js';\nimport { parseMergeThen, parseMergeWhen } from '../parser/merge-parser.js';\nimport { parseSelectAll, parseSelectArg, } from '../parser/select-parser.js';\nimport { parseTop } from '../parser/top-parser.js';\nimport { NOOP_QUERY_EXECUTOR } from '../query-executor/noop-query-executor.js';\nimport { freeze } from '../util/object-utils.js';\nimport { MergeResult } from './merge-result.js';\nimport { NoResultError, isNoResultErrorConstructor, } from './no-result-error.js';\nimport { UpdateQueryBuilder } from './update-query-builder.js';\nexport class MergeQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db\n     *   .mergeInto('person')\n     *   .using('pet', 'pet.owner_id', 'person.id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .modifyEnd(sql.raw('-- this is a comment'))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\" using \"pet\" on \"pet\".\"owner_id\" = \"person\".\"id\" when matched then delete -- this is a comment\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * Changes a `merge into` query to an `merge top into` query.\n     *\n     * `top` clause is only supported by some dialects like MS SQL Server.\n     *\n     * ### Examples\n     *\n     * Affect 5 matched rows at most:\n     *\n     * ```ts\n     * await db.mergeInto('person')\n     *   .top(5)\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * merge top(5) into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   delete\n     * ```\n     *\n     * Affect 50% of matched rows:\n     *\n     * ```ts\n     * await db.mergeInto('person')\n     *   .top(50, 'percent')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MS SQL Server):\n     *\n     * ```sql\n     * merge top(50) percent into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   delete\n     * ```\n     */\n    top(expression, modifiers) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    using(...args) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithUsing(this.#props.queryNode, parseJoin('Using', args)),\n        });\n    }\n    returning(args) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    returningAll(table) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    output(args) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    outputAll(table) {\n        return new MergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n}\nexport class WheneableMergeQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db\n     *   .mergeInto('person')\n     *   .using('pet', 'pet.owner_id', 'person.id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .modifyEnd(sql.raw('-- this is a comment'))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\" using \"pet\" on \"pet\".\"owner_id\" = \"person\".\"id\" when matched then delete -- this is a comment\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * See {@link MergeQueryBuilder.top}.\n     */\n    top(expression, modifiers) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    /**\n     * Adds a simple `when matched` clause to the query.\n     *\n     * For a `when matched` clause with an `and` condition, see {@link whenMatchedAnd}.\n     *\n     * For a simple `when not matched` clause, see {@link whenNotMatched}.\n     *\n     * For a `when not matched` clause with an `and` condition, see {@link whenNotMatchedAnd}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   delete\n     * ```\n     */\n    whenMatched() {\n        return this.#whenMatched([]);\n    }\n    whenMatchedAnd(...args) {\n        return this.#whenMatched(args);\n    }\n    /**\n     * Adds the `when matched` clause to the query with an `and` condition. But unlike\n     * {@link whenMatchedAnd}, this method accepts a column reference as the 3rd argument.\n     *\n     * This method is similar to {@link SelectQueryBuilder.whereRef}, so see the documentation\n     * for that method for more examples.\n     */\n    whenMatchedAndRef(lhs, op, rhs) {\n        return this.#whenMatched([lhs, op, rhs], true);\n    }\n    #whenMatched(args, refRight) {\n        return new MatchedThenableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithWhen(this.#props.queryNode, parseMergeWhen({ isMatched: true }, args, refRight)),\n        });\n    }\n    /**\n     * Adds a simple `when not matched` clause to the query.\n     *\n     * For a `when not matched` clause with an `and` condition, see {@link whenNotMatchedAnd}.\n     *\n     * For a simple `when matched` clause, see {@link whenMatched}.\n     *\n     * For a `when matched` clause with an `and` condition, see {@link whenMatchedAnd}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenNotMatched()\n     *   .thenInsertValues({\n     *     first_name: 'John',\n     *     last_name: 'Doe',\n     *   })\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when not matched then\n     *   insert (\"first_name\", \"last_name\") values ($1, $2)\n     * ```\n     */\n    whenNotMatched() {\n        return this.#whenNotMatched([]);\n    }\n    whenNotMatchedAnd(...args) {\n        return this.#whenNotMatched(args);\n    }\n    /**\n     * Adds the `when not matched` clause to the query with an `and` condition. But unlike\n     * {@link whenNotMatchedAnd}, this method accepts a column reference as the 3rd argument.\n     *\n     * Unlike {@link whenMatchedAndRef}, you cannot reference columns from the target table.\n     *\n     * This method is similar to {@link SelectQueryBuilder.whereRef}, so see the documentation\n     * for that method for more examples.\n     */\n    whenNotMatchedAndRef(lhs, op, rhs) {\n        return this.#whenNotMatched([lhs, op, rhs], true);\n    }\n    /**\n     * Adds a simple `when not matched by source` clause to the query.\n     *\n     * Supported in MS SQL Server.\n     *\n     * Similar to {@link whenNotMatched}, but returns a {@link MatchedThenableMergeQueryBuilder}.\n     */\n    whenNotMatchedBySource() {\n        return this.#whenNotMatched([], false, true);\n    }\n    whenNotMatchedBySourceAnd(...args) {\n        return this.#whenNotMatched(args, false, true);\n    }\n    /**\n     * Adds the `when not matched by source` clause to the query with an `and` condition.\n     *\n     * Similar to {@link whenNotMatchedAndRef}, but you can reference columns from\n     * the target table, and not from source table and returns a {@link MatchedThenableMergeQueryBuilder}.\n     */\n    whenNotMatchedBySourceAndRef(lhs, op, rhs) {\n        return this.#whenNotMatched([lhs, op, rhs], true, true);\n    }\n    returning(args) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    returningAll(table) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithReturning(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    output(args) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectArg(args)),\n        });\n    }\n    outputAll(table) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOutput(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    #whenNotMatched(args, refRight = false, bySource = false) {\n        const props = {\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithWhen(this.#props.queryNode, parseMergeWhen({ isMatched: false, bySource }, args, refRight)),\n        };\n        const Builder = bySource\n            ? MatchedThenableMergeQueryBuilder\n            : NotMatchedThenableMergeQueryBuilder;\n        return new Builder(props);\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     *\n     * If you want to conditionally call a method on `this`, see\n     * the {@link $if} method.\n     *\n     * ### Examples\n     *\n     * The next example uses a helper function `log` to log a query:\n     *\n     * ```ts\n     * import type { Compilable } from 'kysely'\n     *\n     * function log<T extends Compilable>(qb: T): T {\n     *   console.log(qb.compile())\n     *   return qb\n     * }\n     *\n     * await db.updateTable('person')\n     *   .set({ first_name: 'John' })\n     *   .$call(log)\n     *   .execute()\n     * ```\n     */\n    $call(func) {\n        return func(this);\n    }\n    /**\n     * Call `func(this)` if `condition` is true.\n     *\n     * This method is especially handy with optional selects. Any `returning` or `returningAll`\n     * method calls add columns as optional fields to the output type when called inside\n     * the `func` callback. This is because we can't know if those selections were actually\n     * made before running the code.\n     *\n     * You can also call any other methods inside the callback.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import type { PersonUpdate } from 'type-editor' // imaginary module\n     *\n     * async function updatePerson(id: number, updates: PersonUpdate, returnLastName: boolean) {\n     *   return await db\n     *     .updateTable('person')\n     *     .set(updates)\n     *     .where('id', '=', id)\n     *     .returning(['id', 'first_name'])\n     *     .$if(returnLastName, (qb) => qb.returning('last_name'))\n     *     .executeTakeFirstOrThrow()\n     * }\n     * ```\n     *\n     * Any selections added inside the `if` callback will be added as optional fields to the\n     * output type since we can't know if the selections were actually made before running\n     * the code. In the example above the return type of the `updatePerson` function is:\n     *\n     * ```ts\n     * Promise<{\n     *   id: number\n     *   first_name: string\n     *   last_name?: string\n     * }>\n     * ```\n     */\n    $if(condition, func) {\n        if (condition) {\n            return func(this);\n        }\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.queryNode, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    /**\n     * Executes the query and returns an array of rows.\n     *\n     * Also see the {@link executeTakeFirst} and {@link executeTakeFirstOrThrow} methods.\n     */\n    async execute() {\n        const compiledQuery = this.compile();\n        const result = await this.#props.executor.executeQuery(compiledQuery, this.#props.queryId);\n        const { adapter } = this.#props.executor;\n        const query = compiledQuery.query;\n        if ((query.returning && adapter.supportsReturning) ||\n            (query.output && adapter.supportsOutput)) {\n            return result.rows;\n        }\n        return [new MergeResult(result.numAffectedRows)];\n    }\n    /**\n     * Executes the query and returns the first result or undefined if\n     * the query returned no result.\n     */\n    async executeTakeFirst() {\n        const [result] = await this.execute();\n        return result;\n    }\n    /**\n     * Executes the query and returns the first result or throws if\n     * the query returned no result.\n     *\n     * By default an instance of {@link NoResultError} is thrown, but you can\n     * provide a custom error class, or callback as the only argument to throw a different\n     * error.\n     */\n    async executeTakeFirstOrThrow(errorConstructor = NoResultError) {\n        const result = await this.executeTakeFirst();\n        if (result === undefined) {\n            const error = isNoResultErrorConstructor(errorConstructor)\n                ? new errorConstructor(this.toOperationNode())\n                : errorConstructor(this.toOperationNode());\n            throw error;\n        }\n        return result;\n    }\n}\nexport class MatchedThenableMergeQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Performs the `delete` action.\n     *\n     * To perform the `do nothing` action, see {@link thenDoNothing}.\n     *\n     * To perform the `update` action, see {@link thenUpdate} or {@link thenUpdateSet}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenDelete()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   delete\n     * ```\n     */\n    thenDelete() {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithThen(this.#props.queryNode, parseMergeThen('delete')),\n        });\n    }\n    /**\n     * Performs the `do nothing` action.\n     *\n     * This is supported in PostgreSQL.\n     *\n     * To perform the `delete` action, see {@link thenDelete}.\n     *\n     * To perform the `update` action, see {@link thenUpdate} or {@link thenUpdateSet}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenDoNothing()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   do nothing\n     * ```\n     */\n    thenDoNothing() {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithThen(this.#props.queryNode, parseMergeThen('do nothing')),\n        });\n    }\n    /**\n     * Perform an `update` operation with a full-fledged {@link UpdateQueryBuilder}.\n     * This is handy when multiple `set` invocations are needed.\n     *\n     * For a shorthand version of this method, see {@link thenUpdateSet}.\n     *\n     * To perform the `delete` action, see {@link thenDelete}.\n     *\n     * To perform the `do nothing` action, see {@link thenDoNothing}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenMatched()\n     *   .thenUpdate((ub) => ub\n     *     .set(sql`metadata['has_pets']`, 'Y')\n     *     .set({\n     *       updated_at: new Date().toISOString(),\n     *     })\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when matched then\n     *   update set metadata['has_pets'] = $1, \"updated_at\" = $2\n     * ```\n     */\n    thenUpdate(set) {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithThen(this.#props.queryNode, parseMergeThen(set(new UpdateQueryBuilder({\n                queryId: this.#props.queryId,\n                executor: NOOP_QUERY_EXECUTOR,\n                queryNode: UpdateQueryNode.createWithoutTable(),\n            })))),\n        });\n    }\n    thenUpdateSet(...args) {\n        // @ts-ignore not sure how to type this so it won't complain about set(...args).\n        return this.thenUpdate((ub) => ub.set(...args));\n    }\n}\nexport class NotMatchedThenableMergeQueryBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Performs the `do nothing` action.\n     *\n     * This is supported in PostgreSQL.\n     *\n     * To perform the `insert` action, see {@link thenInsertValues}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db.mergeInto('person')\n     *   .using('pet', 'person.id', 'pet.owner_id')\n     *   .whenNotMatched()\n     *   .thenDoNothing()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * merge into \"person\"\n     * using \"pet\" on \"person\".\"id\" = \"pet\".\"owner_id\"\n     * when not matched then\n     *   do nothing\n     * ```\n     */\n    thenDoNothing() {\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithThen(this.#props.queryNode, parseMergeThen('do nothing')),\n        });\n    }\n    thenInsertValues(insert) {\n        const [columns, values] = parseInsertExpression(insert);\n        return new WheneableMergeQueryBuilder({\n            ...this.#props,\n            queryNode: MergeQueryNode.cloneWithThen(this.#props.queryNode, parseMergeThen(InsertQueryNode.cloneWith(InsertQueryNode.createWithoutInto(), {\n                columns,\n                values,\n            }))),\n        });\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,SAAS,eAAe;QAC7F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8CC,GACD,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,uNAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACvF;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,OAAO,IAAI,EAAE;QACT,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,SAAS,eAAe;QAC7F;IACJ;IACA;;KAEC,GACD,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,cAAc;QACV,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC,EAAE;IAC/B;IACA,eAAe,GAAG,IAAI,EAAE;QACpB,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC;IAC7B;IACA;;;;;;KAMC,GACD,kBAAkB,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QAC5B,OAAO,IAAI,CAAC,CAAA,WAAY,CAAC;YAAC;YAAK;YAAI;SAAI,EAAE;IAC7C;IACA,CAAA,WAAY,CAAC,IAAI,EAAE,QAAQ;QACvB,OAAO,IAAI,iCAAiC;YACxC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,WAAW;YAAK,GAAG,MAAM;QAC7G;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8BC,GACD,iBAAiB;QACb,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC,EAAE;IAClC;IACA,kBAAkB,GAAG,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC;IAChC;IACA;;;;;;;;KAQC,GACD,qBAAqB,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QAC/B,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC;YAAC;YAAK;YAAI;SAAI,EAAE;IAChD;IACA;;;;;;KAMC,GACD,yBAAyB;QACrB,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC,EAAE,EAAE,OAAO;IAC3C;IACA,0BAA0B,GAAG,IAAI,EAAE;QAC/B,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC,MAAM,OAAO;IAC7C;IACA;;;;;KAKC,GACD,6BAA6B,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACvC,OAAO,IAAI,CAAC,CAAA,cAAe,CAAC;YAAC;YAAK;YAAI;SAAI,EAAE,MAAM;IACtD;IACA,UAAU,IAAI,EAAE;QACZ,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,OAAO,IAAI,EAAE;QACT,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QAC/E;IACJ;IACA,CAAA,cAAe,CAAC,IAAI,EAAE,WAAW,KAAK,EAAE,WAAW,KAAK;QACpD,MAAM,QAAQ;YACV,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,WAAW;gBAAO;YAAS,GAAG,MAAM;QACxH;QACA,MAAM,UAAU,WACV,mCACA;QACN,OAAO,IAAI,QAAQ;IACvB;IACA;;;;;;;;;;;;;;;;;;;;;;;;KAwBC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqCC,GACD,IAAI,SAAS,EAAE,IAAI,EAAE;QACjB,IAAI,WAAW;YACX,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;QAClB;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACzF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA;;;;KAIC,GACD,MAAM,UAAU;QACZ,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACzF,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ;QACxC,MAAM,QAAQ,cAAc,KAAK;QACjC,IAAI,AAAC,MAAM,SAAS,IAAI,QAAQ,iBAAiB,IAC5C,MAAM,MAAM,IAAI,QAAQ,cAAc,EAAG;YAC1C,OAAO,OAAO,IAAI;QACtB;QACA,OAAO;YAAC,IAAI,kOAAA,CAAA,cAAW,CAAC,OAAO,eAAe;SAAE;IACpD;IACA;;;KAGC,GACD,MAAM,mBAAmB;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;QACnC,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,wBAAwB,mBAAmB,wOAAA,CAAA,gBAAa,EAAE;QAC5D,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;QAC1C,IAAI,WAAW,WAAW;YACtB,MAAM,QAAQ,CAAA,GAAA,wOAAA,CAAA,6BAA0B,AAAD,EAAE,oBACnC,IAAI,iBAAiB,IAAI,CAAC,eAAe,MACzC,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM;QACV;QACA,OAAO;IACX;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,aAAa;QACT,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,gBAAgB;QACZ,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmCC,GACD,WAAW,GAAG,EAAE;QACZ,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,IAAI,6OAAA,CAAA,qBAAkB,CAAC;gBACrG,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;gBAC5B,UAAU,6OAAA,CAAA,sBAAmB;gBAC7B,WAAW,2OAAA,CAAA,kBAAe,CAAC,kBAAkB;YACjD;QACJ;IACJ;IACA,cAAc,GAAG,IAAI,EAAE;QACnB,gFAAgF;QAChF,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAO,GAAG,GAAG,IAAI;IAC7C;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,gBAAgB;QACZ,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE;QAClF;IACJ;IACA,iBAAiB,MAAM,EAAE;QACrB,MAAM,CAAC,SAAS,OAAO,GAAG,CAAA,GAAA,mOAAA,CAAA,wBAAqB,AAAD,EAAE;QAChD,OAAO,IAAI,2BAA2B;YAClC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,2OAAA,CAAA,kBAAe,CAAC,iBAAiB,IAAI;gBACzI;gBACA;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3468, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/select-query-builder.js"], "sourcesContent": ["/// <reference types=\"./select-query-builder.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { SelectModifierNode } from '../operation-node/select-modifier-node.js';\nimport { parseJoin, } from '../parser/join-parser.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { parseSelectArg, parseSelectAll, } from '../parser/select-parser.js';\nimport { parseReferenceExpressionOrList, } from '../parser/reference-parser.js';\nimport { SelectQueryNode } from '../operation-node/select-query-node.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { parseOrderBy, } from '../parser/order-by-parser.js';\nimport { LimitNode } from '../operation-node/limit-node.js';\nimport { OffsetNode } from '../operation-node/offset-node.js';\nimport { asArray, freeze } from '../util/object-utils.js';\nimport { parseGroupBy } from '../parser/group-by-parser.js';\nimport { isNoResultErrorConstructor, NoResultError, } from './no-result-error.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { parseSetOperations, } from '../parser/set-operation-parser.js';\nimport { parseValueBinaryOperationOrExpression, parseReferentialBinaryOperation, } from '../parser/binary-operation-parser.js';\nimport { ExpressionWrapper } from '../expression/expression-wrapper.js';\nimport { parseValueExpression, } from '../parser/value-parser.js';\nimport { parseFetch } from '../parser/fetch-parser.js';\nimport { parseTop } from '../parser/top-parser.js';\nclass SelectQueryBuilderImpl {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    get expressionType() {\n        return undefined;\n    }\n    get isSelectQueryBuilder() {\n        return true;\n    }\n    where(...args) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    whereRef(lhs, op, rhs) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithWhere(this.#props.queryNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    having(...args) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithHaving(this.#props.queryNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    havingRef(lhs, op, rhs) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithHaving(this.#props.queryNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    select(selection) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSelections(this.#props.queryNode, parseSelectArg(selection)),\n        });\n    }\n    distinctOn(selection) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithDistinctOn(this.#props.queryNode, parseReferenceExpressionOrList(selection)),\n        });\n    }\n    modifyFront(modifier) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithFrontModifier(this.#props.queryNode, SelectModifierNode.createWithExpression(modifier.toOperationNode())),\n        });\n    }\n    modifyEnd(modifier) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.createWithExpression(modifier.toOperationNode())),\n        });\n    }\n    distinct() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithFrontModifier(this.#props.queryNode, SelectModifierNode.create('Distinct')),\n        });\n    }\n    forUpdate(of) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('ForUpdate', of ? asArray(of).map(parseTable) : undefined)),\n        });\n    }\n    forShare(of) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('ForShare', of ? asArray(of).map(parseTable) : undefined)),\n        });\n    }\n    forKeyShare(of) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('ForKeyShare', of ? asArray(of).map(parseTable) : undefined)),\n        });\n    }\n    forNoKeyUpdate(of) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('ForNoKeyUpdate', of ? asArray(of).map(parseTable) : undefined)),\n        });\n    }\n    skipLocked() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('SkipLocked')),\n        });\n    }\n    noWait() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithEndModifier(this.#props.queryNode, SelectModifierNode.create('NoWait')),\n        });\n    }\n    selectAll(table) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSelections(this.#props.queryNode, parseSelectAll(table)),\n        });\n    }\n    innerJoin(...args) {\n        return this.#join('InnerJoin', args);\n    }\n    leftJoin(...args) {\n        return this.#join('LeftJoin', args);\n    }\n    rightJoin(...args) {\n        return this.#join('RightJoin', args);\n    }\n    fullJoin(...args) {\n        return this.#join('FullJoin', args);\n    }\n    crossJoin(...args) {\n        return this.#join('CrossJoin', args);\n    }\n    innerJoinLateral(...args) {\n        return this.#join('LateralInnerJoin', args);\n    }\n    leftJoinLateral(...args) {\n        return this.#join('LateralLeftJoin', args);\n    }\n    crossJoinLateral(...args) {\n        return this.#join('LateralCrossJoin', args);\n    }\n    crossApply(...args) {\n        return this.#join('CrossApply', args);\n    }\n    outerApply(...args) {\n        return this.#join('OuterApply', args);\n    }\n    #join(joinType, args) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithJoin(this.#props.queryNode, parseJoin(joinType, args)),\n        });\n    }\n    orderBy(...args) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithOrderByItems(this.#props.queryNode, parseOrderBy(args)),\n        });\n    }\n    groupBy(groupBy) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithGroupByItems(this.#props.queryNode, parseGroupBy(groupBy)),\n        });\n    }\n    limit(limit) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithLimit(this.#props.queryNode, LimitNode.create(parseValueExpression(limit))),\n        });\n    }\n    offset(offset) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithOffset(this.#props.queryNode, OffsetNode.create(parseValueExpression(offset))),\n        });\n    }\n    fetch(rowCount, modifier = 'only') {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithFetch(this.#props.queryNode, parseFetch(rowCount, modifier)),\n        });\n    }\n    top(expression, modifiers) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithTop(this.#props.queryNode, parseTop(expression, modifiers)),\n        });\n    }\n    union(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('union', expression, false)),\n        });\n    }\n    unionAll(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('union', expression, true)),\n        });\n    }\n    intersect(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('intersect', expression, false)),\n        });\n    }\n    intersectAll(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('intersect', expression, true)),\n        });\n    }\n    except(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('except', expression, false)),\n        });\n    }\n    exceptAll(expression) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithSetOperations(this.#props.queryNode, parseSetOperations('except', expression, true)),\n        });\n    }\n    as(alias) {\n        return new AliasedSelectQueryBuilderImpl(this, alias);\n    }\n    clearSelect() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithoutSelections(this.#props.queryNode),\n        });\n    }\n    clearWhere() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutWhere(this.#props.queryNode),\n        });\n    }\n    clearLimit() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithoutLimit(this.#props.queryNode),\n        });\n    }\n    clearOffset() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithoutOffset(this.#props.queryNode),\n        });\n    }\n    clearOrderBy() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithoutOrderBy(this.#props.queryNode),\n        });\n    }\n    clearGroupBy() {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: SelectQueryNode.cloneWithoutGroupBy(this.#props.queryNode),\n        });\n    }\n    $call(func) {\n        return func(this);\n    }\n    $if(condition, func) {\n        if (condition) {\n            return func(this);\n        }\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n        });\n    }\n    $castTo() {\n        return new SelectQueryBuilderImpl(this.#props);\n    }\n    $narrowType() {\n        return new SelectQueryBuilderImpl(this.#props);\n    }\n    $assertType() {\n        return new SelectQueryBuilderImpl(this.#props);\n    }\n    $asTuple() {\n        return new ExpressionWrapper(this.toOperationNode());\n    }\n    $asScalar() {\n        return new ExpressionWrapper(this.toOperationNode());\n    }\n    withPlugin(plugin) {\n        return new SelectQueryBuilderImpl({\n            ...this.#props,\n            executor: this.#props.executor.withPlugin(plugin),\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.queryNode, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        const compiledQuery = this.compile();\n        const result = await this.#props.executor.executeQuery(compiledQuery, this.#props.queryId);\n        return result.rows;\n    }\n    async executeTakeFirst() {\n        const [result] = await this.execute();\n        return result;\n    }\n    async executeTakeFirstOrThrow(errorConstructor = NoResultError) {\n        const result = await this.executeTakeFirst();\n        if (result === undefined) {\n            const error = isNoResultErrorConstructor(errorConstructor)\n                ? new errorConstructor(this.toOperationNode())\n                : errorConstructor(this.toOperationNode());\n            throw error;\n        }\n        return result;\n    }\n    async *stream(chunkSize = 100) {\n        const compiledQuery = this.compile();\n        const stream = this.#props.executor.stream(compiledQuery, chunkSize, this.#props.queryId);\n        for await (const item of stream) {\n            yield* item.rows;\n        }\n    }\n    async explain(format, options) {\n        const builder = new SelectQueryBuilderImpl({\n            ...this.#props,\n            queryNode: QueryNode.cloneWithExplain(this.#props.queryNode, format, options),\n        });\n        return await builder.execute();\n    }\n}\nexport function createSelectQueryBuilder(props) {\n    return new SelectQueryBuilderImpl(props);\n}\n/**\n * {@link SelectQueryBuilder} with an alias. The result of calling {@link SelectQueryBuilder.as}.\n */\nclass AliasedSelectQueryBuilderImpl {\n    #queryBuilder;\n    #alias;\n    constructor(queryBuilder, alias) {\n        this.#queryBuilder = queryBuilder;\n        this.#alias = alias;\n    }\n    get expression() {\n        return this.#queryBuilder;\n    }\n    get alias() {\n        return this.#alias;\n    }\n    get isAliasedSelectQueryBuilder() {\n        return true;\n    }\n    toOperationNode() {\n        return AliasNode.create(this.#queryBuilder.toOperationNode(), IdentifierNode.create(this.#alias));\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AACA,MAAM;IACF,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,IAAI,uBAAuB;QACvB,OAAO;IACX;IACA,MAAM,GAAG,IAAI,EAAE;QACX,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACrG;IACJ;IACA,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACnB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QACxG;IACJ;IACA,OAAO,GAAG,IAAI,EAAE;QACZ,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QAC5G;IACJ;IACA,UAAU,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACpB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QAC/G;IACJ;IACA,OAAO,SAAS,EAAE;QACd,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QACzF;IACJ;IACA,WAAW,SAAS,EAAE;QAClB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE;QACzG;IACJ;IACA,YAAY,QAAQ,EAAE;QAClB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,oBAAoB,CAAC,SAAS,eAAe;QAC7I;IACJ;IACA,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,oBAAoB,CAAC,SAAS,eAAe;QACrI;IACJ;IACA,WAAW;QACP,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;QACvG;IACJ;IACA,UAAU,EAAE,EAAE;QACV,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,aAAa,KAAK,CAAA,GAAA,sNAAA,CAAA,UAAO,AAAD,EAAE,IAAI,GAAG,CAAC,wNAAA,CAAA,aAAU,IAAI;QAC/I;IACJ;IACA,SAAS,EAAE,EAAE;QACT,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,YAAY,KAAK,CAAA,GAAA,sNAAA,CAAA,UAAO,AAAD,EAAE,IAAI,GAAG,CAAC,wNAAA,CAAA,aAAU,IAAI;QAC9I;IACJ;IACA,YAAY,EAAE,EAAE;QACZ,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,eAAe,KAAK,CAAA,GAAA,sNAAA,CAAA,UAAO,AAAD,EAAE,IAAI,GAAG,CAAC,wNAAA,CAAA,aAAU,IAAI;QACjJ;IACJ;IACA,eAAe,EAAE,EAAE;QACf,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,kBAAkB,KAAK,CAAA,GAAA,sNAAA,CAAA,UAAO,AAAD,EAAE,IAAI,GAAG,CAAC,wNAAA,CAAA,aAAU,IAAI;QACpJ;IACJ;IACA,aAAa;QACT,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;QAC/F;IACJ;IACA,SAAS;QACL,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;QAC/F;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;QACzF;IACJ;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,SAAS,GAAG,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,YAAY;IAClC;IACA,UAAU,GAAG,IAAI,EAAE;QACf,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,aAAa;IACnC;IACA,iBAAiB,GAAG,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,oBAAoB;IAC1C;IACA,gBAAgB,GAAG,IAAI,EAAE;QACrB,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,mBAAmB;IACzC;IACA,iBAAiB,GAAG,IAAI,EAAE;QACtB,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,oBAAoB;IAC1C;IACA,WAAW,GAAG,IAAI,EAAE;QAChB,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,cAAc;IACpC;IACA,WAAW,GAAG,IAAI,EAAE;QAChB,OAAO,IAAI,CAAC,CAAA,IAAK,CAAC,cAAc;IACpC;IACA,CAAA,IAAK,CAAC,QAAQ,EAAE,IAAI;QAChB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,uNAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAClF;IACJ;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QACnF;IACJ;IACA,QAAQ,OAAO,EAAE;QACb,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QACzF;IACJ;IACA,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC3G;IACJ;IACA,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,kOAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC7G;IACJ;IACA,MAAM,QAAQ,EAAE,WAAW,MAAM,EAAE;QAC/B,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QAC1F;IACJ;IACA,IAAI,UAAU,EAAE,SAAS,EAAE;QACvB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QAClF;IACJ;IACA,MAAM,UAAU,EAAE;QACd,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,YAAY;QACrH;IACJ;IACA,SAAS,UAAU,EAAE;QACjB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,YAAY;QACrH;IACJ;IACA,UAAU,UAAU,EAAE;QAClB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,YAAY;QACzH;IACJ;IACA,aAAa,UAAU,EAAE;QACrB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,YAAY;QACzH;IACJ;IACA,OAAO,UAAU,EAAE;QACf,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,YAAY;QACtH;IACJ;IACA,UAAU,UAAU,EAAE;QAClB,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,YAAY;QACtH;IACJ;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,8BAA8B,IAAI,EAAE;IACnD;IACA,cAAc;QACV,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAC3E;IACJ;IACA,aAAa;QACT,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAChE;IACJ;IACA,aAAa;QACT,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACtE;IACJ;IACA,cAAc;QACV,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACvE;IACJ;IACA,eAAe;QACX,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QAClE;IACJ;IACA,eAAe;QACX,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS;QACxE;IACJ;IACA,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,IAAI,SAAS,EAAE,IAAI,EAAE;QACjB,IAAI,WAAW;YACX,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;QAClB;IACJ;IACA,UAAU;QACN,OAAO,IAAI,uBAAuB,IAAI,CAAC,CAAA,KAAM;IACjD;IACA,cAAc;QACV,OAAO,IAAI,uBAAuB,IAAI,CAAC,CAAA,KAAM;IACjD;IACA,cAAc;QACV,OAAO,IAAI,uBAAuB,IAAI,CAAC,CAAA,KAAM;IACjD;IACA,WAAW;QACP,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,eAAe;IACrD;IACA,YAAY;QACR,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,eAAe;IACrD;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,uBAAuB;YAC9B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,UAAU,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC9C;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACzF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACzF,OAAO,OAAO,IAAI;IACtB;IACA,MAAM,mBAAmB;QACrB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO;QACnC,OAAO;IACX;IACA,MAAM,wBAAwB,mBAAmB,wOAAA,CAAA,gBAAa,EAAE;QAC5D,MAAM,SAAS,MAAM,IAAI,CAAC,gBAAgB;QAC1C,IAAI,WAAW,WAAW;YACtB,MAAM,QAAQ,CAAA,GAAA,wOAAA,CAAA,6BAA0B,AAAD,EAAE,oBACnC,IAAI,iBAAiB,IAAI,CAAC,eAAe,MACzC,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM;QACV;QACA,OAAO;IACX;IACA,OAAO,OAAO,YAAY,GAAG,EAAE;QAC3B,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,MAAM,SAAS,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,WAAW,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QACxF,WAAW,MAAM,QAAQ,OAAQ;YAC7B,OAAO,KAAK,IAAI;QACpB;IACJ;IACA,MAAM,QAAQ,MAAM,EAAE,OAAO,EAAE;QAC3B,MAAM,UAAU,IAAI,uBAAuB;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,WAAW,iOAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,SAAS,EAAE,QAAQ;QACzE;QACA,OAAO,MAAM,QAAQ,OAAO;IAChC;AACJ;AACO,SAAS,yBAAyB,KAAK;IAC1C,OAAO,IAAI,uBAAuB;AACtC;AACA;;CAEC,GACD,MAAM;IACF,CAAA,YAAa,CAAC;IACd,CAAA,KAAM,CAAC;IACP,YAAY,YAAY,EAAE,KAAK,CAAE;QAC7B,IAAI,CAAC,CAAA,YAAa,GAAG;QACrB,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,YAAa;IAC7B;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,IAAI,8BAA8B;QAC9B,OAAO;IACX;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,YAAa,CAAC,eAAe,IAAI,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IACnG;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3869, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/aggregate-function-builder.js"], "sourcesContent": ["/// <reference types=\"./aggregate-function-builder.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { AggregateFunctionNode } from '../operation-node/aggregate-function-node.js';\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { createOverBuilder } from '../parser/parse-utils.js';\nimport { parseReferentialBinaryOperation, parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nimport { parseOrderBy, } from '../parser/order-by-parser.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nexport class AggregateFunctionBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /** @private */\n    get expressionType() {\n        return undefined;\n    }\n    /**\n     * Returns an aliased version of the function.\n     *\n     * In addition to slapping `as \"the_alias\"` to the end of the SQL,\n     * this method also provides strict typing:\n     *\n     * ```ts\n     * const result = await db\n     *   .selectFrom('person')\n     *   .select(\n     *     (eb) => eb.fn.count<number>('id').as('person_count')\n     *   )\n     *   .executeTakeFirstOrThrow()\n     *\n     * // `person_count: number` field exists in the result type.\n     * console.log(result.person_count)\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select count(\"id\") as \"person_count\"\n     * from \"person\"\n     * ```\n     */\n    as(alias) {\n        return new AliasedAggregateFunctionBuilder(this, alias);\n    }\n    /**\n     * Adds a `distinct` clause inside the function.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db\n     *   .selectFrom('person')\n     *   .select((eb) =>\n     *     eb.fn.count<number>('first_name').distinct().as('first_name_count')\n     *   )\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select count(distinct \"first_name\") as \"first_name_count\"\n     * from \"person\"\n     * ```\n     */\n    distinct() {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: AggregateFunctionNode.cloneWithDistinct(this.#props.aggregateFunctionNode),\n        });\n    }\n    orderBy(...args) {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: QueryNode.cloneWithOrderByItems(this.#props.aggregateFunctionNode, parseOrderBy(args)),\n        });\n    }\n    clearOrderBy() {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: QueryNode.cloneWithoutOrderBy(this.#props.aggregateFunctionNode),\n        });\n    }\n    withinGroupOrderBy(...args) {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: AggregateFunctionNode.cloneWithOrderBy(this.#props.aggregateFunctionNode, parseOrderBy(args), true),\n        });\n    }\n    filterWhere(...args) {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: AggregateFunctionNode.cloneWithFilter(this.#props.aggregateFunctionNode, parseValueBinaryOperationOrExpression(args)),\n        });\n    }\n    /**\n     * Adds a `filter` clause with a nested `where` clause after the function, where\n     * both sides of the operator are references to columns.\n     *\n     * Similar to {@link WhereInterface}'s `whereRef` method.\n     *\n     * ### Examples\n     *\n     * Count people with same first and last names versus general public:\n     *\n     * ```ts\n     * const result = await db\n     *   .selectFrom('person')\n     *   .select((eb) => [\n     *     eb.fn\n     *       .count<number>('id')\n     *       .filterWhereRef('first_name', '=', 'last_name')\n     *       .as('repeat_name_count'),\n     *     eb.fn.count<number>('id').as('total_count'),\n     *   ])\n     *   .executeTakeFirstOrThrow()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select\n     *   count(\"id\") filter(where \"first_name\" = \"last_name\") as \"repeat_name_count\",\n     *   count(\"id\") as \"total_count\"\n     * from \"person\"\n     * ```\n     */\n    filterWhereRef(lhs, op, rhs) {\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: AggregateFunctionNode.cloneWithFilter(this.#props.aggregateFunctionNode, parseReferentialBinaryOperation(lhs, op, rhs)),\n        });\n    }\n    /**\n     * Adds an `over` clause (window functions) after the function.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * const result = await db\n     *   .selectFrom('person')\n     *   .select(\n     *     (eb) => eb.fn.avg<number>('age').over().as('average_age')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select avg(\"age\") over() as \"average_age\"\n     * from \"person\"\n     * ```\n     *\n     * Also supports passing a callback that returns an over builder,\n     * allowing to add partition by and sort by clauses inside over.\n     *\n     * ```ts\n     * const result = await db\n     *   .selectFrom('person')\n     *   .select(\n     *     (eb) => eb.fn.avg<number>('age').over(\n     *       ob => ob.partitionBy('last_name').orderBy('first_name', 'asc')\n     *     ).as('average_age')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select avg(\"age\") over(partition by \"last_name\" order by \"first_name\" asc) as \"average_age\"\n     * from \"person\"\n     * ```\n     */\n    over(over) {\n        const builder = createOverBuilder();\n        return new AggregateFunctionBuilder({\n            ...this.#props,\n            aggregateFunctionNode: AggregateFunctionNode.cloneWithOver(this.#props.aggregateFunctionNode, (over ? over(builder) : builder).toOperationNode()),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    /**\n     * Casts the expression to the given type.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `AggregateFunctionBuilder` with a new output type.\n     */\n    $castTo() {\n        return new AggregateFunctionBuilder(this.#props);\n    }\n    /**\n     * Omit null from the expression's type.\n     *\n     * This function can be useful in cases where you know an expression can't be\n     * null, but Kysely is unable to infer it.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of `this` with a new output type.\n     */\n    $notNull() {\n        return new AggregateFunctionBuilder(this.#props);\n    }\n    toOperationNode() {\n        return this.#props.aggregateFunctionNode;\n    }\n}\n/**\n * {@link AggregateFunctionBuilder} with an alias. The result of calling {@link AggregateFunctionBuilder.as}.\n */\nexport class AliasedAggregateFunctionBuilder {\n    #aggregateFunctionBuilder;\n    #alias;\n    constructor(aggregateFunctionBuilder, alias) {\n        this.#aggregateFunctionBuilder = aggregateFunctionBuilder;\n        this.#alias = alias;\n    }\n    /** @private */\n    get expression() {\n        return this.#aggregateFunctionBuilder;\n    }\n    /** @private */\n    get alias() {\n        return this.#alias;\n    }\n    toOperationNode() {\n        return AliasNode.create(this.#aggregateFunctionBuilder.toOperationNode(), IdentifierNode.create(this.#alias));\n    }\n}\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,aAAa,GACb,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA;;;;;;;;;;;;;;;;;;;;;;;;KAwBC,GACD,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,gCAAgC,IAAI,EAAE;IACrD;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,WAAW;QACP,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB;QACpG;IACJ;IACA,QAAQ,GAAG,IAAI,EAAE;QACb,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iOAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE;QAC3G;IACJ;IACA,eAAe;QACX,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iOAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB;QAC1F;IACJ;IACA,mBAAmB,GAAG,IAAI,EAAE;QACxB,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB,EAAE,CAAA,GAAA,8NAAA,CAAA,eAAY,AAAD,EAAE,OAAO;QACzH;IACJ;IACA,YAAY,GAAG,IAAI,EAAE;QACjB,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB,EAAE,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QAC1I;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BC,GACD,eAAe,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE;QACzB,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB,EAAE,CAAA,GAAA,sOAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,IAAI;QAC7I;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyCC,GACD,KAAK,IAAI,EAAE;QACP,MAAM,UAAU,CAAA,GAAA,uNAAA,CAAA,oBAAiB,AAAD;QAChC,OAAO,IAAI,yBAAyB;YAChC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB,EAAE,CAAC,OAAO,KAAK,WAAW,OAAO,EAAE,eAAe;QAClJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,yBAAyB,IAAI,CAAC,CAAA,KAAM;IACnD;IACA;;;;;;;;KAQC,GACD,WAAW;QACP,OAAO,IAAI,yBAAyB,IAAI,CAAC,CAAA,KAAM;IACnD;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,qBAAqB;IAC5C;AACJ;AAIO,MAAM;IACT,CAAA,wBAAyB,CAAC;IAC1B,CAAA,KAAM,CAAC;IACP,YAAY,wBAAwB,EAAE,KAAK,CAAE;QACzC,IAAI,CAAC,CAAA,wBAAyB,GAAG;QACjC,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,aAAa,GACb,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,wBAAyB;IACzC;IACA,aAAa,GACb,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,wBAAyB,CAAC,eAAe,IAAI,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IAC/G;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4112, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/function-module.js"], "sourcesContent": ["/// <reference types=\"./function-module.d.ts\" />\nimport { ExpressionWrapper } from '../expression/expression-wrapper.js';\nimport { AggregateFunctionNode } from '../operation-node/aggregate-function-node.js';\nimport { FunctionNode } from '../operation-node/function-node.js';\nimport { parseReferenceExpressionOrList, } from '../parser/reference-parser.js';\nimport { parseSelectAll } from '../parser/select-parser.js';\nimport { AggregateFunctionBuilder } from './aggregate-function-builder.js';\nimport { isString } from '../util/object-utils.js';\nimport { parseTable } from '../parser/table-parser.js';\nexport function createFunctionModule() {\n    const fn = (name, args) => {\n        return new ExpressionWrapper(FunctionNode.create(name, parseReferenceExpressionOrList(args ?? [])));\n    };\n    const agg = (name, args) => {\n        return new AggregateFunctionBuilder({\n            aggregateFunctionNode: AggregateFunctionNode.create(name, args ? parseReferenceExpressionOrList(args) : undefined),\n        });\n    };\n    return Object.assign(fn, {\n        agg,\n        avg(column) {\n            return agg('avg', [column]);\n        },\n        coalesce(...values) {\n            return fn('coalesce', values);\n        },\n        count(column) {\n            return agg('count', [column]);\n        },\n        countAll(table) {\n            return new AggregateFunctionBuilder({\n                aggregateFunctionNode: AggregateFunctionNode.create('count', parseSelectAll(table)),\n            });\n        },\n        max(column) {\n            return agg('max', [column]);\n        },\n        min(column) {\n            return agg('min', [column]);\n        },\n        sum(column) {\n            return agg('sum', [column]);\n        },\n        any(column) {\n            return fn('any', [column]);\n        },\n        jsonAgg(table) {\n            return new AggregateFunctionBuilder({\n                aggregateFunctionNode: AggregateFunctionNode.create('json_agg', [\n                    isString(table) ? parseTable(table) : table.toOperationNode(),\n                ]),\n            });\n        },\n        toJson(table) {\n            return new ExpressionWrapper(FunctionNode.create('to_json', [\n                isString(table) ? parseTable(table) : table.toOperationNode(),\n            ]));\n        },\n    });\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,SAAS;IACZ,MAAM,KAAK,CAAC,MAAM;QACd,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE,QAAQ,EAAE;IACpG;IACA,MAAM,MAAM,CAAC,MAAM;QACf,OAAO,IAAI,mPAAA,CAAA,2BAAwB,CAAC;YAChC,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,MAAM,OAAO,CAAA,GAAA,4NAAA,CAAA,iCAA8B,AAAD,EAAE,QAAQ;QAC5G;IACJ;IACA,OAAO,OAAO,MAAM,CAAC,IAAI;QACrB;QACA,KAAI,MAAM;YACN,OAAO,IAAI,OAAO;gBAAC;aAAO;QAC9B;QACA,UAAS,GAAG,MAAM;YACd,OAAO,GAAG,YAAY;QAC1B;QACA,OAAM,MAAM;YACR,OAAO,IAAI,SAAS;gBAAC;aAAO;QAChC;QACA,UAAS,KAAK;YACV,OAAO,IAAI,mPAAA,CAAA,2BAAwB,CAAC;gBAChC,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,SAAS,CAAA,GAAA,yNAAA,CAAA,iBAAc,AAAD,EAAE;YAChF;QACJ;QACA,KAAI,MAAM;YACN,OAAO,IAAI,OAAO;gBAAC;aAAO;QAC9B;QACA,KAAI,MAAM;YACN,OAAO,IAAI,OAAO;gBAAC;aAAO;QAC9B;QACA,KAAI,MAAM;YACN,OAAO,IAAI,OAAO;gBAAC;aAAO;QAC9B;QACA,KAAI,MAAM;YACN,OAAO,GAAG,OAAO;gBAAC;aAAO;QAC7B;QACA,SAAQ,KAAK;YACT,OAAO,IAAI,mPAAA,CAAA,2BAAwB,CAAC;gBAChC,uBAAuB,iPAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,YAAY;oBAC5D,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,SAAS,MAAM,eAAe;iBAC9D;YACL;QACJ;QACA,QAAO,KAAK;YACR,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,oOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,WAAW;gBACxD,CAAA,GAAA,sNAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,SAAS,MAAM,eAAe;aAC9D;QACL;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4201, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/case-builder.js"], "sourcesContent": ["/// <reference types=\"./case-builder.d.ts\" />\nimport { ExpressionWrapper } from '../expression/expression-wrapper.js';\nimport { freeze } from '../util/object-utils.js';\nimport { CaseNode } from '../operation-node/case-node.js';\nimport { WhenNode } from '../operation-node/when-node.js';\nimport { parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nimport { isSafeImmediateValue, parseSafeImmediateValue, parseValueExpression, } from '../parser/value-parser.js';\nexport class CaseBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    when(...args) {\n        return new CaseThenBuilder({\n            ...this.#props,\n            node: CaseNode.cloneWithWhen(this.#props.node, WhenNode.create(parseValueBinaryOperationOrExpression(args))),\n        });\n    }\n}\nexport class CaseThenBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    then(valueExpression) {\n        return new CaseWhenBuilder({\n            ...this.#props,\n            node: CaseNode.cloneWithThen(this.#props.node, isSafeImmediateValue(valueExpression)\n                ? parseSafeImmediateValue(valueExpression)\n                : parseValueExpression(valueExpression)),\n        });\n    }\n}\nexport class CaseWhenBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    when(...args) {\n        return new CaseThenBuilder({\n            ...this.#props,\n            node: CaseNode.cloneWithWhen(this.#props.node, WhenNode.create(parseValueBinaryOperationOrExpression(args))),\n        });\n    }\n    else(valueExpression) {\n        return new CaseEndBuilder({\n            ...this.#props,\n            node: CaseNode.cloneWith(this.#props.node, {\n                else: isSafeImmediateValue(valueExpression)\n                    ? parseSafeImmediateValue(valueExpression)\n                    : parseValueExpression(valueExpression),\n            }),\n        });\n    }\n    end() {\n        return new ExpressionWrapper(CaseNode.cloneWith(this.#props.node, { isStatement: false }));\n    }\n    endCase() {\n        return new ExpressionWrapper(CaseNode.cloneWith(this.#props.node, { isStatement: true }));\n    }\n}\nexport class CaseEndBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    end() {\n        return new ExpressionWrapper(CaseNode.cloneWith(this.#props.node, { isStatement: false }));\n    }\n    endCase() {\n        return new ExpressionWrapper(CaseNode.cloneWith(this.#props.node, { isStatement: true }));\n    }\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;;;AAC7C;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,KAAK,GAAG,IAAI,EAAE;QACV,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,gOAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACzG;IACJ;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,KAAK,eAAe,EAAE;QAClB,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,gOAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,mBAC9D,CAAA,GAAA,wNAAA,CAAA,0BAAuB,AAAD,EAAE,mBACxB,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;QAC/B;IACJ;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,KAAK,GAAG,IAAI,EAAE;QACV,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,gOAAA,CAAA,WAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,gOAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE;QACzG;IACJ;IACA,KAAK,eAAe,EAAE;QAClB,OAAO,IAAI,eAAe;YACtB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,gOAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvC,MAAM,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE,mBACrB,CAAA,GAAA,wNAAA,CAAA,0BAAuB,AAAD,EAAE,mBACxB,CAAA,GAAA,wNAAA,CAAA,uBAAoB,AAAD,EAAE;YAC/B;QACJ;IACJ;IACA,MAAM;QACF,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,gOAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;YAAE,aAAa;QAAM;IAC3F;IACA,UAAU;QACN,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,gOAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;YAAE,aAAa;QAAK;IAC1F;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,MAAM;QACF,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,gOAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;YAAE,aAAa;QAAM;IAC3F;IACA,UAAU;QACN,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC,gOAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;YAAE,aAAa;QAAK;IAC1F;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4296, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/query-builder/json-path-builder.js"], "sourcesContent": ["/// <reference types=\"./json-path-builder.d.ts\" />\nimport { AliasNode } from '../operation-node/alias-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { JSONOperator<PERSON>hainNode } from '../operation-node/json-operator-chain-node.js';\nimport { JSONPathLegNode, } from '../operation-node/json-path-leg-node.js';\nimport { JSONPathNode } from '../operation-node/json-path-node.js';\nimport { JSONReferenceNode } from '../operation-node/json-reference-node.js';\nimport { isOperationNodeSource } from '../operation-node/operation-node-source.js';\nimport { ValueNode } from '../operation-node/value-node.js';\nexport class JSONPathBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /**\n     * Access an element of a JSON array in a specific location.\n     *\n     * Since there's no guarantee an element exists in the given array location, the\n     * resulting type is always nullable. If you're sure the element exists, you\n     * should use {@link SelectQueryBuilder.$assertType} to narrow the type safely.\n     *\n     * See also {@link key} to access properties of JSON objects.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.selectFrom('person')\n     *   .select(eb =>\n     *     eb.ref('nicknames', '->').at(0).as('primary_nickname')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"nicknames\"->0 as \"primary_nickname\" from \"person\"\n     *```\n     *\n     * Combined with {@link key}:\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('experience', '->').at(0).key('role').as('first_role')\n     * )\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"experience\"->0->'role' as \"first_role\" from \"person\"\n     * ```\n     *\n     * You can use `'last'` to access the last element of the array in MySQL:\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('nicknames', '->$').at('last').as('last_nickname')\n     * )\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * select `nicknames`->'$[last]' as `last_nickname` from `person`\n     * ```\n     *\n     * Or `'#-1'` in SQLite:\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('nicknames', '->>$').at('#-1').as('last_nickname')\n     * )\n     * ```\n     *\n     * The generated SQL (SQLite):\n     *\n     * ```sql\n     * select \"nicknames\"->>'$[#-1]' as `last_nickname` from `person`\n     * ```\n     */\n    at(index) {\n        return this.#createBuilderWithPathLeg('ArrayLocation', index);\n    }\n    /**\n     * Access a property of a JSON object.\n     *\n     * If a field is optional, the resulting type will be nullable.\n     *\n     * See also {@link at} to access elements of JSON arrays.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('address', '->').key('city').as('city')\n     * )\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"address\"->'city' as \"city\" from \"person\"\n     * ```\n     *\n     * Going deeper:\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('profile', '->$').key('website').key('url').as('website_url')\n     * )\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * select `profile`->'$.website.url' as `website_url` from `person`\n     * ```\n     *\n     * Combined with {@link at}:\n     *\n     * ```ts\n     * db.selectFrom('person').select(eb =>\n     *   eb.ref('profile', '->').key('addresses').at(0).key('city').as('city')\n     * )\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * select \"profile\"->'addresses'->0->'city' as \"city\" from \"person\"\n     * ```\n     */\n    key(key) {\n        return this.#createBuilderWithPathLeg('Member', key);\n    }\n    #createBuilderWithPathLeg(legType, value) {\n        if (JSONReferenceNode.is(this.#node)) {\n            return new TraversedJSONPathBuilder(JSONReferenceNode.cloneWithTraversal(this.#node, JSONPathNode.is(this.#node.traversal)\n                ? JSONPathNode.cloneWithLeg(this.#node.traversal, JSONPathLegNode.create(legType, value))\n                : JSONOperatorChainNode.cloneWithValue(this.#node.traversal, ValueNode.createImmediate(value))));\n        }\n        return new TraversedJSONPathBuilder(JSONPathNode.cloneWithLeg(this.#node, JSONPathLegNode.create(legType, value)));\n    }\n}\nexport class TraversedJSONPathBuilder extends JSONPathBuilder {\n    #node;\n    constructor(node) {\n        super(node);\n        this.#node = node;\n    }\n    /** @private */\n    get expressionType() {\n        return undefined;\n    }\n    as(alias) {\n        return new AliasedJSONPathBuilder(this, alias);\n    }\n    /**\n     * Change the output type of the json path.\n     *\n     * This method call doesn't change the SQL in any way. This methods simply\n     * returns a copy of this `JSONPathBuilder` with a new output type.\n     */\n    $castTo() {\n        return new TraversedJSONPathBuilder(this.#node);\n    }\n    $notNull() {\n        return new TraversedJSONPathBuilder(this.#node);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\nexport class AliasedJSONPathBuilder {\n    #jsonPath;\n    #alias;\n    constructor(jsonPath, alias) {\n        this.#jsonPath = jsonPath;\n        this.#alias = alias;\n    }\n    /** @private */\n    get expression() {\n        return this.#jsonPath;\n    }\n    /** @private */\n    get alias() {\n        return this.#alias;\n    }\n    toOperationNode() {\n        return AliasNode.create(this.#jsonPath.toOperationNode(), isOperationNodeSource(this.#alias)\n            ? this.#alias.toOperationNode()\n            : IdentifierNode.create(this.#alias));\n    }\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkEC,GACD,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,CAAC,CAAA,wBAAyB,CAAC,iBAAiB;IAC3D;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgDC,GACD,IAAI,GAAG,EAAE;QACL,OAAO,IAAI,CAAC,CAAA,wBAAyB,CAAC,UAAU;IACpD;IACA,CAAA,wBAAyB,CAAC,OAAO,EAAE,KAAK;QACpC,IAAI,6OAAA,CAAA,oBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,IAAK,GAAG;YAClC,OAAO,IAAI,yBAAyB,6OAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,wOAAA,CAAA,eAAY,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,SAAS,IACnH,wOAAA,CAAA,eAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,SAAS,EAAE,+OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,SAAS,UAChF,qPAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,SAAS,EAAE,iOAAA,CAAA,YAAS,CAAC,eAAe,CAAC;QAC/F;QACA,OAAO,IAAI,yBAAyB,wOAAA,CAAA,eAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,+OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,SAAS;IAC9G;AACJ;AACO,MAAM,iCAAiC;IAC1C,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,aAAa,GACb,IAAI,iBAAiB;QACjB,OAAO;IACX;IACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,uBAAuB,IAAI,EAAE;IAC5C;IACA;;;;;KAKC,GACD,UAAU;QACN,OAAO,IAAI,yBAAyB,IAAI,CAAC,CAAA,IAAK;IAClD;IACA,WAAW;QACP,OAAO,IAAI,yBAAyB,IAAI,CAAC,CAAA,IAAK;IAClD;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ;AACO,MAAM;IACT,CAAA,QAAS,CAAC;IACV,CAAA,KAAM,CAAC;IACP,YAAY,QAAQ,EAAE,KAAK,CAAE;QACzB,IAAI,CAAC,CAAA,QAAS,GAAG;QACjB,IAAI,CAAC,CAAA,KAAM,GAAG;IAClB;IACA,aAAa,GACb,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,CAAA,QAAS;IACzB;IACA,aAAa,GACb,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,CAAA,KAAM;IACtB;IACA,kBAAkB;QACd,OAAO,iOAAA,CAAA,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,CAAC,eAAe,IAAI,CAAA,GAAA,+OAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,CAAA,KAAM,IACrF,IAAI,CAAC,CAAA,KAAM,CAAC,eAAe,KAC3B,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}]}