{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/column-definition-builder.js"], "sourcesContent": ["/// <reference types=\"./column-definition-builder.d.ts\" />\nimport { CheckConstraintNode } from '../operation-node/check-constraint-node.js';\nimport { ReferencesNode, } from '../operation-node/references-node.js';\nimport { SelectAllNode } from '../operation-node/select-all-node.js';\nimport { parseStringReference } from '../parser/reference-parser.js';\nimport { ColumnDefinitionNode } from '../operation-node/column-definition-node.js';\nimport { parseDefaultValueExpression, } from '../parser/default-value-parser.js';\nimport { GeneratedNode } from '../operation-node/generated-node.js';\nimport { DefaultValueNode } from '../operation-node/default-value-node.js';\nimport { parseOnModifyForeignAction } from '../parser/on-modify-action-parser.js';\nexport class ColumnDefinitionBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /**\n     * Adds `auto_increment` or `autoincrement` to the column definition\n     * depending on the dialect.\n     *\n     * Some dialects like PostgreSQL don't support this. On PostgreSQL\n     * you can use the `serial` or `bigserial` data type instead.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.autoIncrement().primaryKey())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `id` integer primary key auto_increment\n     * )\n     * ```\n     */\n    autoIncrement() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { autoIncrement: true }));\n    }\n    /**\n     * Makes the column an identity column.\n     *\n     * This only works on some dialects like MS SQL Server (MSSQL).\n     *\n     * For PostgreSQL's `generated always as identity` use {@link generatedAlwaysAsIdentity}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.identity().primaryKey())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MSSQL):\n     *\n     * ```sql\n     * create table \"person\" (\n     *   \"id\" integer identity primary key\n     * )\n     * ```\n     */\n    identity() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { identity: true }));\n    }\n    /**\n     * Makes the column the primary key.\n     *\n     * If you want to specify a composite primary key use the\n     * {@link CreateTableBuilder.addPrimaryKeyConstraint} method.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `id` integer primary key\n     * )\n     */\n    primaryKey() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { primaryKey: true }));\n    }\n    /**\n     * Adds a foreign key constraint for the column.\n     *\n     * If your database engine doesn't support foreign key constraints in the\n     * column definition (like MySQL 5) you need to call the table level\n     * {@link CreateTableBuilder.addForeignKeyConstraint} method instead.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('owner_id', 'integer', (col) => col.references('person.id'))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"pet\" (\n     *   \"owner_id\" integer references \"person\" (\"id\")\n     * )\n     * ```\n     */\n    references(ref) {\n        const references = parseStringReference(ref);\n        if (!references.table || SelectAllNode.is(references.column)) {\n            throw new Error(`invalid call references('${ref}'). The reference must have format table.column or schema.table.column`);\n        }\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            references: ReferencesNode.create(references.table, [\n                references.column,\n            ]),\n        }));\n    }\n    /**\n     * Adds an `on delete` constraint for the foreign key column.\n     *\n     * If your database engine doesn't support foreign key constraints in the\n     * column definition (like MySQL 5) you need to call the table level\n     * {@link CreateTableBuilder.addForeignKeyConstraint} method instead.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn(\n     *     'owner_id',\n     *     'integer',\n     *     (col) => col.references('person.id').onDelete('cascade')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"pet\" (\n     *   \"owner_id\" integer references \"person\" (\"id\") on delete cascade\n     * )\n     * ```\n     */\n    onDelete(onDelete) {\n        if (!this.#node.references) {\n            throw new Error('on delete constraint can only be added for foreign keys');\n        }\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            references: ReferencesNode.cloneWithOnDelete(this.#node.references, parseOnModifyForeignAction(onDelete)),\n        }));\n    }\n    /**\n     * Adds an `on update` constraint for the foreign key column.\n     *\n     * If your database engine doesn't support foreign key constraints in the\n     * column definition (like MySQL 5) you need to call the table level\n     * {@link CreateTableBuilder.addForeignKeyConstraint} method instead.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn(\n     *     'owner_id',\n     *     'integer',\n     *     (col) => col.references('person.id').onUpdate('cascade')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"pet\" (\n     *   \"owner_id\" integer references \"person\" (\"id\") on update cascade\n     * )\n     * ```\n     */\n    onUpdate(onUpdate) {\n        if (!this.#node.references) {\n            throw new Error('on update constraint can only be added for foreign keys');\n        }\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            references: ReferencesNode.cloneWithOnUpdate(this.#node.references, parseOnModifyForeignAction(onUpdate)),\n        }));\n    }\n    /**\n     * Adds a unique constraint for the column.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('email', 'varchar(255)', col => col.unique())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `email` varchar(255) unique\n     * )\n     * ```\n     */\n    unique() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { unique: true }));\n    }\n    /**\n     * Adds a `not null` constraint for the column.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('first_name', 'varchar(255)', col => col.notNull())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `first_name` varchar(255) not null\n     * )\n     * ```\n     */\n    notNull() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { notNull: true }));\n    }\n    /**\n     * Adds a `unsigned` modifier for the column.\n     *\n     * This only works on some dialects like MySQL.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('age', 'integer', col => col.unsigned())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `age` integer unsigned\n     * )\n     * ```\n     */\n    unsigned() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { unsigned: true }));\n    }\n    /**\n     * Adds a default value constraint for the column.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('number_of_legs', 'integer', (col) => col.defaultTo(4))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `pet` (\n     *   `number_of_legs` integer default 4\n     * )\n     * ```\n     *\n     * Values passed to `defaultTo` are interpreted as value literals by default. You can define\n     * an arbitrary SQL expression using the {@link sql} template tag:\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn(\n     *     'created_at',\n     *     'timestamp',\n     *     (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`)\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `pet` (\n     *   `created_at` timestamp default CURRENT_TIMESTAMP\n     * )\n     * ```\n     */\n    defaultTo(value) {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            defaultTo: DefaultValueNode.create(parseDefaultValueExpression(value)),\n        }));\n    }\n    /**\n     * Adds a check constraint for the column.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('number_of_legs', 'integer', (col) =>\n     *     col.check(sql`number_of_legs < 5`)\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `pet` (\n     *   `number_of_legs` integer check (number_of_legs < 5)\n     * )\n     * ```\n     */\n    check(expression) {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            check: CheckConstraintNode.create(expression.toOperationNode()),\n        }));\n    }\n    /**\n     * Makes the column a generated column using a `generated always as` statement.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('full_name', 'varchar(255)',\n     *     (col) => col.generatedAlwaysAs(sql`concat(first_name, ' ', last_name)`)\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `full_name` varchar(255) generated always as (concat(first_name, ' ', last_name))\n     * )\n     * ```\n     */\n    generatedAlwaysAs(expression) {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            generated: GeneratedNode.createWithExpression(expression.toOperationNode()),\n        }));\n    }\n    /**\n     * Adds the `generated always as identity` specifier.\n     *\n     * This only works on some dialects like PostgreSQL.\n     *\n     * For MS SQL Server (MSSQL)'s identity column use {@link identity}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.generatedAlwaysAsIdentity().primaryKey())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"person\" (\n     *   \"id\" integer generated always as identity primary key\n     * )\n     * ```\n     */\n    generatedAlwaysAsIdentity() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            generated: GeneratedNode.create({ identity: true, always: true }),\n        }));\n    }\n    /**\n     * Adds the `generated by default as identity` specifier on supported dialects.\n     *\n     * This only works on some dialects like PostgreSQL.\n     *\n     * For MS SQL Server (MSSQL)'s identity column use {@link identity}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.generatedByDefaultAsIdentity().primaryKey())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"person\" (\n     *   \"id\" integer generated by default as identity primary key\n     * )\n     * ```\n     */\n    generatedByDefaultAsIdentity() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            generated: GeneratedNode.create({ identity: true, byDefault: true }),\n        }));\n    }\n    /**\n     * Makes a generated column stored instead of virtual. This method can only\n     * be used with {@link generatedAlwaysAs}\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('full_name', 'varchar(255)', (col) => col\n     *     .generatedAlwaysAs(sql`concat(first_name, ' ', last_name)`)\n     *     .stored()\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `full_name` varchar(255) generated always as (concat(first_name, ' ', last_name)) stored\n     * )\n     * ```\n     */\n    stored() {\n        if (!this.#node.generated) {\n            throw new Error('stored() can only be called after generatedAlwaysAs');\n        }\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, {\n            generated: GeneratedNode.cloneWith(this.#node.generated, {\n                stored: true,\n            }),\n        }));\n    }\n    /**\n     * This can be used to add any additional SQL right after the column's data type.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .addColumn(\n     *     'first_name',\n     *     'varchar(36)',\n     *     (col) => col.modifyFront(sql`collate utf8mb4_general_ci`).notNull()\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `id` integer primary key,\n     *   `first_name` varchar(36) collate utf8mb4_general_ci not null\n     * )\n     * ```\n     */\n    modifyFront(modifier) {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWithFrontModifier(this.#node, modifier.toOperationNode()));\n    }\n    /**\n     * Adds `nulls not distinct` specifier.\n     * Should be used with `unique` constraint.\n     *\n     * This only works on some dialects like PostgreSQL.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .addColumn('first_name', 'varchar(30)', col => col.unique().nullsNotDistinct())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create table \"person\" (\n     *   \"id\" integer primary key,\n     *   \"first_name\" varchar(30) unique nulls not distinct\n     * )\n     * ```\n     */\n    nullsNotDistinct() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { nullsNotDistinct: true }));\n    }\n    /**\n     * Adds `if not exists` specifier. This only works for PostgreSQL.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .alterTable('person')\n     *   .addColumn('email', 'varchar(255)', col => col.unique().ifNotExists())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * alter table \"person\" add column if not exists \"email\" varchar(255) unique\n     * ```\n     */\n    ifNotExists() {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWith(this.#node, { ifNotExists: true }));\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the column definition.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .addColumn(\n     *     'age',\n     *     'integer',\n     *     col => col.unsigned()\n     *       .notNull()\n     *       .modifyEnd(sql`comment ${sql.lit('it is not polite to ask a woman her age')}`)\n     *   )\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `id` integer primary key,\n     *   `age` integer unsigned not null comment 'it is not polite to ask a woman her age'\n     * )\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new ColumnDefinitionBuilder(ColumnDefinitionNode.cloneWithEndModifier(this.#node, modifier.toOperationNode()));\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,gBAAgB;QACZ,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,eAAe;QAAK;IACxG;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,WAAW;QACP,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,UAAU;QAAK;IACnG;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,aAAa;QACT,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAK;IACrG;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,WAAW,GAAG,EAAE;QACZ,MAAM,aAAa,CAAA,GAAA,4NAAA,CAAA,uBAAoB,AAAD,EAAE;QACxC,IAAI,CAAC,WAAW,KAAK,IAAI,yOAAA,CAAA,gBAAa,CAAC,EAAE,CAAC,WAAW,MAAM,GAAG;YAC1D,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,IAAI,sEAAsE,CAAC;QAC3H;QACA,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,YAAY,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,WAAW,KAAK,EAAE;gBAChD,WAAW,MAAM;aACpB;QACL;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,UAAU,EAAE;YACxB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,YAAY,sOAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,UAAU,EAAE,CAAA,GAAA,yOAAA,CAAA,6BAA0B,AAAD,EAAE;QACnG;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,UAAU,EAAE;YACxB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,YAAY,sOAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,UAAU,EAAE,CAAA,GAAA,yOAAA,CAAA,6BAA0B,AAAD,EAAE;QACnG;IACJ;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,SAAS;QACL,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,QAAQ;QAAK;IACjG;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,UAAU;QACN,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,SAAS;QAAK;IAClG;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,WAAW;QACP,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,UAAU;QAAK;IACnG;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2CC,GACD,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,WAAW,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,CAAA,GAAA,mOAAA,CAAA,8BAA2B,AAAD,EAAE;QACnE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,MAAM,UAAU,EAAE;QACd,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,OAAO,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,WAAW,eAAe;QAChE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,kBAAkB,UAAU,EAAE;QAC1B,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,WAAW,qOAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC,WAAW,eAAe;QAC5E;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,4BAA4B;QACxB,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,WAAW,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAE,UAAU;gBAAM,QAAQ;YAAK;QACnE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;KAuBC,GACD,+BAA+B;QAC3B,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,WAAW,qOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAE,UAAU;gBAAM,WAAW;YAAK;QACtE;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;KAyBC,GACD,SAAS;QACL,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,SAAS,EAAE;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC1E,WAAW,qOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,CAAC,SAAS,EAAE;gBACrD,QAAQ;YACZ;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2BC,GACD,YAAY,QAAQ,EAAE;QAClB,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,eAAe;IACvH;IACA;;;;;;;;;;;;;;;;;;;;;;;;KAwBC,GACD,mBAAmB;QACf,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,kBAAkB;QAAK;IAC3G;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,cAAc;QACV,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,aAAa;QAAK;IACtG;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,wBAAwB,gPAAA,CAAA,uBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,eAAe;IACrH;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/foreign-key-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./foreign-key-constraint-builder.d.ts\" />\nimport { ForeignKeyConstraintNode } from '../operation-node/foreign-key-constraint-node.js';\nimport { parseOnModifyForeignAction } from '../parser/on-modify-action-parser.js';\nexport class ForeignKeyConstraintBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    onDelete(onDelete) {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, {\n            onDelete: parseOnModifyForeignAction(onDelete),\n        }));\n    }\n    onUpdate(onUpdate) {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, {\n            onUpdate: parseOnModifyForeignAction(onUpdate),\n        }));\n    }\n    deferrable() {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, { deferrable: true }));\n    }\n    notDeferrable() {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, { deferrable: false }));\n    }\n    initiallyDeferred() {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: true,\n        }));\n    }\n    initiallyImmediate() {\n        return new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: false,\n        }));\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;AAC/D;AACA;;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,UAAU,CAAA,GAAA,yOAAA,CAAA,6BAA0B,AAAD,EAAE;QACzC;IACJ;IACA,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,UAAU,CAAA,GAAA,yOAAA,CAAA,6BAA0B,AAAD,EAAE;QACzC;IACJ;IACA,aAAa;QACT,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAK;IAC7G;IACA,gBAAgB;QACZ,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAM;IAC9G;IACA,oBAAoB;QAChB,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,mBAAmB;QACvB;IACJ;IACA,qBAAqB;QACjB,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,mBAAmB;QACvB;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-column-builder.js"], "sourcesContent": ["/// <reference types=\"./alter-column-builder.d.ts\" />\nimport { AlterColumnNode } from '../operation-node/alter-column-node.js';\nimport { parseDataTypeExpression, } from '../parser/data-type-parser.js';\nimport { parseDefaultValueExpression, } from '../parser/default-value-parser.js';\nexport class AlterColumnBuilder {\n    #column;\n    constructor(column) {\n        this.#column = column;\n    }\n    setDataType(dataType) {\n        return new AlteredColumnBuilder(AlterColumnNode.create(this.#column, 'dataType', parseDataTypeExpression(dataType)));\n    }\n    setDefault(value) {\n        return new AlteredColumnBuilder(AlterColumnNode.create(this.#column, 'setDefault', parseDefaultValueExpression(value)));\n    }\n    dropDefault() {\n        return new AlteredColumnBuilder(AlterColumnNode.create(this.#column, 'dropDefault', true));\n    }\n    setNotNull() {\n        return new AlteredColumnBuilder(AlterColumnNode.create(this.#column, 'setNotNull', true));\n    }\n    dropNotNull() {\n        return new AlteredColumnBuilder(AlterColumnNode.create(this.#column, 'dropNotNull', true));\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n}\n/**\n * Allows us to force consumers to do exactly one alteration to a column.\n *\n * One cannot do no alterations:\n *\n * ```ts\n * await db.schema\n *   .alterTable('person')\n * //  .execute() // Property 'execute' does not exist on type 'AlteredColumnBuilder'.\n * ```\n *\n * ```ts\n * await db.schema\n *   .alterTable('person')\n * //  .alterColumn('age', (ac) => ac) // Type 'AlterColumnBuilder' is not assignable to type 'AlteredColumnBuilder'.\n * //  .execute()\n * ```\n *\n * One cannot do multiple alterations:\n *\n * ```ts\n * await db.schema\n *   .alterTable('person')\n * //  .alterColumn('age', (ac) => ac.dropNotNull().setNotNull()) // Property 'setNotNull' does not exist on type 'AlteredColumnBuilder'.\n * //  .execute()\n * ```\n *\n * Which would now throw a compilation error, instead of a runtime error.\n */\nexport class AlteredColumnBuilder {\n    #alterColumnNode;\n    constructor(alterColumnNode) {\n        this.#alterColumnNode = alterColumnNode;\n    }\n    toOperationNode() {\n        return this.#alterColumnNode;\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,MAAO,CAAC;IACR,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,CAAA,MAAO,GAAG;IACnB;IACA,YAAY,QAAQ,EAAE;QAClB,OAAO,IAAI,qBAAqB,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;IAC7G;IACA,WAAW,KAAK,EAAE;QACd,OAAO,IAAI,qBAAqB,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,cAAc,CAAA,GAAA,mOAAA,CAAA,8BAA2B,AAAD,EAAE;IACnH;IACA,cAAc;QACV,OAAO,IAAI,qBAAqB,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,eAAe;IACxF;IACA,aAAa;QACT,OAAO,IAAI,qBAAqB,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,cAAc;IACvF;IACA,cAAc;QACV,OAAO,IAAI,qBAAqB,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,EAAE,eAAe;IACxF;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;AACJ;AA8BO,MAAM;IACT,CAAA,eAAgB,CAAC;IACjB,YAAY,eAAe,CAAE;QACzB,IAAI,CAAC,CAAA,eAAgB,GAAG;IAC5B;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,eAAgB;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-table-executor.js"], "sourcesContent": ["/// <reference types=\"./alter-table-executor.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nexport class AlterTableExecutor {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-table-add-foreign-key-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./alter-table-add-foreign-key-constraint-builder.d.ts\" />\nimport { AddConstraintNode } from '../operation-node/add-constraint-node.js';\nimport { AlterTableNode } from '../operation-node/alter-table-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class AlterTableAddForeignKeyConstraintBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    onDelete(onDelete) {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.onDelete(onDelete),\n        });\n    }\n    onUpdate(onUpdate) {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.onUpdate(onUpdate),\n        });\n    }\n    deferrable() {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.deferrable(),\n        });\n    }\n    notDeferrable() {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.notDeferrable(),\n        });\n    }\n    initiallyDeferred() {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.initiallyDeferred(),\n        });\n    }\n    initiallyImmediate() {\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder: this.#props.constraintBuilder.initiallyImmediate(),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(AlterTableNode.cloneWithTableProps(this.#props.node, {\n            addConstraint: AddConstraintNode.create(this.#props.constraintBuilder.toOperationNode()),\n        }), this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,+EAA+E;;;;AAC/E;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QAC9D;IACJ;IACA,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;QAC9D;IACJ;IACA,aAAa;QACT,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,UAAU;QAC/D;IACJ;IACA,gBAAgB;QACZ,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,aAAa;QAClE;IACJ;IACA,oBAAoB;QAChB,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,iBAAiB;QACtE;IACJ;IACA,qBAAqB;QACjB,OAAO,IAAI,yCAAyC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,mBAAmB,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,kBAAkB;QACvE;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;YAC5F,eAAe,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,iBAAiB,CAAC,eAAe;QACzF,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC3B;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-table-drop-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./alter-table-drop-constraint-builder.d.ts\" />\nimport { AlterTableNode } from '../operation-node/alter-table-node.js';\nimport { DropConstraintNode } from '../operation-node/drop-constraint-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class AlterTableDropConstraintBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    ifExists() {\n        return new AlterTableDropConstraintBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                dropConstraint: DropConstraintNode.cloneWith(this.#props.node.dropConstraint, {\n                    ifExists: true,\n                }),\n            }),\n        });\n    }\n    cascade() {\n        return new AlterTableDropConstraintBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                dropConstraint: DropConstraintNode.cloneWith(this.#props.node.dropConstraint, {\n                    modifier: 'cascade',\n                }),\n            }),\n        });\n    }\n    restrict() {\n        return new AlterTableDropConstraintBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                dropConstraint: DropConstraintNode.cloneWith(this.#props.node.dropConstraint, {\n                    modifier: 'restrict',\n                }),\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;AACpE;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,WAAW;QACP,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,gBAAgB,8OAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1E,UAAU;gBACd;YACJ;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,gBAAgB,8OAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1E,UAAU;gBACd;YACJ;QACJ;IACJ;IACA,WAAW;QACP,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,gBAAgB,8OAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,cAAc,EAAE;oBAC1E,UAAU;gBACd;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-table-add-index-builder.js"], "sourcesContent": ["/// <reference types=\"./alter-table-add-index-builder.d.ts\" />\nimport { AddIndexNode } from '../operation-node/add-index-node.js';\nimport { AlterTableNode } from '../operation-node/alter-table-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { parseOrderedColumnName, } from '../parser/reference-parser.js';\nimport { freeze } from '../util/object-utils.js';\nexport class AlterTableAddIndexBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Makes the index unique.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .alterTable('person')\n     *   .addIndex('person_first_name_index')\n     *   .unique()\n     *   .column('email')\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` add unique index `person_first_name_index` (`email`)\n     * ```\n     */\n    unique() {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.cloneWith(this.#props.node.addIndex, {\n                    unique: true,\n                }),\n            }),\n        });\n    }\n    /**\n     * Adds a column to the index.\n     *\n     * Also see {@link columns} for adding multiple columns at once or {@link expression}\n     * for specifying an arbitrary expression.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .alterTable('person')\n     *   .addIndex('person_first_name_and_age_index')\n     *   .column('first_name')\n     *   .column('age desc')\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` add index `person_first_name_and_age_index` (`first_name`, `age` desc)\n     * ```\n     */\n    column(column) {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.cloneWithColumns(this.#props.node.addIndex, [\n                    parseOrderedColumnName(column),\n                ]),\n            }),\n        });\n    }\n    /**\n     * Specifies a list of columns for the index.\n     *\n     * Also see {@link column} for adding a single column or {@link expression} for\n     * specifying an arbitrary expression.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .alterTable('person')\n     *   .addIndex('person_first_name_and_age_index')\n     *   .columns(['first_name', 'age desc'])\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` add index `person_first_name_and_age_index` (`first_name`, `age` desc)\n     * ```\n     */\n    columns(columns) {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.cloneWithColumns(this.#props.node.addIndex, columns.map(parseOrderedColumnName)),\n            }),\n        });\n    }\n    /**\n     * Specifies an arbitrary expression for the index.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .alterTable('person')\n     *   .addIndex('person_first_name_index')\n     *   .expression(sql<boolean>`(first_name < 'Sami')`)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` add index `person_first_name_index` ((first_name < 'Sami'))\n     * ```\n     */\n    expression(expression) {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.cloneWithColumns(this.#props.node.addIndex, [\n                    expression.toOperationNode(),\n                ]),\n            }),\n        });\n    }\n    using(indexType) {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.cloneWith(this.#props.node.addIndex, {\n                    using: RawNode.createWithSql(indexType),\n                }),\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAC9D;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,SAAS;QACL,OAAO,IAAI,0BAA0B;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACxD,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,0BAA0B;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC/D,CAAA,GAAA,4NAAA,CAAA,yBAAsB,AAAD,EAAE;iBAC1B;YACL;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,QAAQ,OAAO,EAAE;QACb,OAAO,IAAI,0BAA0B;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,4NAAA,CAAA,yBAAsB;YACzG;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,WAAW,UAAU,EAAE;QACnB,OAAO,IAAI,0BAA0B;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBAC/D,WAAW,eAAe;iBAC7B;YACL;QACJ;IACJ;IACA,MAAM,SAAS,EAAE;QACb,OAAO,IAAI,0BAA0B;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACxD,OAAO,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBACjC;YACJ;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/unique-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./unique-constraint-builder.d.ts\" />\nimport { UniqueConstraintNode } from '../operation-node/unique-constraint-node.js';\nexport class UniqueConstraintNodeBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /**\n     * Adds `nulls not distinct` to the unique constraint definition\n     *\n     * Supported by PostgreSQL dialect only\n     */\n    nullsNotDistinct() {\n        return new UniqueConstraintNodeBuilder(UniqueConstraintNode.cloneWith(this.#node, { nullsNotDistinct: true }));\n    }\n    deferrable() {\n        return new UniqueConstraintNodeBuilder(UniqueConstraintNode.cloneWith(this.#node, { deferrable: true }));\n    }\n    notDeferrable() {\n        return new UniqueConstraintNodeBuilder(UniqueConstraintNode.cloneWith(this.#node, { deferrable: false }));\n    }\n    initiallyDeferred() {\n        return new UniqueConstraintNodeBuilder(UniqueConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: true,\n        }));\n    }\n    initiallyImmediate() {\n        return new UniqueConstraintNodeBuilder(UniqueConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: false,\n        }));\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;AAC1D;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA;;;;KAIC,GACD,mBAAmB;QACf,OAAO,IAAI,4BAA4B,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,kBAAkB;QAAK;IAC/G;IACA,aAAa;QACT,OAAO,IAAI,4BAA4B,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAK;IACzG;IACA,gBAAgB;QACZ,OAAO,IAAI,4BAA4B,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAM;IAC1G;IACA,oBAAoB;QAChB,OAAO,IAAI,4BAA4B,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC9E,mBAAmB;QACvB;IACJ;IACA,qBAAqB;QACjB,OAAO,IAAI,4BAA4B,gPAAA,CAAA,uBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAC9E,mBAAmB;QACvB;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/primary-key-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./primary-key-constraint-builder.d.ts\" />\nimport { PrimaryKeyConstraintNode } from '../operation-node/primary-key-constraint-node.js';\nexport class PrimaryKeyConstraintBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    deferrable() {\n        return new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.cloneWith(this.#node, { deferrable: true }));\n    }\n    notDeferrable() {\n        return new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.cloneWith(this.#node, { deferrable: false }));\n    }\n    initiallyDeferred() {\n        return new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: true,\n        }));\n    }\n    initiallyImmediate() {\n        return new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.cloneWith(this.#node, {\n            initiallyDeferred: false,\n        }));\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;AAC/D;;AACO,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA,aAAa;QACT,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAK;IAC7G;IACA,gBAAgB;QACZ,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAAE,YAAY;QAAM;IAC9G;IACA,oBAAoB;QAChB,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,mBAAmB;QACvB;IACJ;IACA,qBAAqB;QACjB,OAAO,IAAI,4BAA4B,wPAAA,CAAA,2BAAwB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE;YAClF,mBAAmB;QACvB;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/check-constraint-builder.js"], "sourcesContent": ["/// <reference types=\"./check-constraint-builder.d.ts\" />\nexport class CheckConstraintBuilder {\n    #node;\n    constructor(node) {\n        this.#node = node;\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#node;\n    }\n}\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;AAClD,MAAM;IACT,CAAA,IAAK,CAAC;IACN,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,CAAA,IAAK,GAAG;IACjB;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,IAAK;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/alter-table-builder.js"], "sourcesContent": ["/// <reference types=\"./alter-table-builder.d.ts\" />\nimport { AddColumnNode } from '../operation-node/add-column-node.js';\nimport { AlterTableNode } from '../operation-node/alter-table-node.js';\nimport { ColumnDefinitionNode } from '../operation-node/column-definition-node.js';\nimport { DropColumnNode } from '../operation-node/drop-column-node.js';\nimport { IdentifierNode } from '../operation-node/identifier-node.js';\nimport { RenameColumnNode } from '../operation-node/rename-column-node.js';\nimport { freeze, noop } from '../util/object-utils.js';\nimport { ColumnDefinitionBuilder, } from './column-definition-builder.js';\nimport { ModifyColumnNode } from '../operation-node/modify-column-node.js';\nimport { parseDataTypeExpression, } from '../parser/data-type-parser.js';\nimport { ForeignKeyConstraintBuilder, } from './foreign-key-constraint-builder.js';\nimport { AddConstraintNode } from '../operation-node/add-constraint-node.js';\nimport { UniqueConstraintNode } from '../operation-node/unique-constraint-node.js';\nimport { CheckConstraintNode } from '../operation-node/check-constraint-node.js';\nimport { ForeignKeyConstraintNode } from '../operation-node/foreign-key-constraint-node.js';\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { DropConstraintNode } from '../operation-node/drop-constraint-node.js';\nimport { AlterColumnBuilder, } from './alter-column-builder.js';\nimport { AlterTableExecutor } from './alter-table-executor.js';\nimport { AlterTableAddForeignKeyConstraintBuilder } from './alter-table-add-foreign-key-constraint-builder.js';\nimport { AlterTableDropConstraintBuilder } from './alter-table-drop-constraint-builder.js';\nimport { PrimaryKeyConstraintNode } from '../operation-node/primary-key-constraint-node.js';\nimport { DropIndexNode } from '../operation-node/drop-index-node.js';\nimport { AddIndexNode } from '../operation-node/add-index-node.js';\nimport { AlterTableAddIndexBuilder } from './alter-table-add-index-builder.js';\nimport { UniqueConstraintNodeBuilder, } from './unique-constraint-builder.js';\nimport { PrimaryKeyConstraintBuilder, } from './primary-key-constraint-builder.js';\nimport { CheckConstraintBuilder, } from './check-constraint-builder.js';\nimport { RenameConstraintNode } from '../operation-node/rename-constraint-node.js';\n/**\n * This builder can be used to create a `alter table` query.\n */\nexport class AlterTableBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    renameTo(newTableName) {\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                renameTo: parseTable(newTableName),\n            }),\n        });\n    }\n    setSchema(newSchema) {\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                setSchema: IdentifierNode.create(newSchema),\n            }),\n        });\n    }\n    alterColumn(column, alteration) {\n        const builder = alteration(new AlterColumnBuilder(column));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, builder.toOperationNode()),\n        });\n    }\n    dropColumn(column) {\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, DropColumnNode.create(column)),\n        });\n    }\n    renameColumn(column, newColumn) {\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, RenameColumnNode.create(column, newColumn)),\n        });\n    }\n    addColumn(columnName, dataType, build = noop) {\n        const builder = build(new ColumnDefinitionBuilder(ColumnDefinitionNode.create(columnName, parseDataTypeExpression(dataType))));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, AddColumnNode.create(builder.toOperationNode())),\n        });\n    }\n    modifyColumn(columnName, dataType, build = noop) {\n        const builder = build(new ColumnDefinitionBuilder(ColumnDefinitionNode.create(columnName, parseDataTypeExpression(dataType))));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, ModifyColumnNode.create(builder.toOperationNode())),\n        });\n    }\n    /**\n     * See {@link CreateTableBuilder.addUniqueConstraint}\n     */\n    addUniqueConstraint(constraintName, columns, build = noop) {\n        const uniqueConstraintBuilder = build(new UniqueConstraintNodeBuilder(UniqueConstraintNode.create(columns, constraintName)));\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addConstraint: AddConstraintNode.create(uniqueConstraintBuilder.toOperationNode()),\n            }),\n        });\n    }\n    /**\n     * See {@link CreateTableBuilder.addCheckConstraint}\n     */\n    addCheckConstraint(constraintName, checkExpression, build = noop) {\n        const constraintBuilder = build(new CheckConstraintBuilder(CheckConstraintNode.create(checkExpression.toOperationNode(), constraintName)));\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addConstraint: AddConstraintNode.create(constraintBuilder.toOperationNode()),\n            }),\n        });\n    }\n    /**\n     * See {@link CreateTableBuilder.addForeignKeyConstraint}\n     *\n     * Unlike {@link CreateTableBuilder.addForeignKeyConstraint} this method returns\n     * the constraint builder and doesn't take a callback as the last argument. This\n     * is because you can only add one column per `ALTER TABLE` query.\n     */\n    addForeignKeyConstraint(constraintName, columns, targetTable, targetColumns, build = noop) {\n        const constraintBuilder = build(new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.create(columns.map(ColumnNode.create), parseTable(targetTable), targetColumns.map(ColumnNode.create), constraintName)));\n        return new AlterTableAddForeignKeyConstraintBuilder({\n            ...this.#props,\n            constraintBuilder,\n        });\n    }\n    /**\n     * See {@link CreateTableBuilder.addPrimaryKeyConstraint}\n     */\n    addPrimaryKeyConstraint(constraintName, columns, build = noop) {\n        const constraintBuilder = build(new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.create(columns, constraintName)));\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addConstraint: AddConstraintNode.create(constraintBuilder.toOperationNode()),\n            }),\n        });\n    }\n    dropConstraint(constraintName) {\n        return new AlterTableDropConstraintBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                dropConstraint: DropConstraintNode.create(constraintName),\n            }),\n        });\n    }\n    renameConstraint(oldName, newName) {\n        return new AlterTableDropConstraintBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                renameConstraint: RenameConstraintNode.create(oldName, newName),\n            }),\n        });\n    }\n    /**\n     * This can be used to add index to table.\n     *\n     *  ### Examples\n     *\n     * ```ts\n     * db.schema.alterTable('person')\n     *   .addIndex('person_email_index')\n     *   .column('email')\n     *   .unique()\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` add unique index `person_email_index` (`email`)\n     * ```\n     */\n    addIndex(indexName) {\n        return new AlterTableAddIndexBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                addIndex: AddIndexNode.create(indexName),\n            }),\n        });\n    }\n    /**\n     * This can be used to drop index from table.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.schema.alterTable('person')\n     *   .dropIndex('person_email_index')\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * alter table `person` drop index `test_first_name_index`\n     * ```\n     */\n    dropIndex(indexName) {\n        return new AlterTableExecutor({\n            ...this.#props,\n            node: AlterTableNode.cloneWithTableProps(this.#props.node, {\n                dropIndex: DropIndexNode.create(indexName),\n            }),\n        });\n    }\n    /**\n     * Calls the given function passing `this` as the only argument.\n     *\n     * See {@link CreateTableBuilder.$call}\n     */\n    $call(func) {\n        return func(this);\n    }\n}\nexport class AlterTableColumnAlteringBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    alterColumn(column, alteration) {\n        const builder = alteration(new AlterColumnBuilder(column));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, builder.toOperationNode()),\n        });\n    }\n    dropColumn(column) {\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, DropColumnNode.create(column)),\n        });\n    }\n    renameColumn(column, newColumn) {\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, RenameColumnNode.create(column, newColumn)),\n        });\n    }\n    addColumn(columnName, dataType, build = noop) {\n        const builder = build(new ColumnDefinitionBuilder(ColumnDefinitionNode.create(columnName, parseDataTypeExpression(dataType))));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, AddColumnNode.create(builder.toOperationNode())),\n        });\n    }\n    modifyColumn(columnName, dataType, build = noop) {\n        const builder = build(new ColumnDefinitionBuilder(ColumnDefinitionNode.create(columnName, parseDataTypeExpression(dataType))));\n        return new AlterTableColumnAlteringBuilder({\n            ...this.#props,\n            node: AlterTableNode.cloneWithColumnAlteration(this.#props.node, ModifyColumnNode.create(builder.toOperationNode())),\n        });\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,SAAS,YAAY,EAAE;QACnB,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;YACzB;QACJ;IACJ;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,WAAW,sOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;YACrC;QACJ;IACJ;IACA,YAAY,MAAM,EAAE,UAAU,EAAE;QAC5B,MAAM,UAAU,WAAW,IAAI,mOAAA,CAAA,qBAAkB,CAAC;QAClD,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,QAAQ,eAAe;QAC5F;IACJ;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAC3F;IACJ;IACA,aAAa,MAAM,EAAE,SAAS,EAAE;QAC5B,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,QAAQ;QACrG;IACJ;IACA,UAAU,UAAU,EAAE,QAAQ,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC1C,MAAM,UAAU,MAAM,IAAI,wOAAA,CAAA,0BAAuB,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QAClH,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,QAAQ,eAAe;QACjH;IACJ;IACA,aAAa,UAAU,EAAE,QAAQ,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC7C,MAAM,UAAU,MAAM,IAAI,wOAAA,CAAA,0BAAuB,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QAClH,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,QAAQ,eAAe;QACpH;IACJ;IACA;;KAEC,GACD,oBAAoB,cAAc,EAAE,OAAO,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QACvD,MAAM,0BAA0B,MAAM,IAAI,wOAAA,CAAA,8BAA2B,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,SAAS;QAC3G,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,eAAe,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,wBAAwB,eAAe;YACnF;QACJ;IACJ;IACA;;KAEC,GACD,mBAAmB,cAAc,EAAE,eAAe,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC9D,MAAM,oBAAoB,MAAM,IAAI,uOAAA,CAAA,yBAAsB,CAAC,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,gBAAgB,eAAe,IAAI;QACzH,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,eAAe,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,kBAAkB,eAAe;YAC7E;QACJ;IACJ;IACA;;;;;;KAMC,GACD,wBAAwB,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QACvF,MAAM,oBAAoB,MAAM,IAAI,gPAAA,CAAA,8BAA2B,CAAC,wPAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,GAAG,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,cAAc,cAAc,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,GAAG;QAC/L,OAAO,IAAI,yQAAA,CAAA,2CAAwC,CAAC;YAChD,GAAG,IAAI,CAAC,CAAA,KAAM;YACd;QACJ;IACJ;IACA;;KAEC,GACD,wBAAwB,cAAc,EAAE,OAAO,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC3D,MAAM,oBAAoB,MAAM,IAAI,gPAAA,CAAA,8BAA2B,CAAC,wPAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC,SAAS;QACzG,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,eAAe,6OAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,kBAAkB,eAAe;YAC7E;QACJ;IACJ;IACA,eAAe,cAAc,EAAE;QAC3B,OAAO,IAAI,wPAAA,CAAA,kCAA+B,CAAC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,gBAAgB,8OAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;YAC9C;QACJ;IACJ;IACA,iBAAiB,OAAO,EAAE,OAAO,EAAE;QAC/B,OAAO,IAAI,wPAAA,CAAA,kCAA+B,CAAC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,kBAAkB,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,SAAS;YAC3D;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;KAkBC,GACD,SAAS,SAAS,EAAE;QAChB,OAAO,IAAI,kPAAA,CAAA,4BAAyB,CAAC;YACjC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,UAAU,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;YAClC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACvD,WAAW,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YACpC;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;AACJ;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,YAAY,MAAM,EAAE,UAAU,EAAE;QAC5B,MAAM,UAAU,WAAW,IAAI,mOAAA,CAAA,qBAAkB,CAAC;QAClD,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,QAAQ,eAAe;QAC5F;IACJ;IACA,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAC3F;IACJ;IACA,aAAa,MAAM,EAAE,SAAS,EAAE;QAC5B,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,QAAQ;QACrG;IACJ;IACA,UAAU,UAAU,EAAE,QAAQ,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC1C,MAAM,UAAU,MAAM,IAAI,wOAAA,CAAA,0BAAuB,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QAClH,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,QAAQ,eAAe;QACjH;IACJ;IACA,aAAa,UAAU,EAAE,QAAQ,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC7C,MAAM,UAAU,MAAM,IAAI,wOAAA,CAAA,0BAAuB,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QAClH,OAAO,IAAI,gCAAgC;YACvC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,QAAQ,eAAe;QACpH;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/create-index-builder.js"], "sourcesContent": ["/// <reference types=\"./create-index-builder.d.ts\" />\nimport { CreateIndexNode, } from '../operation-node/create-index-node.js';\nimport { RawNode } from '../operation-node/raw-node.js';\nimport { parseOrderedColumnName, } from '../parser/reference-parser.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { freeze } from '../util/object-utils.js';\nimport { parseValueBinaryOperationOrExpression, } from '../parser/binary-operation-parser.js';\nimport { QueryNode } from '../operation-node/query-node.js';\nimport { ImmediateValueTransformer } from '../plugin/immediate-value/immediate-value-transformer.js';\nexport class CreateIndexBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Adds the \"if not exists\" modifier.\n     *\n     * If the index already exists, no error is thrown if this method has been called.\n     */\n    ifNotExists() {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWith(this.#props.node, {\n                ifNotExists: true,\n            }),\n        });\n    }\n    /**\n     * Makes the index unique.\n     */\n    unique() {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWith(this.#props.node, {\n                unique: true,\n            }),\n        });\n    }\n    /**\n     * Adds `nulls not distinct` specifier to index.\n     * This only works on some dialects like PostgreSQL.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.schema.createIndex('person_first_name_index')\n     *  .on('person')\n     *  .column('first_name')\n     *  .nullsNotDistinct()\n     *  .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create index \"person_first_name_index\"\n     * on \"test\" (\"first_name\")\n     * nulls not distinct;\n     * ```\n     */\n    nullsNotDistinct() {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWith(this.#props.node, {\n                nullsNotDistinct: true,\n            }),\n        });\n    }\n    /**\n     * Specifies the table for the index.\n     */\n    on(table) {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWith(this.#props.node, {\n                table: parseTable(table),\n            }),\n        });\n    }\n    /**\n     * Adds a column to the index.\n     *\n     * Also see {@link columns} for adding multiple columns at once or {@link expression}\n     * for specifying an arbitrary expression.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *         .createIndex('person_first_name_and_age_index')\n     *         .on('person')\n     *         .column('first_name')\n     *         .column('age desc')\n     *         .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create index \"person_first_name_and_age_index\" on \"person\" (\"first_name\", \"age\" desc)\n     * ```\n     */\n    column(column) {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWithColumns(this.#props.node, [\n                parseOrderedColumnName(column),\n            ]),\n        });\n    }\n    /**\n     * Specifies a list of columns for the index.\n     *\n     * Also see {@link column} for adding a single column or {@link expression} for\n     * specifying an arbitrary expression.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *         .createIndex('person_first_name_and_age_index')\n     *         .on('person')\n     *         .columns(['first_name', 'age desc'])\n     *         .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create index \"person_first_name_and_age_index\" on \"person\" (\"first_name\", \"age\" desc)\n     * ```\n     */\n    columns(columns) {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWithColumns(this.#props.node, columns.map(parseOrderedColumnName)),\n        });\n    }\n    /**\n     * Specifies an arbitrary expression for the index.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createIndex('person_first_name_index')\n     *   .on('person')\n     *   .expression(sql`first_name COLLATE \"fi_FI\"`)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create index \"person_first_name_index\" on \"person\" (first_name COLLATE \"fi_FI\")\n     * ```\n     */\n    expression(expression) {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWithColumns(this.#props.node, [\n                expression.toOperationNode(),\n            ]),\n        });\n    }\n    using(indexType) {\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: CreateIndexNode.cloneWith(this.#props.node, {\n                using: RawNode.createWithSql(indexType),\n            }),\n        });\n    }\n    where(...args) {\n        const transformer = new ImmediateValueTransformer();\n        return new CreateIndexBuilder({\n            ...this.#props,\n            node: QueryNode.cloneWithWhere(this.#props.node, transformer.transformNode(parseValueBinaryOperationOrExpression(args), this.#props.queryId)),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;KAIC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,aAAa;YACjB;QACJ;IACJ;IACA;;KAEC,GACD,SAAS;QACL,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,QAAQ;YACZ;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,mBAAmB;QACf,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,kBAAkB;YACtB;QACJ;IACJ;IACA;;KAEC,GACD,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,OAAO,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;YACtB;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACrD,CAAA,GAAA,4NAAA,CAAA,yBAAsB,AAAD,EAAE;aAC1B;QACL;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;KAqBC,GACD,QAAQ,OAAO,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,4NAAA,CAAA,yBAAsB;QAC/F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;KAoBC,GACD,WAAW,UAAU,EAAE;QACnB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBACrD,WAAW,eAAe;aAC7B;QACL;IACJ;IACA,MAAM,SAAS,EAAE;QACb,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,OAAO,+NAAA,CAAA,UAAO,CAAC,aAAa,CAAC;YACjC;QACJ;IACJ;IACA,MAAM,GAAG,IAAI,EAAE;QACX,MAAM,cAAc,IAAI,gQAAA,CAAA,4BAAyB;QACjD,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,iOAAA,CAAA,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,YAAY,aAAa,CAAC,CAAA,GAAA,sOAAA,CAAA,wCAAqC,AAAD,EAAE,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;QAC/I;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/create-schema-builder.js"], "sourcesContent": ["/// <reference types=\"./create-schema-builder.d.ts\" />\nimport { CreateSchemaNode } from '../operation-node/create-schema-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class CreateSchemaBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    ifNotExists() {\n        return new CreateSchemaBuilder({\n            ...this.#props,\n            node: CreateSchemaNode.cloneWith(this.#props.node, { ifNotExists: true }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,cAAc;QACV,OAAO,IAAI,oBAAoB;YAC3B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,4OAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAAE,aAAa;YAAK;QAC3E;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/create-table-builder.js"], "sourcesContent": ["/// <reference types=\"./create-table-builder.d.ts\" />\nimport { ColumnDefinitionNode } from '../operation-node/column-definition-node.js';\nimport { CreateTableNode, } from '../operation-node/create-table-node.js';\nimport { ColumnDefinitionBuilder } from './column-definition-builder.js';\nimport { freeze, noop } from '../util/object-utils.js';\nimport { ForeignKeyConstraintNode } from '../operation-node/foreign-key-constraint-node.js';\nimport { ColumnNode } from '../operation-node/column-node.js';\nimport { ForeignKeyConstraintBuilder, } from './foreign-key-constraint-builder.js';\nimport { parseDataTypeExpression, } from '../parser/data-type-parser.js';\nimport { PrimaryKeyConstraintNode } from '../operation-node/primary-key-constraint-node.js';\nimport { UniqueConstraintNode } from '../operation-node/unique-constraint-node.js';\nimport { CheckConstraintNode } from '../operation-node/check-constraint-node.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { parseOnCommitAction } from '../parser/on-commit-action-parse.js';\nimport { UniqueConstraintNodeBuilder, } from './unique-constraint-builder.js';\nimport { parseExpression } from '../parser/expression-parser.js';\nimport { PrimaryKeyConstraintBuilder, } from './primary-key-constraint-builder.js';\nimport { CheckConstraintBuilder, } from './check-constraint-builder.js';\n/**\n * This builder can be used to create a `create table` query.\n */\nexport class CreateTableBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Adds the \"temporary\" modifier.\n     *\n     * Use this to create a temporary table.\n     */\n    temporary() {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWith(this.#props.node, {\n                temporary: true,\n            }),\n        });\n    }\n    /**\n     * Adds an \"on commit\" statement.\n     *\n     * This can be used in conjunction with temporary tables on supported databases\n     * like PostgreSQL.\n     */\n    onCommit(onCommit) {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWith(this.#props.node, {\n                onCommit: parseOnCommitAction(onCommit),\n            }),\n        });\n    }\n    /**\n     * Adds the \"if not exists\" modifier.\n     *\n     * If the table already exists, no error is thrown if this method has been called.\n     */\n    ifNotExists() {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWith(this.#props.node, {\n                ifNotExists: true,\n            }),\n        });\n    }\n    /**\n     * Adds a column to the table.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', (col) => col.autoIncrement().primaryKey())\n     *   .addColumn('first_name', 'varchar(50)', (col) => col.notNull())\n     *   .addColumn('last_name', 'varchar(255)')\n     *   .addColumn('bank_balance', 'numeric(8, 2)')\n     *   // You can specify any data type using the `sql` tag if the types\n     *   // don't include it.\n     *   .addColumn('data', sql`any_type_here`)\n     *   .addColumn('parent_id', 'integer', (col) =>\n     *     col.references('person.id').onDelete('cascade')\n     *   )\n     * ```\n     *\n     * With this method, it's once again good to remember that Kysely just builds the\n     * query and doesn't provide the same API for all databases. For example, some\n     * databases like older MySQL don't support the `references` statement in the\n     * column definition. Instead foreign key constraints need to be defined in the\n     * `create table` query. See the next example:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', (col) => col.primaryKey())\n     *   .addColumn('parent_id', 'integer')\n     *   .addForeignKeyConstraint(\n     *     'person_parent_id_fk',\n     *     ['parent_id'],\n     *     'person',\n     *     ['id'],\n     *     (cb) => cb.onDelete('cascade')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * Another good example is that PostgreSQL doesn't support the `auto_increment`\n     * keyword and you need to define an autoincrementing column for example using\n     * `serial`:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'serial', (col) => col.primaryKey())\n     *   .execute()\n     * ```\n     */\n    addColumn(columnName, dataType, build = noop) {\n        const columnBuilder = build(new ColumnDefinitionBuilder(ColumnDefinitionNode.create(columnName, parseDataTypeExpression(dataType))));\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithColumn(this.#props.node, columnBuilder.toOperationNode()),\n        });\n    }\n    /**\n     * Adds a primary key constraint for one or more columns.\n     *\n     * The constraint name can be anything you want, but it must be unique\n     * across the whole database.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('first_name', 'varchar(64)')\n     *   .addColumn('last_name', 'varchar(64)')\n     *   .addPrimaryKeyConstraint('primary_key', ['first_name', 'last_name'])\n     *   .execute()\n     * ```\n     */\n    addPrimaryKeyConstraint(constraintName, columns, build = noop) {\n        const constraintBuilder = build(new PrimaryKeyConstraintBuilder(PrimaryKeyConstraintNode.create(columns, constraintName)));\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithConstraint(this.#props.node, constraintBuilder.toOperationNode()),\n        });\n    }\n    /**\n     * Adds a unique constraint for one or more columns.\n     *\n     * The constraint name can be anything you want, but it must be unique\n     * across the whole database.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('first_name', 'varchar(64)')\n     *   .addColumn('last_name', 'varchar(64)')\n     *   .addUniqueConstraint(\n     *     'first_name_last_name_unique',\n     *     ['first_name', 'last_name']\n     *   )\n     *   .execute()\n     * ```\n     *\n     * In dialects such as PostgreSQL you can specify `nulls not distinct` as follows:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('first_name', 'varchar(64)')\n     *   .addColumn('last_name', 'varchar(64)')\n     *   .addUniqueConstraint(\n     *     'first_name_last_name_unique',\n     *     ['first_name', 'last_name'],\n     *     (cb) => cb.nullsNotDistinct()\n     *   )\n     *   .execute()\n     * ```\n     */\n    addUniqueConstraint(constraintName, columns, build = noop) {\n        const uniqueConstraintBuilder = build(new UniqueConstraintNodeBuilder(UniqueConstraintNode.create(columns, constraintName)));\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithConstraint(this.#props.node, uniqueConstraintBuilder.toOperationNode()),\n        });\n    }\n    /**\n     * Adds a check constraint.\n     *\n     * The constraint name can be anything you want, but it must be unique\n     * across the whole database.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('animal')\n     *   .addColumn('number_of_legs', 'integer')\n     *   .addCheckConstraint('check_legs', sql`number_of_legs < 5`)\n     *   .execute()\n     * ```\n     */\n    addCheckConstraint(constraintName, checkExpression, build = noop) {\n        const constraintBuilder = build(new CheckConstraintBuilder(CheckConstraintNode.create(checkExpression.toOperationNode(), constraintName)));\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithConstraint(this.#props.node, constraintBuilder.toOperationNode()),\n        });\n    }\n    /**\n     * Adds a foreign key constraint.\n     *\n     * The constraint name can be anything you want, but it must be unique\n     * across the whole database.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('owner_id', 'integer')\n     *   .addForeignKeyConstraint(\n     *     'owner_id_foreign',\n     *     ['owner_id'],\n     *     'person',\n     *     ['id'],\n     *   )\n     *   .execute()\n     * ```\n     *\n     * Add constraint for multiple columns:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('owner_id1', 'integer')\n     *   .addColumn('owner_id2', 'integer')\n     *   .addForeignKeyConstraint(\n     *     'owner_id_foreign',\n     *     ['owner_id1', 'owner_id2'],\n     *     'person',\n     *     ['id1', 'id2'],\n     *     (cb) => cb.onDelete('cascade')\n     *   )\n     *   .execute()\n     * ```\n     */\n    addForeignKeyConstraint(constraintName, columns, targetTable, targetColumns, build = noop) {\n        const builder = build(new ForeignKeyConstraintBuilder(ForeignKeyConstraintNode.create(columns.map(ColumnNode.create), parseTable(targetTable), targetColumns.map(ColumnNode.create), constraintName)));\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithConstraint(this.#props.node, builder.toOperationNode()),\n        });\n    }\n    /**\n     * This can be used to add any additional SQL to the front of the query __after__ the `create` keyword.\n     *\n     * Also see {@link temporary}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .modifyFront(sql`global temporary`)\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .addColumn('first_name', 'varchar(64)', col => col.notNull())\n     *   .addColumn('last_name', 'varchar(64)', col => col.notNull())\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (Postgres):\n     *\n     * ```sql\n     * create global temporary table \"person\" (\n     *   \"id\" integer primary key,\n     *   \"first_name\" varchar(64) not null,\n     *   \"last_name\" varchar(64) not null\n     * )\n     * ```\n     */\n    modifyFront(modifier) {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithFrontModifier(this.#props.node, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * This can be used to add any additional SQL to the end of the query.\n     *\n     * Also see {@link onCommit}.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * import { sql } from 'kysely'\n     *\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey())\n     *   .addColumn('first_name', 'varchar(64)', col => col.notNull())\n     *   .addColumn('last_name', 'varchar(64)', col => col.notNull())\n     *   .modifyEnd(sql`collate utf8_unicode_ci`)\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (MySQL):\n     *\n     * ```sql\n     * create table `person` (\n     *   `id` integer primary key,\n     *   `first_name` varchar(64) not null,\n     *   `last_name` varchar(64) not null\n     * ) collate utf8_unicode_ci\n     * ```\n     */\n    modifyEnd(modifier) {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWithEndModifier(this.#props.node, modifier.toOperationNode()),\n        });\n    }\n    /**\n     * Allows to create table from `select` query.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('copy')\n     *   .temporary()\n     *   .as(db.selectFrom('person').select(['first_name', 'last_name']))\n     *   .execute()\n     * ```\n     *\n     * The generated SQL (PostgreSQL):\n     *\n     * ```sql\n     * create temporary table \"copy\" as\n     * select \"first_name\", \"last_name\" from \"person\"\n     * ```\n     */\n    as(expression) {\n        return new CreateTableBuilder({\n            ...this.#props,\n            node: CreateTableNode.cloneWith(this.#props.node, {\n                selectQuery: parseExpression(expression),\n            }),\n        });\n    }\n    /**\n     * Calls the given function passing `this` as the only argument.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('test')\n     *   .$call((builder) => builder.addColumn('id', 'integer'))\n     *   .execute()\n     * ```\n     *\n     * This is useful for creating reusable functions that can be called with a builder.\n     *\n     * ```ts\n     * import { type CreateTableBuilder, sql } from 'kysely'\n     *\n     * const addDefaultColumns = (ctb: CreateTableBuilder<any, any>) => {\n     *   return ctb\n     *     .addColumn('id', 'integer', (col) => col.notNull())\n     *     .addColumn('created_at', 'date', (col) =>\n     *       col.notNull().defaultTo(sql`now()`)\n     *     )\n     *     .addColumn('updated_at', 'date', (col) =>\n     *       col.notNull().defaultTo(sql`now()`)\n     *     )\n     * }\n     *\n     * await db.schema\n     *   .createTable('test')\n     *   .$call(addDefaultColumns)\n     *   .execute()\n     * ```\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAIO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;KAIC,GACD,YAAY;QACR,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,WAAW;YACf;QACJ;IACJ;IACA;;;;;KAKC,GACD,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,UAAU,CAAA,GAAA,wOAAA,CAAA,sBAAmB,AAAD,EAAE;YAClC;QACJ;IACJ;IACA;;;;KAIC,GACD,cAAc;QACV,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,aAAa;YACjB;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqDC,GACD,UAAU,UAAU,EAAE,QAAQ,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC1C,MAAM,gBAAgB,MAAM,IAAI,wOAAA,CAAA,0BAAuB,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,YAAY,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE;QACxH,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,cAAc,eAAe;QACzF;IACJ;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,wBAAwB,cAAc,EAAE,OAAO,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC3D,MAAM,oBAAoB,MAAM,IAAI,gPAAA,CAAA,8BAA2B,CAAC,wPAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC,SAAS;QACzG,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,kBAAkB,eAAe;QACjG;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkCC,GACD,oBAAoB,cAAc,EAAE,OAAO,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QACvD,MAAM,0BAA0B,MAAM,IAAI,wOAAA,CAAA,8BAA2B,CAAC,gPAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,SAAS;QAC3G,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,wBAAwB,eAAe;QACvG;IACJ;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,mBAAmB,cAAc,EAAE,eAAe,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QAC9D,MAAM,oBAAoB,MAAM,IAAI,uOAAA,CAAA,yBAAsB,CAAC,+OAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,gBAAgB,eAAe,IAAI;QACzH,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,kBAAkB,eAAe;QACjG;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqCC,GACD,wBAAwB,cAAc,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,sNAAA,CAAA,OAAI,EAAE;QACvF,MAAM,UAAU,MAAM,IAAI,gPAAA,CAAA,8BAA2B,CAAC,wPAAA,CAAA,2BAAwB,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,GAAG,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,cAAc,cAAc,GAAG,CAAC,kOAAA,CAAA,aAAU,CAAC,MAAM,GAAG;QACrL,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,QAAQ,eAAe;QACvF;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BC,GACD,YAAY,QAAQ,EAAE;QAClB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,SAAS,eAAe;QAC3F;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4BC,GACD,UAAU,QAAQ,EAAE;QAChB,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,SAAS,eAAe;QACzF;IACJ;IACA;;;;;;;;;;;;;;;;;;;KAmBC,GACD,GAAG,UAAU,EAAE;QACX,OAAO,IAAI,mBAAmB;YAC1B,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2OAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC9C,aAAa,CAAA,GAAA,6NAAA,CAAA,kBAAe,AAAD,EAAE;YACjC;QACJ;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiCC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/drop-index-builder.js"], "sourcesContent": ["/// <reference types=\"./drop-index-builder.d.ts\" />\nimport { DropIndexNode } from '../operation-node/drop-index-node.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { freeze } from '../util/object-utils.js';\nexport class DropIndexBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Specifies the table the index was created for. This is not needed\n     * in all dialects.\n     */\n    on(table) {\n        return new DropIndexBuilder({\n            ...this.#props,\n            node: DropIndexNode.cloneWith(this.#props.node, {\n                table: parseTable(table),\n            }),\n        });\n    }\n    ifExists() {\n        return new DropIndexBuilder({\n            ...this.#props,\n            node: DropIndexNode.cloneWith(this.#props.node, {\n                ifExists: true,\n            }),\n        });\n    }\n    cascade() {\n        return new DropIndexBuilder({\n            ...this.#props,\n            node: DropIndexNode.cloneWith(this.#props.node, {\n                cascade: true,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;AACA;AACA;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;KAGC,GACD,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,iBAAiB;YACxB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC5C,OAAO,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;YACtB;QACJ;IACJ;IACA,WAAW;QACP,OAAO,IAAI,iBAAiB;YACxB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC5C,UAAU;YACd;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,iBAAiB;YACxB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC5C,SAAS;YACb;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2231, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/drop-schema-builder.js"], "sourcesContent": ["/// <reference types=\"./drop-schema-builder.d.ts\" />\nimport { DropSchemaNode } from '../operation-node/drop-schema-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class DropSchemaBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    ifExists() {\n        return new DropSchemaBuilder({\n            ...this.#props,\n            node: DropSchemaNode.cloneWith(this.#props.node, {\n                ifExists: true,\n            }),\n        });\n    }\n    cascade() {\n        return new DropSchemaBuilder({\n            ...this.#props,\n            node: DropSchemaNode.cloneWith(this.#props.node, {\n                cascade: true,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,WAAW;QACP,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,UAAU;YACd;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,SAAS;YACb;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/drop-table-builder.js"], "sourcesContent": ["/// <reference types=\"./drop-table-builder.d.ts\" />\nimport { DropTableNode } from '../operation-node/drop-table-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class DropTableBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    ifExists() {\n        return new DropTableBuilder({\n            ...this.#props,\n            node: DropTableNode.cloneWith(this.#props.node, {\n                ifExists: true,\n            }),\n        });\n    }\n    cascade() {\n        return new DropTableBuilder({\n            ...this.#props,\n            node: DropTableNode.cloneWith(this.#props.node, {\n                cascade: true,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,mDAAmD;;;;AACnD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,WAAW;QACP,OAAO,IAAI,iBAAiB;YACxB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC5C,UAAU;YACd;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,iBAAiB;YACxB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,yOAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC5C,SAAS;YACb;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/create-view-builder.js"], "sourcesContent": ["/// <reference types=\"./create-view-builder.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { CreateViewNode } from '../operation-node/create-view-node.js';\nimport { parseColumnName } from '../parser/reference-parser.js';\nimport { ImmediateValuePlugin } from '../plugin/immediate-value/immediate-value-plugin.js';\nexport class CreateViewBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Adds the \"temporary\" modifier.\n     *\n     * Use this to create a temporary view.\n     */\n    temporary() {\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                temporary: true,\n            }),\n        });\n    }\n    materialized() {\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                materialized: true,\n            }),\n        });\n    }\n    /**\n     * Only implemented on some dialects like SQLite. On most dialects, use {@link orReplace}.\n     */\n    ifNotExists() {\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                ifNotExists: true,\n            }),\n        });\n    }\n    orReplace() {\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                orReplace: true,\n            }),\n        });\n    }\n    columns(columns) {\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                columns: columns.map(parseColumnName),\n            }),\n        });\n    }\n    /**\n     * Sets the select query or a `values` statement that creates the view.\n     *\n     * WARNING!\n     * Some dialects don't support parameterized queries in DDL statements and therefore\n     * the query or raw {@link sql } expression passed here is interpolated into a single\n     * string opening an SQL injection vulnerability. DO NOT pass unchecked user input\n     * into the query or raw expression passed to this method!\n     */\n    as(query) {\n        const queryNode = query\n            .withPlugin(new ImmediateValuePlugin())\n            .toOperationNode();\n        return new CreateViewBuilder({\n            ...this.#props,\n            node: CreateViewNode.cloneWith(this.#props.node, {\n                as: queryNode,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;AACA;AACA;;;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;KAIC,GACD,YAAY;QACR,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,WAAW;YACf;QACJ;IACJ;IACA,eAAe;QACX,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,cAAc;YAClB;QACJ;IACJ;IACA;;KAEC,GACD,cAAc;QACV,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,aAAa;YACjB;QACJ;IACJ;IACA,YAAY;QACR,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,WAAW;YACf;QACJ;IACJ;IACA,QAAQ,OAAO,EAAE;QACb,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,SAAS,QAAQ,GAAG,CAAC,4NAAA,CAAA,kBAAe;YACxC;QACJ;IACJ;IACA;;;;;;;;KAQC,GACD,GAAG,KAAK,EAAE;QACN,MAAM,YAAY,MACb,UAAU,CAAC,IAAI,2PAAA,CAAA,uBAAoB,IACnC,eAAe;QACpB,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC7C,IAAI;YACR;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/drop-view-builder.js"], "sourcesContent": ["/// <reference types=\"./drop-view-builder.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { DropViewNode } from '../operation-node/drop-view-node.js';\nexport class DropViewBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    materialized() {\n        return new DropViewBuilder({\n            ...this.#props,\n            node: DropViewNode.cloneWith(this.#props.node, {\n                materialized: true,\n            }),\n        });\n    }\n    ifExists() {\n        return new DropViewBuilder({\n            ...this.#props,\n            node: DropViewNode.cloneWith(this.#props.node, {\n                ifExists: true,\n            }),\n        });\n    }\n    cascade() {\n        return new DropViewBuilder({\n            ...this.#props,\n            node: DropViewNode.cloneWith(this.#props.node, {\n                cascade: true,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;AAClD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,eAAe;QACX,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC3C,cAAc;YAClB;QACJ;IACJ;IACA,WAAW;QACP,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC3C,UAAU;YACd;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC3C,SAAS;YACb;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/create-type-builder.js"], "sourcesContent": ["/// <reference types=\"./create-type-builder.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { CreateTypeNode } from '../operation-node/create-type-node.js';\nexport class CreateTypeBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    /**\n     * Creates an anum type.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * db.schema.createType('species').asEnum(['cat', 'dog', 'frog'])\n     * ```\n     */\n    asEnum(values) {\n        return new CreateTypeBuilder({\n            ...this.#props,\n            node: CreateTypeNode.cloneWithEnum(this.#props.node, values),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA;;;;;;;;KAQC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,kBAAkB;YACzB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,0OAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;QACzD;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/drop-type-builder.js"], "sourcesContent": ["/// <reference types=\"./drop-type-builder.d.ts\" />\nimport { DropTypeNode } from '../operation-node/drop-type-node.js';\nimport { freeze } from '../util/object-utils.js';\nexport class DropTypeBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    ifExists() {\n        return new DropTypeBuilder({\n            ...this.#props,\n            node: DropTypeNode.cloneWith(this.#props.node, {\n                ifExists: true,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;AAClD;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA,WAAW;QACP,OAAO,IAAI,gBAAgB;YACvB,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,wOAAA,CAAA,eAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC3C,UAAU;YACd;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2586, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/refresh-materialized-view-builder.js"], "sourcesContent": ["/// <reference types=\"./refresh-materialized-view-builder.d.ts\" />\nimport { freeze } from '../util/object-utils.js';\nimport { RefreshMaterializedViewNode } from '../operation-node/refresh-materialized-view-node.js';\nexport class RefreshMaterializedViewBuilder {\n    #props;\n    constructor(props) {\n        this.#props = freeze(props);\n    }\n    /**\n     * Adds the \"concurrently\" modifier.\n     *\n     * Use this to refresh the view without locking out concurrent selects on the materialized view.\n     *\n     * WARNING!\n     * This cannot be used with the \"with no data\" modifier.\n     */\n    concurrently() {\n        return new RefreshMaterializedViewBuilder({\n            ...this.#props,\n            node: RefreshMaterializedViewNode.cloneWith(this.#props.node, {\n                concurrently: true,\n                withNoData: false,\n            }),\n        });\n    }\n    /**\n     * Adds the \"with data\" modifier.\n     *\n     * If specified (or defaults) the backing query is executed to provide the new data, and the materialized view is left in a scannable state\n     */\n    withData() {\n        return new RefreshMaterializedViewBuilder({\n            ...this.#props,\n            node: RefreshMaterializedViewNode.cloneWith(this.#props.node, {\n                withNoData: false,\n            }),\n        });\n    }\n    /**\n     * Adds the \"with no data\" modifier.\n     *\n     * If specified, no new data is generated and the materialized view is left in an unscannable state.\n     *\n     * WARNING!\n     * This cannot be used with the \"concurrently\" modifier.\n     */\n    withNoData() {\n        return new RefreshMaterializedViewBuilder({\n            ...this.#props,\n            node: RefreshMaterializedViewNode.cloneWith(this.#props.node, {\n                withNoData: true,\n                concurrently: false,\n            }),\n        });\n    }\n    /**\n     * Simply calls the provided function passing `this` as the only argument. `$call` returns\n     * what the provided function returns.\n     */\n    $call(func) {\n        return func(this);\n    }\n    toOperationNode() {\n        return this.#props.executor.transformQuery(this.#props.node, this.#props.queryId);\n    }\n    compile() {\n        return this.#props.executor.compileQuery(this.toOperationNode(), this.#props.queryId);\n    }\n    async execute() {\n        await this.#props.executor.executeQuery(this.compile(), this.#props.queryId);\n    }\n}\n"], "names": [], "mappings": "AAAA,kEAAkE;;;;AAClE;AACA;;;AACO,MAAM;IACT,CAAA,KAAM,CAAC;IACP,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,CAAA,KAAM,GAAG,CAAA,GAAA,sNAAA,CAAA,SAAM,AAAD,EAAE;IACzB;IACA;;;;;;;KAOC,GACD,eAAe;QACX,OAAO,IAAI,+BAA+B;YACtC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2PAAA,CAAA,8BAA2B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC1D,cAAc;gBACd,YAAY;YAChB;QACJ;IACJ;IACA;;;;KAIC,GACD,WAAW;QACP,OAAO,IAAI,+BAA+B;YACtC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2PAAA,CAAA,8BAA2B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC1D,YAAY;YAChB;QACJ;IACJ;IACA;;;;;;;KAOC,GACD,aAAa;QACT,OAAO,IAAI,+BAA+B;YACtC,GAAG,IAAI,CAAC,CAAA,KAAM;YACd,MAAM,2PAAA,CAAA,8BAA2B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE;gBAC1D,YAAY;gBACZ,cAAc;YAClB;QACJ;IACJ;IACA;;;KAGC,GACD,MAAM,IAAI,EAAE;QACR,OAAO,KAAK,IAAI;IACpB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,KAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACpF;IACA,UAAU;QACN,OAAO,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IACxF;IACA,MAAM,UAAU;QACZ,MAAM,IAAI,CAAC,CAAA,KAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAA,KAAM,CAAC,OAAO;IAC/E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/kysely%400.28.2/node_modules/kysely/dist/esm/schema/schema.js"], "sourcesContent": ["/// <reference types=\"./schema.d.ts\" />\nimport { AlterTableNode } from '../operation-node/alter-table-node.js';\nimport { CreateIndexNode } from '../operation-node/create-index-node.js';\nimport { CreateSchemaNode } from '../operation-node/create-schema-node.js';\nimport { CreateTableNode } from '../operation-node/create-table-node.js';\nimport { DropIndexNode } from '../operation-node/drop-index-node.js';\nimport { DropSchemaNode } from '../operation-node/drop-schema-node.js';\nimport { DropTableNode } from '../operation-node/drop-table-node.js';\nimport { parseTable } from '../parser/table-parser.js';\nimport { AlterTableBuilder } from './alter-table-builder.js';\nimport { CreateIndexBuilder } from './create-index-builder.js';\nimport { CreateSchemaBuilder } from './create-schema-builder.js';\nimport { CreateTableBuilder } from './create-table-builder.js';\nimport { DropIndexBuilder } from './drop-index-builder.js';\nimport { DropSchemaBuilder } from './drop-schema-builder.js';\nimport { DropTableBuilder } from './drop-table-builder.js';\nimport { createQueryId } from '../util/query-id.js';\nimport { WithSchemaPlugin } from '../plugin/with-schema/with-schema-plugin.js';\nimport { CreateViewBuilder } from './create-view-builder.js';\nimport { CreateViewNode } from '../operation-node/create-view-node.js';\nimport { DropViewBuilder } from './drop-view-builder.js';\nimport { DropViewNode } from '../operation-node/drop-view-node.js';\nimport { CreateTypeBuilder } from './create-type-builder.js';\nimport { DropTypeBuilder } from './drop-type-builder.js';\nimport { CreateTypeNode } from '../operation-node/create-type-node.js';\nimport { DropTypeNode } from '../operation-node/drop-type-node.js';\nimport { parseSchemableIdentifier } from '../parser/identifier-parser.js';\nimport { RefreshMaterializedViewBuilder } from './refresh-materialized-view-builder.js';\nimport { RefreshMaterializedViewNode } from '../operation-node/refresh-materialized-view-node.js';\n/**\n * Provides methods for building database schema.\n */\nexport class SchemaModule {\n    #executor;\n    constructor(executor) {\n        this.#executor = executor;\n    }\n    /**\n     * Create a new table.\n     *\n     * ### Examples\n     *\n     * This example creates a new table with columns `id`, `first_name`,\n     * `last_name` and `gender`:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('person')\n     *   .addColumn('id', 'integer', col => col.primaryKey().autoIncrement())\n     *   .addColumn('first_name', 'varchar', col => col.notNull())\n     *   .addColumn('last_name', 'varchar', col => col.notNull())\n     *   .addColumn('gender', 'varchar')\n     *   .execute()\n     * ```\n     *\n     * This example creates a table with a foreign key. Not all database\n     * engines support column-level foreign key constraint definitions.\n     * For example if you are using MySQL 5.X see the next example after\n     * this one.\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('id', 'integer', col => col.primaryKey().autoIncrement())\n     *   .addColumn('owner_id', 'integer', col => col\n     *     .references('person.id')\n     *     .onDelete('cascade')\n     *   )\n     *   .execute()\n     * ```\n     *\n     * This example adds a foreign key constraint for a columns just\n     * like the previous example, but using a table-level statement.\n     * On MySQL 5.X you need to define foreign key constraints like\n     * this:\n     *\n     * ```ts\n     * await db.schema\n     *   .createTable('pet')\n     *   .addColumn('id', 'integer', col => col.primaryKey().autoIncrement())\n     *   .addColumn('owner_id', 'integer')\n     *   .addForeignKeyConstraint(\n     *     'pet_owner_id_foreign', ['owner_id'], 'person', ['id'],\n     *     (constraint) => constraint.onDelete('cascade')\n     *   )\n     *   .execute()\n     * ```\n     */\n    createTable(table) {\n        return new CreateTableBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: CreateTableNode.create(parseTable(table)),\n        });\n    }\n    /**\n     * Drop a table.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .dropTable('person')\n     *   .execute()\n     * ```\n     */\n    dropTable(table) {\n        return new DropTableBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: DropTableNode.create(parseTable(table)),\n        });\n    }\n    /**\n     * Create a new index.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createIndex('person_full_name_unique_index')\n     *   .on('person')\n     *   .columns(['first_name', 'last_name'])\n     *   .execute()\n     * ```\n     */\n    createIndex(indexName) {\n        return new CreateIndexBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: CreateIndexNode.create(indexName),\n        });\n    }\n    /**\n     * Drop an index.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .dropIndex('person_full_name_unique_index')\n     *   .execute()\n     * ```\n     */\n    dropIndex(indexName) {\n        return new DropIndexBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: DropIndexNode.create(indexName),\n        });\n    }\n    /**\n     * Create a new schema.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createSchema('some_schema')\n     *   .execute()\n     * ```\n     */\n    createSchema(schema) {\n        return new CreateSchemaBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: CreateSchemaNode.create(schema),\n        });\n    }\n    /**\n     * Drop a schema.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .dropSchema('some_schema')\n     *   .execute()\n     * ```\n     */\n    dropSchema(schema) {\n        return new DropSchemaBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: DropSchemaNode.create(schema),\n        });\n    }\n    /**\n     * Alter a table.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .alterTable('person')\n     *   .alterColumn('first_name', (ac) => ac.setDataType('text'))\n     *   .execute()\n     * ```\n     */\n    alterTable(table) {\n        return new AlterTableBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: AlterTableNode.create(parseTable(table)),\n        });\n    }\n    /**\n     * Create a new view.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createView('dogs')\n     *   .orReplace()\n     *   .as(db.selectFrom('pet').selectAll().where('species', '=', 'dog'))\n     *   .execute()\n     * ```\n     */\n    createView(viewName) {\n        return new CreateViewBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: CreateViewNode.create(viewName),\n        });\n    }\n    /**\n     * Refresh a materialized view.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .refreshMaterializedView('my_view')\n     *   .concurrently()\n     *   .execute()\n     * ```\n     */\n    refreshMaterializedView(viewName) {\n        return new RefreshMaterializedViewBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: RefreshMaterializedViewNode.create(viewName),\n        });\n    }\n    /**\n     * Drop a view.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .dropView('dogs')\n     *   .ifExists()\n     *   .execute()\n     * ```\n     */\n    dropView(viewName) {\n        return new DropViewBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: DropViewNode.create(viewName),\n        });\n    }\n    /**\n     * Create a new type.\n     *\n     * Only some dialects like PostgreSQL have user-defined types.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .createType('species')\n     *   .asEnum(['dog', 'cat', 'frog'])\n     *   .execute()\n     * ```\n     */\n    createType(typeName) {\n        return new CreateTypeBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: CreateTypeNode.create(parseSchemableIdentifier(typeName)),\n        });\n    }\n    /**\n     * Drop a type.\n     *\n     * Only some dialects like PostgreSQL have user-defined types.\n     *\n     * ### Examples\n     *\n     * ```ts\n     * await db.schema\n     *   .dropType('species')\n     *   .ifExists()\n     *   .execute()\n     * ```\n     */\n    dropType(typeName) {\n        return new DropTypeBuilder({\n            queryId: createQueryId(),\n            executor: this.#executor,\n            node: DropTypeNode.create(parseSchemableIdentifier(typeName)),\n        });\n    }\n    /**\n     * Returns a copy of this schema module with the given plugin installed.\n     */\n    withPlugin(plugin) {\n        return new SchemaModule(this.#executor.withPlugin(plugin));\n    }\n    /**\n     * Returns a copy of this schema module  without any plugins.\n     */\n    withoutPlugins() {\n        return new SchemaModule(this.#executor.withoutPlugins());\n    }\n    /**\n     * See {@link QueryCreator.withSchema}\n     */\n    withSchema(schema) {\n        return new SchemaModule(this.#executor.withPluginAtFront(new WithSchemaPlugin(schema)));\n    }\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,MAAM;IACT,CAAA,QAAS,CAAC;IACV,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,CAAA,QAAS,GAAG;IACrB;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkDC,GACD,YAAY,KAAK,EAAE;QACf,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;QAC5C;IACJ;IACA;;;;;;;;;;KAUC,GACD,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,iOAAA,CAAA,mBAAgB,CAAC;YACxB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;QAC1C;IACJ;IACA;;;;;;;;;;;;KAYC,GACD,YAAY,SAAS,EAAE;QACnB,OAAO,IAAI,mOAAA,CAAA,qBAAkB,CAAC;YAC1B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,2OAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;QACjC;IACJ;IACA;;;;;;;;;;KAUC,GACD,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,iOAAA,CAAA,mBAAgB,CAAC;YACxB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,yOAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC/B;IACJ;IACA;;;;;;;;;;KAUC,GACD,aAAa,MAAM,EAAE;QACjB,OAAO,IAAI,oOAAA,CAAA,sBAAmB,CAAC;YAC3B,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,4OAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC;QAClC;IACJ;IACA;;;;;;;;;;KAUC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC;YACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAChC;IACJ;IACA;;;;;;;;;;;KAWC,GACD,WAAW,KAAK,EAAE;QACd,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC;YACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;QAC3C;IACJ;IACA;;;;;;;;;;;;KAYC,GACD,WAAW,QAAQ,EAAE;QACjB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC;YACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAChC;IACJ;IACA;;;;;;;;;;;KAWC,GACD,wBAAwB,QAAQ,EAAE;QAC9B,OAAO,IAAI,mPAAA,CAAA,iCAA8B,CAAC;YACtC,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,2PAAA,CAAA,8BAA2B,CAAC,MAAM,CAAC;QAC7C;IACJ;IACA;;;;;;;;;;;KAWC,GACD,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,gOAAA,CAAA,kBAAe,CAAC;YACvB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC;QAC9B;IACJ;IACA;;;;;;;;;;;;;KAaC,GACD,WAAW,QAAQ,EAAE;QACjB,OAAO,IAAI,kOAAA,CAAA,oBAAiB,CAAC;YACzB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,0OAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,GAAA,6NAAA,CAAA,2BAAwB,AAAD,EAAE;QACzD;IACJ;IACA;;;;;;;;;;;;;KAaC,GACD,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,gOAAA,CAAA,kBAAe,CAAC;YACvB,SAAS,CAAA,GAAA,kNAAA,CAAA,gBAAa,AAAD;YACrB,UAAU,IAAI,CAAC,CAAA,QAAS;YACxB,MAAM,wOAAA,CAAA,eAAY,CAAC,MAAM,CAAC,CAAA,GAAA,6NAAA,CAAA,2BAAwB,AAAD,EAAE;QACvD;IACJ;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,QAAS,CAAC,UAAU,CAAC;IACtD;IACA;;KAEC,GACD,iBAAiB;QACb,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,QAAS,CAAC,cAAc;IACzD;IACA;;KAEC,GACD,WAAW,MAAM,EAAE;QACf,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,QAAS,CAAC,iBAAiB,CAAC,IAAI,mPAAA,CAAA,mBAAgB,CAAC;IAClF;AACJ", "ignoreList": [0], "debugId": null}}]}