{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/server/src/db/index.ts"], "sourcesContent": ["import { drizzle } from \"drizzle-orm/node-postgres\";\n\nexport const db = drizzle(process.env.DATABASE_URL || \"\");\n\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEO,MAAM,KAAK,CAAA,GAAA,oSAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,GAAG,CAAC,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/server/src/db/schema/auth.ts"], "sourcesContent": ["import { pgTable, text, timestamp, boolean, serial } from \"drizzle-orm/pg-core\";\n\nexport const user = pgTable(\"user\", {\n\t\t\t\t\tid: text(\"id\").primaryKey(),\n\t\t\t\t\tname: text('name').notNull(),\n email: text('email').notNull().unique(),\n emailVerified: boolean('email_verified').notNull(),\n image: text('image'),\n createdAt: timestamp('created_at').notNull(),\n updatedAt: timestamp('updated_at').notNull()\n\t\t\t\t});\n\nexport const session = pgTable(\"session\", {\n\t\t\t\t\tid: text(\"id\").primaryKey(),\n\t\t\t\t\texpiresAt: timestamp('expires_at').notNull(),\n token: text('token').notNull().unique(),\n createdAt: timestamp('created_at').notNull(),\n updatedAt: timestamp('updated_at').notNull(),\n ipAddress: text('ip_address'),\n userAgent: text('user_agent'),\n userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' })\n\t\t\t\t});\n\nexport const account = pgTable(\"account\", {\n\t\t\t\t\tid: text(\"id\").primaryKey(),\n\t\t\t\t\taccountId: text('account_id').notNull(),\n providerId: text('provider_id').notNull(),\n userId: text('user_id').notNull().references(()=> user.id, { onDelete: 'cascade' }),\n accessToken: text('access_token'),\n refreshToken: text('refresh_token'),\n idToken: text('id_token'),\n accessTokenExpiresAt: timestamp('access_token_expires_at'),\n refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),\n scope: text('scope'),\n password: text('password'),\n createdAt: timestamp('created_at').notNull(),\n updatedAt: timestamp('updated_at').notNull()\n\t\t\t\t});\n\nexport const verification = pgTable(\"verification\", {\n\t\t\t\t\tid: text(\"id\").primaryKey(),\n\t\t\t\t\tidentifier: text('identifier').notNull(),\n value: text('value').notNull(),\n expiresAt: timestamp('expires_at').notNull(),\n createdAt: timestamp('created_at'),\n updatedAt: timestamp('updated_at')\n\t\t\t\t});\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;;AAEO,MAAM,OAAO,CAAA,GAAA,6RAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAC/B,IAAI,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,MAAM,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC9B,OAAO,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,eAAe,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO;IAChD,OAAO,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IACZ,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;AACvC;AAEG,MAAM,UAAU,CAAA,GAAA,6RAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACrC,IAAI,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC9C,OAAO,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,GAAG,MAAM;IACrC,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IAChB,WAAW,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IAChB,QAAQ,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAK,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;AAC9E;AAEG,MAAM,UAAU,CAAA,GAAA,6RAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IACrC,IAAI,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACzC,YAAY,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACvC,QAAQ,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAK,KAAK,EAAE,EAAE;QAAE,UAAU;IAAU;IACjF,aAAa,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IAClB,cAAc,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IACnB,SAAS,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IACd,sBAAsB,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE;IAChC,uBAAuB,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE;IACjC,OAAO,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE;IACf,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;AACvC;AAEG,MAAM,eAAe,CAAA,GAAA,6RAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAC/C,IAAI,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,YAAY,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IAC1C,OAAO,CAAA,GAAA,uSAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO;IAC1C,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE;IACrB,WAAW,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD,EAAE;AAClB", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/server/src/lib/auth.ts"], "sourcesContent": ["\nimport { betterAuth } from \"better-auth\";\nimport { drizzleAdapter } from \"better-auth/adapters/drizzle\";\nimport { db } from \"../db\";\nimport * as schema from \"../db/schema/auth\";\n\nexport const auth = betterAuth({\n  database: drizzleAdapter(db, {\n    provider: \"pg\",\n    \n    \n    schema: schema,\n  }),\n  trustedOrigins: [\n    process.env.CORS_ORIGIN || \"\",\n  ],\n  emailAndPassword: {\n    enabled: true,\n  },\n  secret: process.env.BETTER_AUTH_SECRET,\n  baseURL: process.env.BETTER_AUTH_URL,\n});\n\n\n\n"], "names": [], "mappings": ";;;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,OAAO,CAAA,GAAA,8NAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,UAAU,CAAA,GAAA,gPAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,KAAE,EAAE;QAC3B,UAAU;QAGV,QAAQ;IACV;IACA,gBAAgB;QACd,QAAQ,GAAG,CAAC,WAAW,IAAI;KAC5B;IACD,kBAAkB;QAChB,SAAS;IACX;IACA,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;IACtC,SAAS,QAAQ,GAAG,CAAC,eAAe;AACtC", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/server/src/app/api/auth/%5B...all%5D/route.ts"], "sourcesContent": ["import { auth } from \"@/lib/auth\";\nimport { toNextJsHandler } from \"better-auth/next-js\";\n\nexport const { GET, POST } = toNextJsHandler(auth.handler);\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAEO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mOAAA,CAAA,kBAAe,AAAD,EAAE,sIAAA,CAAA,OAAI,CAAC,OAAO", "debugId": null}}]}