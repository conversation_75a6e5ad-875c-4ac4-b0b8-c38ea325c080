{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/server/src/app/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\n\nexport async function GET() {\n  return NextResponse.json({ message: \"OK\" });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAK;AAC3C", "debugId": null}}]}