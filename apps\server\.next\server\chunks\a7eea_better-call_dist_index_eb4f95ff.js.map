{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/error.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/utils.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/to-response.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/validator.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/crypto.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/cookies.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/context.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/middleware.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/endpoint.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/router.ts", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/node_modules/.pnpm/zod%403.24.1/node_modules/zod/lib/index.mjs", "file://C%3A/Users/<USER>/prj/my-erp/node_modules/.pnpm/better-call%401.0.12/node_modules/better-call/src/openapi.ts"], "sourcesContent": ["export const _statusCode = {\n\tOK: 200,\n\tCREATED: 201,\n\tACCEPTED: 202,\n\tNO_CONTENT: 204,\n\tMU<PERSON>IPLE_CHOICES: 300,\n\tMOVED_PERMANENTLY: 301,\n\tFOUND: 302,\n\tSEE_OTHER: 303,\n\tNOT_MODIFIED: 304,\n\tTEMPORARY_REDIRECT: 307,\n\tBAD_REQUEST: 400,\n\tUNAUTHORIZED: 401,\n\tPAYMENT_REQUIRED: 402,\n\tFORBIDDEN: 403,\n\tNOT_FOUND: 404,\n\tMETHOD_NOT_ALLOWED: 405,\n\tNOT_ACCEPTABLE: 406,\n\tPROXY_AUTHENTICATION_REQUIRED: 407,\n\tREQUEST_TIMEOUT: 408,\n\tCONFLICT: 409,\n\tGONE: 410,\n\tLENGTH_REQUIRED: 411,\n\tPRECONDITION_FAILED: 412,\n\tPAYLOAD_TOO_LARGE: 413,\n\tURI_TOO_LONG: 414,\n\tUNSUPPORTED_MEDIA_TYPE: 415,\n\tRANGE_NOT_SATISFIABLE: 416,\n\tEXPECTATION_FAILED: 417,\n\t\"I'M_A_TEAPOT\": 418,\n\tMISDIRECTED_REQUEST: 421,\n\tUNPROCESSABLE_ENTITY: 422,\n\tLOCKED: 423,\n\tFAILED_DEPENDENCY: 424,\n\tTOO_EARLY: 425,\n\tUPGRADE_REQUIRED: 426,\n\tPRECONDITION_REQUIRED: 428,\n\tTOO_MANY_REQUESTS: 429,\n\tREQUEST_HEADER_FIELDS_TOO_LARGE: 431,\n\tUNAVAILABLE_FOR_LEGAL_REASONS: 451,\n\tINTERNAL_SERVER_ERROR: 500,\n\tNOT_IMPLEMENTED: 501,\n\tBAD_GATEWAY: 502,\n\tSERVICE_UNAVAILABLE: 503,\n\tGATEWAY_TIMEOUT: 504,\n\tHTTP_VERSION_NOT_SUPPORTED: 505,\n\tVARIANT_ALSO_NEGOTIATES: 506,\n\tINSUFFICIENT_STORAGE: 507,\n\tLOOP_DETECTED: 508,\n\tNOT_EXTENDED: 510,\n\tNETWORK_AUTHENTICATION_REQUIRED: 511,\n};\n\nexport type Status =\n\t| 100\n\t| 101\n\t| 102\n\t| 103\n\t| 200\n\t| 201\n\t| 202\n\t| 203\n\t| 204\n\t| 205\n\t| 206\n\t| 207\n\t| 208\n\t| 226\n\t| 300\n\t| 301\n\t| 302\n\t| 303\n\t| 304\n\t| 305\n\t| 306\n\t| 307\n\t| 308\n\t| 400\n\t| 401\n\t| 402\n\t| 403\n\t| 404\n\t| 405\n\t| 406\n\t| 407\n\t| 408\n\t| 409\n\t| 410\n\t| 411\n\t| 412\n\t| 413\n\t| 414\n\t| 415\n\t| 416\n\t| 417\n\t| 418\n\t| 421\n\t| 422\n\t| 423\n\t| 424\n\t| 425\n\t| 426\n\t| 428\n\t| 429\n\t| 431\n\t| 451\n\t| 500\n\t| 501\n\t| 502\n\t| 503\n\t| 504\n\t| 505\n\t| 506\n\t| 507\n\t| 508\n\t| 510\n\t| 511;\n\nexport class APIError extends Error {\n\tconstructor(\n\t\tpublic status: keyof typeof _statusCode | Status = \"INTERNAL_SERVER_ERROR\",\n\t\tpublic body:\n\t\t\t| ({\n\t\t\t\t\tmessage?: string;\n\t\t\t\t\tcode?: string;\n\t\t\t  } & Record<string, any>)\n\t\t\t| undefined = undefined,\n\t\tpublic headers: HeadersInit = {},\n\t\tpublic statusCode = typeof status === \"number\" ? status : _statusCode[status],\n\t) {\n\t\tsuper(body?.message);\n\t\tthis.name = \"APIError\";\n\t\tthis.status = status;\n\t\tthis.headers = headers;\n\t\tthis.statusCode = statusCode;\n\t\tthis.body = body\n\t\t\t? {\n\t\t\t\t\tcode: body?.message\n\t\t\t\t\t\t?.toUpperCase()\n\t\t\t\t\t\t.replace(/ /g, \"_\")\n\t\t\t\t\t\t.replace(/[^A-Z0-9_]/g, \"\"),\n\t\t\t\t\t...body,\n\t\t\t\t}\n\t\t\t: undefined;\n\t\tthis.stack = \"\";\n\t}\n}\n", "import { APIError } from \"./error\";\n\nexport async function getBody(request: Request) {\n\tconst contentType = request.headers.get(\"content-type\") || \"\";\n\n\tif (!request.body) {\n\t\treturn undefined;\n\t}\n\n\tif (contentType.includes(\"application/json\")) {\n\t\treturn await request.json();\n\t}\n\n\tif (contentType.includes(\"application/x-www-form-urlencoded\")) {\n\t\tconst formData = await request.formData();\n\t\tconst result: Record<string, string> = {};\n\t\tformData.forEach((value, key) => {\n\t\t\tresult[key] = value.toString();\n\t\t});\n\t\treturn result;\n\t}\n\n\tif (contentType.includes(\"multipart/form-data\")) {\n\t\tconst formData = await request.formData();\n\t\tconst result: Record<string, any> = {};\n\t\tformData.forEach((value, key) => {\n\t\t\tresult[key] = value;\n\t\t});\n\t\treturn result;\n\t}\n\n\tif (contentType.includes(\"text/plain\")) {\n\t\treturn await request.text();\n\t}\n\n\tif (contentType.includes(\"application/octet-stream\")) {\n\t\treturn await request.arrayBuffer();\n\t}\n\n\tif (\n\t\tcontentType.includes(\"application/pdf\") ||\n\t\tcontentType.includes(\"image/\") ||\n\t\tcontentType.includes(\"video/\")\n\t) {\n\t\tconst blob = await request.blob();\n\t\treturn blob;\n\t}\n\n\tif (contentType.includes(\"application/stream\") || request.body instanceof ReadableStream) {\n\t\treturn request.body;\n\t}\n\n\treturn await request.text();\n}\n\nexport function isAPIError(error: any) {\n\treturn error instanceof APIError || error?.name === \"APIError\";\n}\n\nexport function tryDecode(str: string) {\n\ttry {\n\t\treturn str.includes(\"%\") ? decodeURIComponent(str) : str;\n\t} catch {\n\t\treturn str;\n\t}\n}\n", "import { APIError } from \"./error\";\nimport { isAPIError } from \"./utils\";\n\nfunction isJSONSerializable(value: any) {\n\tif (value === undefined) {\n\t\treturn false;\n\t}\n\tconst t = typeof value;\n\tif (t === \"string\" || t === \"number\" || t === \"boolean\" || t === null) {\n\t\treturn true;\n\t}\n\tif (t !== \"object\") {\n\t\treturn false;\n\t}\n\tif (Array.isArray(value)) {\n\t\treturn true;\n\t}\n\tif (value.buffer) {\n\t\treturn false;\n\t}\n\treturn (\n\t\t(value.constructor && value.constructor.name === \"Object\") ||\n\t\ttypeof value.toJSON === \"function\"\n\t);\n}\n\nexport function toResponse(data?: any, init?: ResponseInit): Response {\n\tif (data instanceof Response) {\n\t\tif (init?.headers instanceof Headers) {\n\t\t\tinit.headers.forEach((value, key) => {\n\t\t\t\tdata.headers.set(key, value);\n\t\t\t});\n\t\t}\n\t\treturn data;\n\t}\n\tif (data?._flag === \"json\") {\n\t\tconst routerResponse = data.routerResponse;\n\t\tif (routerResponse instanceof Response) {\n\t\t\treturn routerResponse;\n\t\t}\n\t\treturn toResponse(data.body, {\n\t\t\theaders: data.headers,\n\t\t\tstatus: data.status,\n\t\t});\n\t}\n\tif (isAPIError(data)) {\n\t\treturn toResponse(data.body, {\n\t\t\tstatus: data.statusCode,\n\t\t\tstatusText: data.status.toString(),\n\t\t\theaders: init?.headers || data.headers,\n\t\t});\n\t}\n\tlet body = data;\n\tlet headers = new Headers(init?.headers);\n\tif (!data) {\n\t\tif (data === null) {\n\t\t\tbody = JSON.stringify(null);\n\t\t}\n\t\theaders.set(\"content-type\", \"application/json\");\n\t} else if (typeof data === \"string\") {\n\t\tbody = data;\n\t\theaders.set(\"Content-Type\", \"text/plain\");\n\t} else if (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) {\n\t\tbody = data;\n\t\theaders.set(\"Content-Type\", \"application/octet-stream\");\n\t} else if (data instanceof Blob) {\n\t\tbody = data;\n\t\theaders.set(\"Content-Type\", data.type || \"application/octet-stream\");\n\t} else if (data instanceof FormData) {\n\t\tbody = data;\n\t} else if (data instanceof URLSearchParams) {\n\t\tbody = data;\n\t\theaders.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n\t} else if (data instanceof ReadableStream) {\n\t\tbody = data;\n\t\theaders.set(\"Content-Type\", \"application/octet-stream\");\n\t} else if (isJSONSerializable(data)) {\n\t\tbody = JSON.stringify(data, (key, value) => {\n\t\t\tif (typeof value === \"bigint\") {\n\t\t\t\treturn value.toString();\n\t\t\t}\n\t\t\treturn value;\n\t\t});\n\t\theaders.set(\"Content-Type\", \"application/json\");\n\t}\n\n\treturn new Response(body, {\n\t\t...init,\n\t\theaders,\n\t});\n}\n", "import type { EndpointOptions } from \"./endpoint\";\nimport type { InputContext } from \"./context\";\nimport type { StandardSchemaV1 } from \"./standard-schema\";\n\ntype ValidationResponse =\n\t| {\n\t\t\tdata: {\n\t\t\t\tbody: any;\n\t\t\t\tquery: any;\n\t\t\t};\n\t\t\terror: null;\n\t  }\n\t| {\n\t\t\tdata: null;\n\t\t\terror: {\n\t\t\t\tmessage: string;\n\t\t\t};\n\t  };\n\n/**\n * Runs validation on body and query\n * @returns error and data object\n */\nexport async function runValidation(\n\toptions: EndpointOptions,\n\tcontext: InputContext<any, any> = {},\n): Promise<ValidationResponse> {\n\tlet request = {\n\t\tbody: context.body,\n\t\tquery: context.query,\n\t} as {\n\t\tbody: any;\n\t\tquery: any;\n\t};\n\tif (options.body) {\n\t\tconst result = await options.body[\"~standard\"].validate(context.body);\n\t\tif (result.issues) {\n\t\t\treturn {\n\t\t\t\tdata: null,\n\t\t\t\terror: fromError(result.issues, \"body\"),\n\t\t\t};\n\t\t}\n\t\trequest.body = result.value;\n\t}\n\n\tif (options.query) {\n\t\tconst result = await options.query[\"~standard\"].validate(context.query);\n\t\tif (result.issues) {\n\t\t\treturn {\n\t\t\t\tdata: null,\n\t\t\t\terror: fromError(result.issues, \"query\"),\n\t\t\t};\n\t\t}\n\t\trequest.query = result.value;\n\t}\n\tif (options.requireHeaders && !context.headers) {\n\t\treturn {\n\t\t\tdata: null,\n\t\t\terror: { message: \"Headers is required\" },\n\t\t};\n\t}\n\tif (options.requireRequest && !context.request) {\n\t\treturn {\n\t\t\tdata: null,\n\t\t\terror: { message: \"Request is required\" },\n\t\t};\n\t}\n\treturn {\n\t\tdata: request,\n\t\terror: null,\n\t};\n}\n\nexport function fromError(error: readonly StandardSchemaV1.Issue[], validating: string) {\n\tconst errorMessages: string[] = [];\n\n\tfor (const issue of error) {\n\t\tconst message = issue.message;\n\t\terrorMessages.push(message);\n\t}\n\treturn {\n\t\tmessage: `Invalid ${validating} parameters`,\n\t};\n}\n", "import { subtle } from \"uncrypto\";\n\nconst algorithm = { name: \"<PERSON><PERSON>\", hash: \"SHA-256\" };\n\nexport const getCryptoKey = async (secret: string | BufferSource) => {\n\tconst secretBuf = typeof secret === \"string\" ? new TextEncoder().encode(secret) : secret;\n\treturn await subtle.importKey(\"raw\", secretBuf, algorithm, false, [\"sign\", \"verify\"]);\n};\n\nexport const verifySignature = async (\n\tbase64Signature: string,\n\tvalue: string,\n\tsecret: Crypto<PERSON><PERSON>,\n): Promise<boolean> => {\n\ttry {\n\t\tconst signatureBinStr = atob(base64Signature);\n\t\tconst signature = new Uint8Array(signatureBinStr.length);\n\t\tfor (let i = 0, len = signatureBinStr.length; i < len; i++) {\n\t\t\tsignature[i] = signatureBinStr.charCodeAt(i);\n\t\t}\n\t\treturn await subtle.verify(algorithm, secret, signature, new TextEncoder().encode(value));\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\nconst makeSignature = async (value: string, secret: string | BufferSource): Promise<string> => {\n\tconst key = await getCrypto<PERSON>ey(secret);\n\tconst signature = await subtle.sign(algorithm.name, key, new TextEncoder().encode(value));\n\t// the returned base64 encoded signature will always be 44 characters long and end with one or two equal signs\n\treturn btoa(String.fromCharCode(...new Uint8Array(signature)));\n};\n\nexport const signCookieValue = async (value: string, secret: string | BufferSource) => {\n\tconst signature = await makeSignature(value, secret);\n\tvalue = `${value}.${signature}`;\n\tvalue = encodeURIComponent(value);\n\treturn value;\n};\n", "import { sign<PERSON><PERSON><PERSON>Value } from \"./crypto\";\nimport { tryDecode } from \"./utils\";\n\nexport type CookiePrefixOptions = \"host\" | \"secure\";\n\nexport type CookieOptions = {\n\t/**\n\t * Domain of the cookie\n\t *\n\t * The Domain attribute specifies which server can receive a cookie. If specified, cookies are\n\t * available on the specified server and its subdomains. If the it is not\n\t * specified, the cookies are available on the server that sets it but not on\n\t * its subdomains.\n\t *\n\t * @example\n\t * `domain: \"example.com\"`\n\t */\n\tdomain?: string;\n\t/**\n\t * A lifetime of a cookie. Permanent cookies are deleted after the date specified in the\n\t * Expires attribute:\n\t *\n\t * Expires has been available for longer than Max-Age, however Max-Age is less error-prone, and\n\t * takes precedence when both are set. The rationale behind this is that when you set an\n\t * Expires date and time, they're relative to the client the cookie is being set on. If the\n\t * server is set to a different time, this could cause errors\n\t */\n\texpires?: Date;\n\t/**\n\t * Forbids JavaScript from accessing the cookie, for example, through the Document.cookie\n\t * property. Note that a cookie that has been created with HttpOnly will still be sent with\n\t * JavaScript-initiated requests, for example, when calling XMLHttpRequest.send() or fetch().\n\t * This mitigates attacks against cross-site scripting\n\t */\n\thttpOnly?: boolean;\n\t/**\n\t * Indicates the number of seconds until the cookie expires. A zero or negative number will\n\t * expire the cookie immediately. If both Expires and Max-Age are set, Max-Age has precedence.\n\t *\n\t * @example 604800 - 7 days\n\t */\n\tmaxAge?: number;\n\t/**\n\t * Indicates the path that must exist in the requested URL for the browser to send the Cookie\n\t * header.\n\t *\n\t * @example\n\t * \"/docs\"\n\t * // -> the request paths /docs, /docs/, /docs/Web/, and /docs/Web/HTTP will all match. the request paths /, /fr/docs will not match.\n\t */\n\tpath?: string;\n\t/**\n\t * Indicates that the cookie is sent to the server only when a request is made with the https:\n\t * scheme (except on localhost), and therefore, is more resistant to man-in-the-middle attacks.\n\t */\n\tsecure?: boolean;\n\t/**\n\t * Controls whether or not a cookie is sent with cross-site requests, providing some protection\n\t * against cross-site request forgery attacks (CSRF).\n\t *\n\t * Strict -  Means that the browser sends the cookie only for same-site requests, that is,\n\t * requests originating from the same site that set the cookie. If a request originates from a\n\t * different domain or scheme (even with the same domain), no cookies with the SameSite=Strict\n\t * attribute are sent.\n\t *\n\t * Lax - Means that the cookie is not sent on cross-site requests, such as on requests to load\n\t * images or frames, but is sent when a user is navigating to the origin site from an external\n\t * site (for example, when following a link). This is the default behavior if the SameSite\n\t * attribute is not specified.\n\t *\n\t * None - Means that the browser sends the cookie with both cross-site and same-site requests.\n\t * The Secure attribute must also be set when setting this value.\n\t */\n\tsameSite?: \"Strict\" | \"Lax\" | \"None\" | \"strict\" | \"lax\" | \"none\";\n\t/**\n\t * Indicates that the cookie should be stored using partitioned storage. Note that if this is\n\t * set, the Secure directive must also be set.\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/Privacy/Privacy_sandbox/Partitioned_cookies\n\t */\n\tpartitioned?: boolean;\n\t/**\n\t * Cooke Prefix\n\t *\n\t * - secure: `__Secure-` -> `__Secure-cookie-name`\n\t * - host: `__Host-` -> `__Host-cookie-name`\n\t *\n\t * `secure` must be set to true to use prefixes\n\t */\n\tprefix?: CookiePrefixOptions;\n};\n\nexport const getCookieKey = (key: string, prefix?: CookiePrefixOptions) => {\n\tlet finalKey = key;\n\tif (prefix) {\n\t\tif (prefix === \"secure\") {\n\t\t\tfinalKey = \"__Secure-\" + key;\n\t\t} else if (prefix === \"host\") {\n\t\t\tfinalKey = \"__Host-\" + key;\n\t\t} else {\n\t\t\treturn undefined;\n\t\t}\n\t}\n\treturn finalKey;\n};\n\n/**\n * Parse an HTTP Cookie header string and returning an object of all cookie\n * name-value pairs.\n *\n * Inspired by https://github.com/unjs/cookie-es/blob/main/src/cookie/parse.ts\n *\n * @param str the string representing a `Cookie` header value\n */\nexport function parseCookies(str: string) {\n\tif (typeof str !== \"string\") {\n\t\tthrow new TypeError(\"argument str must be a string\");\n\t}\n\n\tconst cookies: Map<string, string> = new Map();\n\n\tlet index = 0;\n\twhile (index < str.length) {\n\t\tconst eqIdx = str.indexOf(\"=\", index);\n\n\t\tif (eqIdx === -1) {\n\t\t\tbreak;\n\t\t}\n\n\t\tlet endIdx = str.indexOf(\";\", index);\n\n\t\tif (endIdx === -1) {\n\t\t\tendIdx = str.length;\n\t\t} else if (endIdx < eqIdx) {\n\t\t\tindex = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst key = str.slice(index, eqIdx).trim();\n\t\tif (!cookies.has(key)) {\n\t\t\tlet val = str.slice(eqIdx + 1, endIdx).trim();\n\t\t\tif (val.codePointAt(0) === 0x22) {\n\t\t\t\tval = val.slice(1, -1);\n\t\t\t}\n\t\t\tcookies.set(key, tryDecode(val));\n\t\t}\n\n\t\tindex = endIdx + 1;\n\t}\n\n\treturn cookies;\n}\n\nconst _serialize = (key: string, value: string, opt: CookieOptions = {}) => {\n\tlet cookie: string;\n\n\tif (opt?.prefix === \"secure\") {\n\t\tcookie = `${`__Secure-${key}`}=${value}`;\n\t} else if (opt?.prefix === \"host\") {\n\t\tcookie = `${`__Host-${key}`}=${value}`;\n\t} else {\n\t\tcookie = `${key}=${value}`;\n\t}\n\n\tif (key.startsWith(\"__Secure-\") && !opt.secure) {\n\t\topt.secure = true;\n\t}\n\n\tif (key.startsWith(\"__Host-\")) {\n\t\tif (!opt.secure) {\n\t\t\topt.secure = true;\n\t\t}\n\n\t\tif (opt.path !== \"/\") {\n\t\t\topt.path = \"/\";\n\t\t}\n\n\t\tif (opt.domain) {\n\t\t\topt.domain = undefined;\n\t\t}\n\t}\n\n\tif (opt && typeof opt.maxAge === \"number\" && opt.maxAge >= 0) {\n\t\tif (opt.maxAge > 34560000) {\n\t\t\tthrow new Error(\n\t\t\t\t\"Cookies Max-Age SHOULD NOT be greater than 400 days (34560000 seconds) in duration.\",\n\t\t\t);\n\t\t}\n\t\tcookie += `; Max-Age=${Math.floor(opt.maxAge)}`;\n\t}\n\n\tif (opt.domain && opt.prefix !== \"host\") {\n\t\tcookie += `; Domain=${opt.domain}`;\n\t}\n\n\tif (opt.path) {\n\t\tcookie += `; Path=${opt.path}`;\n\t}\n\n\tif (opt.expires) {\n\t\tif (opt.expires.getTime() - Date.now() > 34560000_000) {\n\t\t\tthrow new Error(\n\t\t\t\t\"Cookies Expires SHOULD NOT be greater than 400 days (34560000 seconds) in the future.\",\n\t\t\t);\n\t\t}\n\t\tcookie += `; Expires=${opt.expires.toUTCString()}`;\n\t}\n\n\tif (opt.httpOnly) {\n\t\tcookie += \"; HttpOnly\";\n\t}\n\n\tif (opt.secure) {\n\t\tcookie += \"; Secure\";\n\t}\n\n\tif (opt.sameSite) {\n\t\tcookie += `; SameSite=${opt.sameSite.charAt(0).toUpperCase() + opt.sameSite.slice(1)}`;\n\t}\n\n\tif (opt.partitioned) {\n\t\tif (!opt.secure) {\n\t\t\topt.secure = true;\n\t\t}\n\t\tcookie += \"; Partitioned\";\n\t}\n\n\treturn cookie;\n};\n\nexport const serializeCookie = (key: string, value: string, opt?: CookieOptions) => {\n\tvalue = encodeURIComponent(value);\n\treturn _serialize(key, value, opt);\n};\n\nexport const serializeSignedCookie = async (\n\tkey: string,\n\tvalue: string,\n\tsecret: string,\n\topt?: CookieOptions,\n) => {\n\tvalue = await signCookieValue(value, secret);\n\treturn _serialize(key, value, opt);\n};\n", "import type { EndpointOptions } from \"./endpoint\";\nimport { _statusCode, APIError, type Status } from \"./error\";\nimport type {\n\tInferParamPath,\n\tInferParamWildCard,\n\tIsEmptyObject,\n\tPrettify,\n\tUnionToIntersection,\n} from \"./helper\";\nimport type { Middleware, MiddlewareOptions } from \"./middleware\";\nimport { runValidation } from \"./validator\";\nimport {\n\tgetCookieKey,\n\tparseCookies,\n\tserializeCookie,\n\tserializeSignedCookie,\n\ttype CookieOptions,\n\ttype CookiePrefixOptions,\n} from \"./cookies\";\nimport { getCryptoKey, verifySignature } from \"./crypto\";\nimport type { StandardSchemaV1 } from \"./standard-schema\";\n\nexport type HTTPMethod = \"GET\" | \"POST\" | \"PUT\" | \"DELETE\" | \"PATCH\";\nexport type Method = HTTPMethod | \"*\";\n\nexport type InferBodyInput<\n\tOptions extends EndpointOptions | MiddlewareOptions,\n\tBody = Options[\"metadata\"] extends {\n\t\t$Infer: {\n\t\t\tbody: infer B;\n\t\t};\n\t}\n\t\t? B\n\t\t: Options[\"body\"] extends StandardSchemaV1\n\t\t\t? StandardSchemaV1.InferInput<Options[\"body\"]>\n\t\t\t: undefined,\n> = undefined extends Body\n\t? {\n\t\t\tbody?: Body;\n\t\t}\n\t: {\n\t\t\tbody: Body;\n\t\t};\n\nexport type InferBody<Options extends EndpointOptions | MiddlewareOptions> =\n\tOptions[\"metadata\"] extends {\n\t\t$Infer: {\n\t\t\tbody: infer Body;\n\t\t};\n\t}\n\t\t? Body\n\t\t: Options[\"body\"] extends StandardSchemaV1\n\t\t\t? StandardSchemaV1.InferOutput<Options[\"body\"]>\n\t\t\t: any;\n\nexport type InferQueryInput<\n\tOptions extends EndpointOptions | MiddlewareOptions,\n\tQuery = Options[\"metadata\"] extends {\n\t\t$Infer: {\n\t\t\tquery: infer Query;\n\t\t};\n\t}\n\t\t? Query\n\t\t: Options[\"query\"] extends StandardSchemaV1\n\t\t\t? StandardSchemaV1.InferInput<Options[\"query\"]>\n\t\t\t: Record<string, any> | undefined,\n> = undefined extends Query\n\t? {\n\t\t\tquery?: Query;\n\t\t}\n\t: {\n\t\t\tquery: Query;\n\t\t};\n\nexport type InferQuery<Options extends EndpointOptions | MiddlewareOptions> =\n\tOptions[\"metadata\"] extends {\n\t\t$Infer: {\n\t\t\tquery: infer Query;\n\t\t};\n\t}\n\t\t? Query\n\t\t: Options[\"query\"] extends StandardSchemaV1\n\t\t\t? StandardSchemaV1.InferOutput<Options[\"query\"]>\n\t\t\t: Record<string, any> | undefined;\n\nexport type InferMethod<Options extends EndpointOptions> = Options[\"method\"] extends Array<Method>\n\t? Options[\"method\"][number]\n\t: Options[\"method\"] extends \"*\"\n\t\t? HTTPMethod\n\t\t: Options[\"method\"];\n\nexport type InferInputMethod<\n\tOptions extends EndpointOptions,\n\tMethod = Options[\"method\"] extends Array<any>\n\t\t? Options[\"method\"][number]\n\t\t: Options[\"method\"] extends \"*\"\n\t\t\t? HTTPMethod\n\t\t\t: Options[\"method\"] | undefined,\n> = undefined extends Method\n\t? {\n\t\t\tmethod?: Method;\n\t\t}\n\t: {\n\t\t\tmethod: Method;\n\t\t};\n\nexport type InferParam<Path extends string> = IsEmptyObject<\n\tInferParamPath<Path> & InferParamWildCard<Path>\n> extends true\n\t? Record<string, any> | undefined\n\t: Prettify<InferParamPath<Path> & InferParamWildCard<Path>>;\n\nexport type InferParamInput<Path extends string> = IsEmptyObject<\n\tInferParamPath<Path> & InferParamWildCard<Path>\n> extends true\n\t? {\n\t\t\tparams?: Record<string, any>;\n\t\t}\n\t: {\n\t\t\tparams: Prettify<InferParamPath<Path> & InferParamWildCard<Path>>;\n\t\t};\n\nexport type InferRequest<Option extends EndpointOptions | MiddlewareOptions> =\n\tOption[\"requireRequest\"] extends true ? Request : Request | undefined;\n\nexport type InferRequestInput<Option extends EndpointOptions | MiddlewareOptions> =\n\tOption[\"requireRequest\"] extends true\n\t\t? {\n\t\t\t\trequest: Request;\n\t\t\t}\n\t\t: {\n\t\t\t\trequest?: Request;\n\t\t\t};\n\nexport type InferHeaders<Option extends EndpointOptions | MiddlewareOptions> =\n\tOption[\"requireHeaders\"] extends true ? Headers : Headers | undefined;\n\nexport type InferHeadersInput<Option extends EndpointOptions | MiddlewareOptions> =\n\tOption[\"requireHeaders\"] extends true\n\t\t? {\n\t\t\t\theaders: HeadersInit;\n\t\t\t}\n\t\t: {\n\t\t\t\theaders?: HeadersInit;\n\t\t\t};\n\nexport type InferUse<Opts extends EndpointOptions[\"use\"]> = Opts extends Middleware[]\n\t? UnionToIntersection<Awaited<ReturnType<Opts[number]>>>\n\t: {};\n\nexport type InferMiddlewareBody<Options extends MiddlewareOptions> =\n\tOptions[\"body\"] extends StandardSchemaV1<infer T> ? T : any;\n\nexport type InferMiddlewareQuery<Options extends MiddlewareOptions> =\n\tOptions[\"query\"] extends StandardSchemaV1<infer T> ? T : Record<string, any> | undefined;\n\ntype StrictKeys<T, U extends T = T> = Exclude<keyof U, keyof T> extends never ? U : never;\nexport type InputContext<\n\tPath extends string,\n\tOptions extends EndpointOptions,\n> = InferBodyInput<Options> &\n\tInferInputMethod<Options> &\n\tInferQueryInput<Options> &\n\tInferParamInput<Path> &\n\tInferRequestInput<Options> &\n\tInferHeadersInput<Options> & {\n\t\tasResponse?: boolean;\n\t\treturnHeaders?: boolean;\n\t\tuse?: Middleware[];\n\t\tpath?: string;\n\t};\n\nexport const createInternalContext = async (\n\tcontext: InputContext<any, any>,\n\t{\n\t\toptions,\n\t\tpath,\n\t}: {\n\t\toptions: EndpointOptions;\n\t\tpath: string;\n\t},\n) => {\n\tconst headers = new Headers();\n\tconst { data, error } = await runValidation(options, context);\n\tif (error) {\n\t\tthrow new APIError(400, {\n\t\t\tmessage: error.message,\n\t\t\tcode: \"VALIDATION_ERROR\",\n\t\t});\n\t}\n\tconst requestHeaders: Headers | null =\n\t\t\"headers\" in context\n\t\t\t? context.headers instanceof Headers\n\t\t\t\t? context.headers\n\t\t\t\t: new Headers(context.headers)\n\t\t\t: \"request\" in context && context.request instanceof Request\n\t\t\t\t? context.request.headers\n\t\t\t\t: null;\n\tconst requestCookies = requestHeaders?.get(\"cookie\");\n\tconst parsedCookies = requestCookies ? parseCookies(requestCookies) : undefined;\n\tconst internalContext = {\n\t\t...context,\n\t\tbody: data.body,\n\t\tquery: data.query,\n\t\tpath: context.path || path,\n\t\tcontext: \"context\" in context && context.context ? context.context : {},\n\t\treturned: undefined as any,\n\t\theaders: context?.headers,\n\t\trequest: context?.request,\n\t\tparams: \"params\" in context ? context.params : undefined,\n\t\tmethod: context.method,\n\t\tsetHeader: (key: string, value: string) => {\n\t\t\theaders.set(key, value);\n\t\t},\n\t\tgetHeader: (key: string) => {\n\t\t\tif (!requestHeaders) return null;\n\t\t\treturn requestHeaders.get(key);\n\t\t},\n\t\tgetCookie: (key: string, prefix?: CookiePrefixOptions) => {\n\t\t\tconst finalKey = getCookieKey(key, prefix);\n\t\t\tif (!finalKey) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\treturn parsedCookies?.get(finalKey) || null;\n\t\t},\n\t\tgetSignedCookie: async (key: string, secret: string, prefix?: CookiePrefixOptions) => {\n\t\t\tconst finalKey = getCookieKey(key, prefix);\n\t\t\tif (!finalKey) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tconst value = parsedCookies?.get(finalKey);\n\t\t\tif (!value) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tconst signatureStartPos = value.lastIndexOf(\".\");\n\t\t\tif (signatureStartPos < 1) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tconst signedValue = value.substring(0, signatureStartPos);\n\t\t\tconst signature = value.substring(signatureStartPos + 1);\n\t\t\tif (signature.length !== 44 || !signature.endsWith(\"=\")) {\n\t\t\t\treturn null;\n\t\t\t}\n\t\t\tconst secretKey = await getCryptoKey(secret);\n\t\t\tconst isVerified = await verifySignature(signature, signedValue, secretKey);\n\t\t\treturn isVerified ? signedValue : false;\n\t\t},\n\t\tsetCookie: (key: string, value: string, options?: CookieOptions) => {\n\t\t\tconst cookie = serializeCookie(key, value, options);\n\t\t\theaders.append(\"set-cookie\", cookie);\n\t\t\treturn cookie;\n\t\t},\n\t\tsetSignedCookie: async (\n\t\t\tkey: string,\n\t\t\tvalue: string,\n\t\t\tsecret: string,\n\t\t\toptions?: CookieOptions,\n\t\t) => {\n\t\t\tconst cookie = await serializeSignedCookie(key, value, secret, options);\n\t\t\theaders.append(\"set-cookie\", cookie);\n\t\t\treturn cookie;\n\t\t},\n\t\tredirect: (url: string) => {\n\t\t\theaders.set(\"location\", url);\n\t\t\treturn new APIError(\"FOUND\", undefined, headers);\n\t\t},\n\t\terror: (\n\t\t\tstatus: keyof typeof _statusCode | Status,\n\t\t\tbody?:\n\t\t\t\t| {\n\t\t\t\t\t\tmessage?: string;\n\t\t\t\t\t\tcode?: string;\n\t\t\t\t  }\n\t\t\t\t| undefined,\n\t\t\theaders?: HeadersInit,\n\t\t) => {\n\t\t\treturn new APIError(status, body, headers);\n\t\t},\n\t\tjson: (\n\t\t\tjson: Record<string, any>,\n\t\t\trouterResponse?:\n\t\t\t\t| {\n\t\t\t\t\t\tstatus?: number;\n\t\t\t\t\t\theaders?: Record<string, string>;\n\t\t\t\t\t\tresponse?: Response;\n\t\t\t\t\t\tbody?: Record<string, any>;\n\t\t\t\t  }\n\t\t\t\t| Response,\n\t\t) => {\n\t\t\tif (!context.asResponse) {\n\t\t\t\treturn json;\n\t\t\t}\n\t\t\treturn {\n\t\t\t\tbody: routerResponse?.body || json,\n\t\t\t\trouterResponse,\n\t\t\t\t_flag: \"json\",\n\t\t\t};\n\t\t},\n\t\tresponseHeaders: headers,\n\t};\n\t//if context was shimmed through the input we want to apply it\n\tfor (const middleware of options.use || []) {\n\t\tconst response = (await middleware({\n\t\t\t...internalContext,\n\t\t\treturnHeaders: true,\n\t\t\tasResponse: false,\n\t\t})) as {\n\t\t\tresponse?: any;\n\t\t\theaders?: Headers;\n\t\t};\n\t\tif (response.response) {\n\t\t\tObject.assign(internalContext.context, response.response);\n\t\t}\n\t\t/**\n\t\t * Apply headers from the middleware to the endpoint headers\n\t\t */\n\t\tif (response.headers) {\n\t\t\tresponse.headers.forEach((value, key) => {\n\t\t\t\tinternalContext.responseHeaders.set(key, value);\n\t\t\t});\n\t\t}\n\t}\n\treturn internalContext;\n};\n", "import {\n\tcreateEndpoint,\n\ttype Endpoint,\n\ttype EndpointContext,\n\ttype EndpointOptions,\n} from \"./endpoint\";\nimport {\n\tcreateInternalContext,\n\ttype InferBody,\n\ttype InferBodyInput,\n\ttype InferHeaders,\n\ttype InferHeadersInput,\n\ttype InferMiddlewareBody,\n\ttype InferMiddlewareQuery,\n\ttype InferQuery,\n\ttype InferQueryInput,\n\ttype InferRequest,\n\ttype InferRequestInput,\n\ttype InferUse,\n\ttype InputContext,\n} from \"./context\";\nimport type { Prettify } from \"./helper\";\n\nexport interface MiddlewareOptions extends Omit<EndpointOptions, \"method\"> {}\n\nexport type MiddlewareResponse = null | void | undefined | Record<string, any>;\n\nexport type MiddlewareContext<Options extends MiddlewareOptions, Context = {}> = EndpointContext<\n\tstring,\n\tOptions & {\n\t\tmethod: \"*\";\n\t}\n> & {\n\t/**\n\t * Method\n\t *\n\t * The request method\n\t */\n\tmethod: string;\n\t/**\n\t * Path\n\t *\n\t * The path of the endpoint\n\t */\n\tpath: string;\n\t/**\n\t * Body\n\t *\n\t * The body object will be the parsed JSON from the request and validated\n\t * against the body schema if it exists\n\t */\n\tbody: InferMiddlewareBody<Options>;\n\t/**\n\t * Query\n\t *\n\t * The query object will be the parsed query string from the request\n\t * and validated against the query schema if it exists\n\t */\n\tquery: InferMiddlewareQuery<Options>;\n\t/**\n\t * Params\n\t *\n\t * If the path is `/user/:id` and the request is `/user/1` then the\n\t * params will\n\t * be `{ id: \"1\" }` and if the path includes a wildcard like `/user/*`\n\t * then the\n\t * params will be `{ _: \"1\" }` where `_` is the wildcard key. If the\n\t * wildcard\n\t * is named like `/user/**:name` then the params will be `{ name: string }`\n\t */\n\tparams: string;\n\t/**\n\t * Request object\n\t *\n\t * If `requireRequest` is set to true in the endpoint options this will be\n\t * required\n\t */\n\trequest: InferRequest<Options>;\n\t/**\n\t * Headers\n\t *\n\t * If `requireHeaders` is set to true in the endpoint options this will be\n\t * required\n\t */\n\theaders: InferHeaders<Options>;\n\t/**\n\t * Set header\n\t *\n\t * If it's called outside of a request it will just be ignored.\n\t */\n\tsetHeader: (key: string, value: string) => void;\n\t/**\n\t * Get header\n\t *\n\t * If it's called outside of a request it will just return null\n\t *\n\t * @param key  - The key of the header\n\t * @returns\n\t */\n\tgetHeader: (key: string) => string | null;\n\t/**\n\t * JSON\n\t *\n\t * a helper function to create a JSON response with\n\t * the correct headers\n\t * and status code. If `asResponse` is set to true in\n\t * the context then\n\t * it will return a Response object instead of the\n\t * JSON object.\n\t *\n\t * @param json - The JSON object to return\n\t * @param routerResponse - The response object to\n\t * return if `asResponse` is\n\t * true in the context this will take precedence\n\t */\n\tjson: <R extends Record<string, any> | null>(\n\t\tjson: R,\n\t\trouterResponse?:\n\t\t\t| {\n\t\t\t\t\tstatus?: number;\n\t\t\t\t\theaders?: Record<string, string>;\n\t\t\t\t\tresponse?: Response;\n\t\t\t  }\n\t\t\t| Response,\n\t) => Promise<R>;\n\t/**\n\t * Middleware context\n\t */\n\tcontext: Prettify<Context>;\n};\n\nexport function createMiddleware<Options extends MiddlewareOptions, R>(\n\toptions: Options,\n\thandler: (context: MiddlewareContext<Options>) => Promise<R>,\n): <InputCtx extends MiddlewareInputContext<Options>>(inputContext: InputCtx) => Promise<R>;\nexport function createMiddleware<Options extends MiddlewareOptions, R>(\n\thandler: (context: MiddlewareContext<Options>) => Promise<R>,\n): <InputCtx extends MiddlewareInputContext<Options>>(inputContext: InputCtx) => Promise<R>;\nexport function createMiddleware(optionsOrHandler: any, handler?: any) {\n\tconst internalHandler = async (inputCtx: InputContext<any, any>) => {\n\t\tconst context = inputCtx as InputContext<any, any>;\n\t\tconst _handler = typeof optionsOrHandler === \"function\" ? optionsOrHandler : handler;\n\t\tconst options = typeof optionsOrHandler === \"function\" ? {} : optionsOrHandler;\n\t\tconst internalContext = await createInternalContext(context, {\n\t\t\toptions,\n\t\t\tpath: \"/\",\n\t\t});\n\n\t\tif (!_handler) {\n\t\t\tthrow new Error(\"handler must be defined\");\n\t\t}\n\t\tconst response = await _handler(internalContext as any);\n\t\tconst headers = internalContext.responseHeaders;\n\t\treturn context.returnHeaders\n\t\t\t? {\n\t\t\t\t\theaders,\n\t\t\t\t\tresponse,\n\t\t\t\t}\n\t\t\t: response;\n\t};\n\tinternalHandler.options = typeof optionsOrHandler === \"function\" ? {} : optionsOrHandler;\n\treturn internalHandler;\n}\n\nexport type MiddlewareInputContext<Options extends MiddlewareOptions> = InferBodyInput<Options> &\n\tInferQueryInput<Options> &\n\tInferRequestInput<Options> &\n\tInferHeadersInput<Options> & {\n\t\tasResponse?: boolean;\n\t\treturnHeaders?: boolean;\n\t\tuse?: Middleware[];\n\t};\n\nexport type Middleware<\n\tOptions extends MiddlewareOptions = MiddlewareOptions,\n\tHandler extends (inputCtx: any) => Promise<any> = any,\n> = Handler & {\n\toptions: Options;\n};\n\ncreateMiddleware.create = <\n\tE extends {\n\t\tuse?: Middleware[];\n\t},\n>(\n\topts?: E,\n) => {\n\ttype InferredContext = InferUse<E[\"use\"]>;\n\tfunction fn<Options extends MiddlewareOptions, R>(\n\t\toptions: Options,\n\t\thandler: (ctx: MiddlewareContext<Options, InferredContext>) => Promise<R>,\n\t): (inputContext: MiddlewareInputContext<Options>) => Promise<R>;\n\tfunction fn<Options extends MiddlewareOptions, R>(\n\t\thandler: (ctx: MiddlewareContext<Options, InferredContext>) => Promise<R>,\n\t): (inputContext: MiddlewareInputContext<Options>) => Promise<R>;\n\tfunction fn(optionsOrHandler: any, handler?: any) {\n\t\tif (typeof optionsOrHandler === \"function\") {\n\t\t\treturn createMiddleware(\n\t\t\t\t{\n\t\t\t\t\tuse: opts?.use,\n\t\t\t\t},\n\t\t\t\toptionsOrHandler,\n\t\t\t);\n\t\t}\n\t\tif (!handler) {\n\t\t\tthrow new Error(\"Middleware handler is required\");\n\t\t}\n\t\tconst middleware = createMiddleware(\n\t\t\t{\n\t\t\t\t...optionsOrHandler,\n\t\t\t\tmethod: \"*\",\n\t\t\t\tuse: [...(opts?.use || []), ...(optionsOrHandler.use || [])],\n\t\t\t},\n\t\t\thandler,\n\t\t);\n\t\treturn middleware as any;\n\t}\n\treturn fn;\n};\n", "import type { <PERSON><PERSON>e<PERSON>d<PERSON><PERSON><PERSON>, Prettify } from \"./helper\";\nimport { toResponse } from \"./to-response\";\nimport { type Middleware } from \"./middleware\";\nimport {\n\tcreateInternalContext,\n\ttype InferBody,\n\ttype InferHeaders,\n\ttype InferMethod,\n\ttype InferParam,\n\ttype InferQuery,\n\ttype InferRequest,\n\ttype InferUse,\n\ttype InputContext,\n\ttype Method,\n} from \"./context\";\nimport type { CookieOptions, CookiePrefixOptions } from \"./cookies\";\nimport { APIError, type _statusCode, type Status } from \"./error\";\nimport type { OpenAPIParameter, OpenAPISchemaType } from \"./openapi\";\nimport type { StandardSchemaV1 } from \"./standard-schema\";\nimport { isAPIError } from \"./utils\";\n\nexport interface EndpointOptions {\n\t/**\n\t * Request Method\n\t */\n\tmethod: Method | Method[];\n\t/**\n\t * Body Schema\n\t */\n\tbody?: StandardSchemaV1;\n\t/**\n\t * Query Schema\n\t */\n\tquery?: StandardSchemaV1;\n\t/**\n\t * If true headers will be required to be passed in the context\n\t */\n\trequireHeaders?: boolean;\n\t/**\n\t * If true request object will be required\n\t */\n\trequireRequest?: boolean;\n\t/**\n\t * Clone the request object from the router\n\t */\n\tcloneRequest?: boolean;\n\t/**\n\t * If true the body will be undefined\n\t */\n\tdisableBody?: boolean;\n\t/**\n\t * Endpoint metadata\n\t */\n\tmetadata?: {\n\t\t/**\n\t\t * Open API definition\n\t\t */\n\t\topenapi?: {\n\t\t\tsummary?: string;\n\t\t\tdescription?: string;\n\t\t\ttags?: string[];\n\t\t\toperationId?: string;\n\t\t\tparameters?: OpenAPIParameter[];\n\t\t\trequestBody?: {\n\t\t\t\tcontent: {\n\t\t\t\t\t\"application/json\": {\n\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t};\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t\tresponses?: {\n\t\t\t\t[status: string]: {\n\t\t\t\t\tdescription: string;\n\t\t\t\t\tcontent?: {\n\t\t\t\t\t\t\"application/json\"?: {\n\t\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\"text/plain\"?: {\n\t\t\t\t\t\t\tschema?: {\n\t\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t};\n\t\t\t\t\t\t\"text/html\"?: {\n\t\t\t\t\t\t\tschema?: {\n\t\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t};\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t\t/**\n\t\t * Infer body and query type from ts interface\n\t\t *\n\t\t * useful for generic and dynamic types\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * const endpoint = createEndpoint(\"/path\", {\n\t\t * \t\tmethod: \"POST\",\n\t\t * \t\tbody: z.record(z.string()),\n\t\t * \t\t$Infer: {\n\t\t * \t\t\tbody: {} as {\n\t\t * \t\t\t\ttype: InferTypeFromOptions<Option> // custom type inference\n\t\t * \t\t\t}\n\t\t * \t\t}\n\t\t * \t}, async(ctx)=>{\n\t\t * \t\tconst body = ctx.body\n\t\t * \t})\n\t\t * ```\n\t\t */\n\t\t$Infer?: {\n\t\t\t/**\n\t\t\t * Body\n\t\t\t */\n\t\t\tbody?: any;\n\t\t\t/**\n\t\t\t * Query\n\t\t\t */\n\t\t\tquery?: Record<string, any>;\n\t\t};\n\t\t/**\n\t\t * If enabled, endpoint won't be exposed over a router\n\t\t */\n\t\tSERVER_ONLY?: boolean;\n\t\t/**\n\t\t * Extra metadata\n\t\t */\n\t\t[key: string]: any;\n\t};\n\t/**\n\t * List of middlewares to use\n\t */\n\tuse?: Middleware[];\n\t/**\n\t * A callback to run before any API error is throw or returned\n\t *\n\t * @param e - The API error\n\t * @returns - The response to return\n\t */\n\tonAPIError?: (e: APIError) => void | Promise<void>;\n}\n\nexport type EndpointContext<Path extends string, Options extends EndpointOptions, Context = {}> = {\n\t/**\n\t * Method\n\t *\n\t * The request method\n\t */\n\tmethod: InferMethod<Options>;\n\t/**\n\t * Path\n\t *\n\t * The path of the endpoint\n\t */\n\tpath: Path;\n\t/**\n\t * Body\n\t *\n\t * The body object will be the parsed JSON from the request and validated\n\t * against the body schema if it exists.\n\t */\n\tbody: InferBody<Options>;\n\t/**\n\t * Query\n\t *\n\t * The query object will be the parsed query string from the request\n\t * and validated against the query schema if it exists\n\t */\n\tquery: InferQuery<Options>;\n\t/**\n\t * Params\n\t *\n\t * If the path is `/user/:id` and the request is `/user/1` then the params will\n\t * be `{ id: \"1\" }` and if the path includes a wildcard like `/user/*` then the\n\t * params will be `{ _: \"1\" }` where `_` is the wildcard key. If the wildcard\n\t * is named like `/user/**:name` then the params will be `{ name: string }`\n\t */\n\tparams: InferParam<Path>;\n\t/**\n\t * Request object\n\t *\n\t * If `requireRequest` is set to true in the endpoint options this will be\n\t * required\n\t */\n\trequest: InferRequest<Options>;\n\t/**\n\t * Headers\n\t *\n\t * If `requireHeaders` is set to true in the endpoint options this will be\n\t * required\n\t */\n\theaders: InferHeaders<Options>;\n\t/**\n\t * Set header\n\t *\n\t * If it's called outside of a request it will just be ignored.\n\t */\n\tsetHeader: (key: string, value: string) => void;\n\t/**\n\t * Get header\n\t *\n\t * If it's called outside of a request it will just return null\n\t *\n\t * @param key  - The key of the header\n\t * @returns\n\t */\n\tgetHeader: (key: string) => string | null;\n\t/**\n\t * Get a cookie value from the request\n\t *\n\t * @param key - The key of the cookie\n\t * @param prefix - The prefix of the cookie between `__Secure-` and `__Host-`\n\t * @returns - The value of the cookie\n\t */\n\tgetCookie: (key: string, prefix?: CookiePrefixOptions) => string | null;\n\t/**\n\t * Get a signed cookie value from the request\n\t *\n\t * @param key - The key of the cookie\n\t * @param secret - The secret of the signed cookie\n\t * @param prefix - The prefix of the cookie between `__Secure-` and `__Host-`\n\t * @returns\n\t */\n\tgetSignedCookie: (\n\t\tkey: string,\n\t\tsecret: string,\n\t\tprefix?: CookiePrefixOptions,\n\t) => Promise<string | null>;\n\t/**\n\t * Set a cookie value in the response\n\t *\n\t * @param key - The key of the cookie\n\t * @param value - The value to set\n\t * @param options - The options of the cookie\n\t * @returns - The cookie string\n\t */\n\tsetCookie: (key: string, value: string, options?: CookieOptions) => string;\n\t/**\n\t * Set signed cookie\n\t *\n\t * @param key - The key of the cookie\n\t * @param value  - The value to set\n\t * @param secret - The secret to sign the cookie with\n\t * @param options - The options of the cookie\n\t * @returns - The cookie string\n\t */\n\tsetSignedCookie: (\n\t\tkey: string,\n\t\tvalue: string,\n\t\tsecret: string,\n\t\toptions?: CookieOptions,\n\t) => Promise<string>;\n\t/**\n\t * JSON\n\t *\n\t * a helper function to create a JSON response with\n\t * the correct headers\n\t * and status code. If `asResponse` is set to true in\n\t * the context then\n\t * it will return a Response object instead of the\n\t * JSON object.\n\t *\n\t * @param json - The JSON object to return\n\t * @param routerResponse - The response object to\n\t * return if `asResponse` is\n\t * true in the context this will take precedence\n\t */\n\tjson: <R extends Record<string, any> | null>(\n\t\tjson: R,\n\t\trouterResponse?:\n\t\t\t| {\n\t\t\t\t\tstatus?: number;\n\t\t\t\t\theaders?: Record<string, string>;\n\t\t\t\t\tresponse?: Response;\n\t\t\t\t\tbody?: Record<string, string>;\n\t\t\t  }\n\t\t\t| Response,\n\t) => Promise<R>;\n\t/**\n\t * Middleware context\n\t */\n\tcontext: Prettify<Context & InferUse<Options[\"use\"]>>;\n\t/**\n\t * Redirect to a new URL\n\t */\n\tredirect: (url: string) => APIError;\n\t/**\n\t * Return error\n\t */\n\terror: (\n\t\tstatus: keyof typeof _statusCode | Status,\n\t\tbody?: {\n\t\t\tmessage?: string;\n\t\t\tcode?: string;\n\t\t} & Record<string, any>,\n\t\theaders?: HeadersInit,\n\t) => APIError;\n};\n\nexport const createEndpoint = <Path extends string, Options extends EndpointOptions, R>(\n\tpath: Path,\n\toptions: Options,\n\thandler: (context: EndpointContext<Path, Options>) => Promise<R>,\n) => {\n\ttype Context = InputContext<Path, Options>;\n\tconst internalHandler = async <\n\t\tAsResponse extends boolean = false,\n\t\tReturnHeaders extends boolean = false,\n\t>(\n\t\t...inputCtx: HasRequiredKeys<Context> extends true\n\t\t\t? [Context & { asResponse?: AsResponse; returnHeaders?: ReturnHeaders }]\n\t\t\t: [(Context & { asResponse?: AsResponse; returnHeaders?: ReturnHeaders })?]\n\t) => {\n\t\tconst context = (inputCtx[0] || {}) as InputContext<any, any>;\n\t\tconst internalContext = await createInternalContext(context, {\n\t\t\toptions,\n\t\t\tpath,\n\t\t});\n\t\tconst response = await handler(internalContext as any).catch(async (e) => {\n\t\t\tif (isAPIError(e)) {\n\t\t\t\tconst onAPIError = options.onAPIError;\n\t\t\t\tif (onAPIError) {\n\t\t\t\t\tawait onAPIError(e);\n\t\t\t\t}\n\t\t\t\tif (context.asResponse) {\n\t\t\t\t\treturn e;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthrow e;\n\t\t});\n\t\tconst headers = internalContext.responseHeaders;\n\t\ttype ResultType = [AsResponse] extends [true]\n\t\t\t? Response\n\t\t\t: [ReturnHeaders] extends [true]\n\t\t\t\t? { headers: Headers; response: R }\n\t\t\t\t: R;\n\n\t\treturn (\n\t\t\tcontext.asResponse\n\t\t\t\t? toResponse(response, {\n\t\t\t\t\t\theaders,\n\t\t\t\t\t})\n\t\t\t\t: context.returnHeaders\n\t\t\t\t\t? {\n\t\t\t\t\t\t\theaders,\n\t\t\t\t\t\t\tresponse,\n\t\t\t\t\t\t}\n\t\t\t\t\t: response\n\t\t) as ResultType;\n\t};\n\tinternalHandler.options = options;\n\tinternalHandler.path = path;\n\treturn internalHandler;\n};\n\ncreateEndpoint.create = <E extends { use?: Middleware[] }>(opts?: E) => {\n\treturn <Path extends string, Opts extends EndpointOptions, R>(\n\t\tpath: Path,\n\t\toptions: Opts,\n\t\thandler: (ctx: EndpointContext<Path, Opts, InferUse<E[\"use\"]>>) => Promise<R>,\n\t) => {\n\t\treturn createEndpoint(\n\t\t\tpath,\n\t\t\t{\n\t\t\t\t...options,\n\t\t\t\tuse: [...(options?.use || []), ...(opts?.use || [])],\n\t\t\t},\n\t\t\thandler,\n\t\t);\n\t};\n};\n\nexport type Endpoint<\n\tPath extends string = string,\n\tOptions extends EndpointOptions = EndpointOptions,\n\tHandler extends (inputCtx: any) => Promise<any> = (inputCtx: any) => Promise<any>,\n> = Handler & {\n\toptions: Options;\n\tpath: Path;\n};\n", "import { createRouter as createRou3Router, addRoute, findRoute, findAllRout<PERSON> } from \"rou3\";\nimport { createEndpoint, type Endpoint } from \"./endpoint\";\nimport { generator, getHTML } from \"./openapi\";\nimport type { Middleware } from \"./middleware\";\nimport { getBody, isAPIError } from \"./utils\";\nimport { APIError } from \"./error\";\nimport { toResponse } from \"./to-response\";\n\nexport interface RouterConfig {\n\tthrowError?: boolean;\n\tonError?: (e: unknown) => void | Promise<void> | Response | Promise<Response>;\n\tbasePath?: string;\n\trouterMiddleware?: Array<{\n\t\tpath: string;\n\t\tmiddleware: Middleware;\n\t}>;\n\t/**\n\t * additional Context that needs to passed to endpoints\n\t *\n\t * this will be available on `ctx.context` on endpoints\n\t */\n\trouterContext?: Record<string, any>;\n\t/**\n\t * A callback to run before any response\n\t */\n\tonResponse?: (res: Response) => any | Promise<any>;\n\t/**\n\t * A callback to run before any request\n\t */\n\tonRequest?: (req: Request) => any | Promise<any>;\n\t/**\n\t * Open API route configuration\n\t */\n\topenapi?: {\n\t\t/**\n\t\t * Disable openapi route\n\t\t *\n\t\t * @default false\n\t\t */\n\t\tdisabled?: boolean;\n\t\t/**\n\t\t * A path to display open api using scalar\n\t\t *\n\t\t * @default \"/api/reference\"\n\t\t */\n\t\tpath?: string;\n\t\t/**\n\t\t * Scalar Configuration\n\t\t */\n\t\tscalar?: {\n\t\t\t/**\n\t\t\t * Title\n\t\t\t * @default \"Open API Reference\"\n\t\t\t */\n\t\t\ttitle?: string;\n\t\t\t/**\n\t\t\t * Description\n\t\t\t *\n\t\t\t * @default \"Better Call Open API Reference\"\n\t\t\t */\n\t\t\tdescription?: string;\n\t\t\t/**\n\t\t\t * Logo URL\n\t\t\t */\n\t\t\tlogo?: string;\n\t\t\t/**\n\t\t\t * Scalar theme\n\t\t\t * @default \"saturn\"\n\t\t\t */\n\t\t\ttheme?: string;\n\t\t};\n\t};\n}\n\nexport const createRouter = <E extends Record<string, Endpoint>, Config extends RouterConfig>(\n\tendpoints: E,\n\tconfig?: Config,\n) => {\n\tif (!config?.openapi?.disabled) {\n\t\tconst openapi = {\n\t\t\tpath: \"/api/reference\",\n\t\t\t...config?.openapi,\n\t\t};\n\t\t//@ts-expect-error\n\t\tendpoints[\"openapi\"] = createEndpoint(\n\t\t\topenapi.path,\n\t\t\t{\n\t\t\t\tmethod: \"GET\",\n\t\t\t},\n\t\t\tasync (c) => {\n\t\t\t\tconst schema = await generator(endpoints);\n\t\t\t\treturn new Response(getHTML(schema, openapi.scalar), {\n\t\t\t\t\theaders: {\n\t\t\t\t\t\t\"Content-Type\": \"text/html\",\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t},\n\t\t);\n\t}\n\tconst router = createRou3Router();\n\tconst middlewareRouter = createRou3Router();\n\n\tfor (const endpoint of Object.values(endpoints)) {\n\t\tif (!endpoint.options) {\n\t\t\tcontinue;\n\t\t}\n\t\tif (endpoint.options?.metadata?.SERVER_ONLY) continue;\n\n\t\tconst methods = Array.isArray(endpoint.options?.method)\n\t\t\t? endpoint.options.method\n\t\t\t: [endpoint.options?.method];\n\n\t\tfor (const method of methods) {\n\t\t\taddRoute(router, method, endpoint.path, endpoint);\n\t\t}\n\t}\n\n\tif (config?.routerMiddleware?.length) {\n\t\tfor (const { path, middleware } of config.routerMiddleware) {\n\t\t\taddRoute(middlewareRouter, \"*\", path, middleware);\n\t\t}\n\t}\n\n\tconst processRequest = async (request: Request) => {\n\t\tconst url = new URL(request.url);\n\t\tconst path = config?.basePath\n\t\t\t? url.pathname\n\t\t\t\t\t.split(config.basePath)\n\t\t\t\t\t.reduce((acc, curr, index) => {\n\t\t\t\t\t\tif (index !== 0) {\n\t\t\t\t\t\t\tif (index > 1) {\n\t\t\t\t\t\t\t\tacc.push(`${config.basePath}${curr}`);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tacc.push(curr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn acc;\n\t\t\t\t\t}, [] as string[])\n\t\t\t\t\t.join(\"\")\n\t\t\t: url.pathname;\n\n\t\tif (!path?.length) {\n\t\t\treturn new Response(null, { status: 404, statusText: \"Not Found\" });\n\t\t}\n\n\t\tconst route = findRoute(router, request.method, path);\n\t\tif (!route?.data) {\n\t\t\treturn new Response(null, { status: 404, statusText: \"Not Found\" });\n\t\t}\n\n\t\tconst query: Record<string, string | string[]> = {};\n\t\turl.searchParams.forEach((value, key) => {\n\t\t\tif (key in query) {\n\t\t\t\tif (Array.isArray(query[key])) {\n\t\t\t\t\t(query[key] as string[]).push(value);\n\t\t\t\t} else {\n\t\t\t\t\tquery[key] = [query[key] as string, value];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tquery[key] = value;\n\t\t\t}\n\t\t});\n\n\t\tconst handler = route.data as Endpoint;\n\t\tconst context = {\n\t\t\tpath,\n\t\t\tmethod: request.method as \"GET\",\n\t\t\theaders: request.headers,\n\t\t\tparams: route.params ? (JSON.parse(JSON.stringify(route.params)) as any) : {},\n\t\t\trequest: request,\n\t\t\tbody: handler.options.disableBody\n\t\t\t\t? undefined\n\t\t\t\t: await getBody(handler.options.cloneRequest ? request.clone() : request),\n\t\t\tquery,\n\t\t\t_flag: \"router\" as const,\n\t\t\tasResponse: true,\n\t\t\tcontext: config?.routerContext,\n\t\t};\n\n\t\ttry {\n\t\t\tconst middlewareRoutes = findAllRoutes(middlewareRouter, \"*\", path);\n\t\t\tif (middlewareRoutes?.length) {\n\t\t\t\tfor (const { data: middleware, params } of middlewareRoutes) {\n\t\t\t\t\tconst res = await (middleware as Endpoint)({\n\t\t\t\t\t\t...context,\n\t\t\t\t\t\tparams,\n\t\t\t\t\t\tasResponse: false,\n\t\t\t\t\t});\n\n\t\t\t\t\tif (res instanceof Response) return res;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst response = (await handler(context)) as Response;\n\t\t\treturn response;\n\t\t} catch (error) {\n\t\t\tif (isAPIError(error)) {\n\t\t\t\treturn toResponse(error);\n\t\t\t}\n\t\t\tconsole.error(`# SERVER_ERROR: `, error);\n\t\t\treturn new Response(null, {\n\t\t\t\tstatus: 500,\n\t\t\t\tstatusText: \"Internal Server Error\",\n\t\t\t});\n\t\t}\n\t};\n\n\treturn {\n\t\thandler: async (request: Request) => {\n\t\t\tconst onReq = await config?.onRequest?.(request);\n\t\t\tif (onReq instanceof Response) {\n\t\t\t\treturn onReq;\n\t\t\t}\n\t\t\tconst req = onReq instanceof Request ? onReq : request;\n\t\t\tconst res = await processRequest(req);\n\t\t\tconst onRes = await config?.onResponse?.(res);\n\t\t\tif (onRes instanceof Response) {\n\t\t\t\treturn onRes;\n\t\t\t}\n\t\t\treturn res;\n\t\t},\n\t\tendpoints,\n\t};\n};\n\nexport type Router = ReturnType<typeof createRouter>;\n", "var util;\n(function (util) {\n    util.assertEqual = (val) => val;\n    function assertIs(_arg) { }\n    util.assertIs = assertIs;\n    function assertNever(_x) {\n        throw new Error();\n    }\n    util.assertNever = assertNever;\n    util.arrayToEnum = (items) => {\n        const obj = {};\n        for (const item of items) {\n            obj[item] = item;\n        }\n        return obj;\n    };\n    util.getValidEnumValues = (obj) => {\n        const validKeys = util.objectKeys(obj).filter((k) => typeof obj[obj[k]] !== \"number\");\n        const filtered = {};\n        for (const k of validKeys) {\n            filtered[k] = obj[k];\n        }\n        return util.objectValues(filtered);\n    };\n    util.objectValues = (obj) => {\n        return util.objectKeys(obj).map(function (e) {\n            return obj[e];\n        });\n    };\n    util.objectKeys = typeof Object.keys === \"function\" // eslint-disable-line ban/ban\n        ? (obj) => Object.keys(obj) // eslint-disable-line ban/ban\n        : (object) => {\n            const keys = [];\n            for (const key in object) {\n                if (Object.prototype.hasOwnProperty.call(object, key)) {\n                    keys.push(key);\n                }\n            }\n            return keys;\n        };\n    util.find = (arr, checker) => {\n        for (const item of arr) {\n            if (checker(item))\n                return item;\n        }\n        return undefined;\n    };\n    util.isInteger = typeof Number.isInteger === \"function\"\n        ? (val) => Number.isInteger(val) // eslint-disable-line ban/ban\n        : (val) => typeof val === \"number\" && isFinite(val) && Math.floor(val) === val;\n    function joinValues(array, separator = \" | \") {\n        return array\n            .map((val) => (typeof val === \"string\" ? `'${val}'` : val))\n            .join(separator);\n    }\n    util.joinValues = joinValues;\n    util.jsonStringifyReplacer = (_, value) => {\n        if (typeof value === \"bigint\") {\n            return value.toString();\n        }\n        return value;\n    };\n})(util || (util = {}));\nvar objectUtil;\n(function (objectUtil) {\n    objectUtil.mergeShapes = (first, second) => {\n        return {\n            ...first,\n            ...second, // second overwrites first\n        };\n    };\n})(objectUtil || (objectUtil = {}));\nconst ZodParsedType = util.arrayToEnum([\n    \"string\",\n    \"nan\",\n    \"number\",\n    \"integer\",\n    \"float\",\n    \"boolean\",\n    \"date\",\n    \"bigint\",\n    \"symbol\",\n    \"function\",\n    \"undefined\",\n    \"null\",\n    \"array\",\n    \"object\",\n    \"unknown\",\n    \"promise\",\n    \"void\",\n    \"never\",\n    \"map\",\n    \"set\",\n]);\nconst getParsedType = (data) => {\n    const t = typeof data;\n    switch (t) {\n        case \"undefined\":\n            return ZodParsedType.undefined;\n        case \"string\":\n            return ZodParsedType.string;\n        case \"number\":\n            return isNaN(data) ? ZodParsedType.nan : ZodParsedType.number;\n        case \"boolean\":\n            return ZodParsedType.boolean;\n        case \"function\":\n            return ZodParsedType.function;\n        case \"bigint\":\n            return ZodParsedType.bigint;\n        case \"symbol\":\n            return ZodParsedType.symbol;\n        case \"object\":\n            if (Array.isArray(data)) {\n                return ZodParsedType.array;\n            }\n            if (data === null) {\n                return ZodParsedType.null;\n            }\n            if (data.then &&\n                typeof data.then === \"function\" &&\n                data.catch &&\n                typeof data.catch === \"function\") {\n                return ZodParsedType.promise;\n            }\n            if (typeof Map !== \"undefined\" && data instanceof Map) {\n                return ZodParsedType.map;\n            }\n            if (typeof Set !== \"undefined\" && data instanceof Set) {\n                return ZodParsedType.set;\n            }\n            if (typeof Date !== \"undefined\" && data instanceof Date) {\n                return ZodParsedType.date;\n            }\n            return ZodParsedType.object;\n        default:\n            return ZodParsedType.unknown;\n    }\n};\n\nconst ZodIssueCode = util.arrayToEnum([\n    \"invalid_type\",\n    \"invalid_literal\",\n    \"custom\",\n    \"invalid_union\",\n    \"invalid_union_discriminator\",\n    \"invalid_enum_value\",\n    \"unrecognized_keys\",\n    \"invalid_arguments\",\n    \"invalid_return_type\",\n    \"invalid_date\",\n    \"invalid_string\",\n    \"too_small\",\n    \"too_big\",\n    \"invalid_intersection_types\",\n    \"not_multiple_of\",\n    \"not_finite\",\n]);\nconst quotelessJson = (obj) => {\n    const json = JSON.stringify(obj, null, 2);\n    return json.replace(/\"([^\"]+)\":/g, \"$1:\");\n};\nclass ZodError extends Error {\n    get errors() {\n        return this.issues;\n    }\n    constructor(issues) {\n        super();\n        this.issues = [];\n        this.addIssue = (sub) => {\n            this.issues = [...this.issues, sub];\n        };\n        this.addIssues = (subs = []) => {\n            this.issues = [...this.issues, ...subs];\n        };\n        const actualProto = new.target.prototype;\n        if (Object.setPrototypeOf) {\n            // eslint-disable-next-line ban/ban\n            Object.setPrototypeOf(this, actualProto);\n        }\n        else {\n            this.__proto__ = actualProto;\n        }\n        this.name = \"ZodError\";\n        this.issues = issues;\n    }\n    format(_mapper) {\n        const mapper = _mapper ||\n            function (issue) {\n                return issue.message;\n            };\n        const fieldErrors = { _errors: [] };\n        const processError = (error) => {\n            for (const issue of error.issues) {\n                if (issue.code === \"invalid_union\") {\n                    issue.unionErrors.map(processError);\n                }\n                else if (issue.code === \"invalid_return_type\") {\n                    processError(issue.returnTypeError);\n                }\n                else if (issue.code === \"invalid_arguments\") {\n                    processError(issue.argumentsError);\n                }\n                else if (issue.path.length === 0) {\n                    fieldErrors._errors.push(mapper(issue));\n                }\n                else {\n                    let curr = fieldErrors;\n                    let i = 0;\n                    while (i < issue.path.length) {\n                        const el = issue.path[i];\n                        const terminal = i === issue.path.length - 1;\n                        if (!terminal) {\n                            curr[el] = curr[el] || { _errors: [] };\n                            // if (typeof el === \"string\") {\n                            //   curr[el] = curr[el] || { _errors: [] };\n                            // } else if (typeof el === \"number\") {\n                            //   const errorArray: any = [];\n                            //   errorArray._errors = [];\n                            //   curr[el] = curr[el] || errorArray;\n                            // }\n                        }\n                        else {\n                            curr[el] = curr[el] || { _errors: [] };\n                            curr[el]._errors.push(mapper(issue));\n                        }\n                        curr = curr[el];\n                        i++;\n                    }\n                }\n            }\n        };\n        processError(this);\n        return fieldErrors;\n    }\n    static assert(value) {\n        if (!(value instanceof ZodError)) {\n            throw new Error(`Not a ZodError: ${value}`);\n        }\n    }\n    toString() {\n        return this.message;\n    }\n    get message() {\n        return JSON.stringify(this.issues, util.jsonStringifyReplacer, 2);\n    }\n    get isEmpty() {\n        return this.issues.length === 0;\n    }\n    flatten(mapper = (issue) => issue.message) {\n        const fieldErrors = {};\n        const formErrors = [];\n        for (const sub of this.issues) {\n            if (sub.path.length > 0) {\n                fieldErrors[sub.path[0]] = fieldErrors[sub.path[0]] || [];\n                fieldErrors[sub.path[0]].push(mapper(sub));\n            }\n            else {\n                formErrors.push(mapper(sub));\n            }\n        }\n        return { formErrors, fieldErrors };\n    }\n    get formErrors() {\n        return this.flatten();\n    }\n}\nZodError.create = (issues) => {\n    const error = new ZodError(issues);\n    return error;\n};\n\nconst errorMap = (issue, _ctx) => {\n    let message;\n    switch (issue.code) {\n        case ZodIssueCode.invalid_type:\n            if (issue.received === ZodParsedType.undefined) {\n                message = \"Required\";\n            }\n            else {\n                message = `Expected ${issue.expected}, received ${issue.received}`;\n            }\n            break;\n        case ZodIssueCode.invalid_literal:\n            message = `Invalid literal value, expected ${JSON.stringify(issue.expected, util.jsonStringifyReplacer)}`;\n            break;\n        case ZodIssueCode.unrecognized_keys:\n            message = `Unrecognized key(s) in object: ${util.joinValues(issue.keys, \", \")}`;\n            break;\n        case ZodIssueCode.invalid_union:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_union_discriminator:\n            message = `Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;\n            break;\n        case ZodIssueCode.invalid_enum_value:\n            message = `Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;\n            break;\n        case ZodIssueCode.invalid_arguments:\n            message = `Invalid function arguments`;\n            break;\n        case ZodIssueCode.invalid_return_type:\n            message = `Invalid function return type`;\n            break;\n        case ZodIssueCode.invalid_date:\n            message = `Invalid date`;\n            break;\n        case ZodIssueCode.invalid_string:\n            if (typeof issue.validation === \"object\") {\n                if (\"includes\" in issue.validation) {\n                    message = `Invalid input: must include \"${issue.validation.includes}\"`;\n                    if (typeof issue.validation.position === \"number\") {\n                        message = `${message} at one or more positions greater than or equal to ${issue.validation.position}`;\n                    }\n                }\n                else if (\"startsWith\" in issue.validation) {\n                    message = `Invalid input: must start with \"${issue.validation.startsWith}\"`;\n                }\n                else if (\"endsWith\" in issue.validation) {\n                    message = `Invalid input: must end with \"${issue.validation.endsWith}\"`;\n                }\n                else {\n                    util.assertNever(issue.validation);\n                }\n            }\n            else if (issue.validation !== \"regex\") {\n                message = `Invalid ${issue.validation}`;\n            }\n            else {\n                message = \"Invalid\";\n            }\n            break;\n        case ZodIssueCode.too_small:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `more than`} ${issue.minimum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? \"exactly\" : issue.inclusive ? `at least` : `over`} ${issue.minimum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${issue.minimum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly equal to `\n                    : issue.inclusive\n                        ? `greater than or equal to `\n                        : `greater than `}${new Date(Number(issue.minimum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodIssueCode.too_big:\n            if (issue.type === \"array\")\n                message = `Array must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `less than`} ${issue.maximum} element(s)`;\n            else if (issue.type === \"string\")\n                message = `String must contain ${issue.exact ? `exactly` : issue.inclusive ? `at most` : `under`} ${issue.maximum} character(s)`;\n            else if (issue.type === \"number\")\n                message = `Number must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"bigint\")\n                message = `BigInt must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `less than or equal to`\n                        : `less than`} ${issue.maximum}`;\n            else if (issue.type === \"date\")\n                message = `Date must be ${issue.exact\n                    ? `exactly`\n                    : issue.inclusive\n                        ? `smaller than or equal to`\n                        : `smaller than`} ${new Date(Number(issue.maximum))}`;\n            else\n                message = \"Invalid input\";\n            break;\n        case ZodIssueCode.custom:\n            message = `Invalid input`;\n            break;\n        case ZodIssueCode.invalid_intersection_types:\n            message = `Intersection results could not be merged`;\n            break;\n        case ZodIssueCode.not_multiple_of:\n            message = `Number must be a multiple of ${issue.multipleOf}`;\n            break;\n        case ZodIssueCode.not_finite:\n            message = \"Number must be finite\";\n            break;\n        default:\n            message = _ctx.defaultError;\n            util.assertNever(issue);\n    }\n    return { message };\n};\n\nlet overrideErrorMap = errorMap;\nfunction setErrorMap(map) {\n    overrideErrorMap = map;\n}\nfunction getErrorMap() {\n    return overrideErrorMap;\n}\n\nconst makeIssue = (params) => {\n    const { data, path, errorMaps, issueData } = params;\n    const fullPath = [...path, ...(issueData.path || [])];\n    const fullIssue = {\n        ...issueData,\n        path: fullPath,\n    };\n    if (issueData.message !== undefined) {\n        return {\n            ...issueData,\n            path: fullPath,\n            message: issueData.message,\n        };\n    }\n    let errorMessage = \"\";\n    const maps = errorMaps\n        .filter((m) => !!m)\n        .slice()\n        .reverse();\n    for (const map of maps) {\n        errorMessage = map(fullIssue, { data, defaultError: errorMessage }).message;\n    }\n    return {\n        ...issueData,\n        path: fullPath,\n        message: errorMessage,\n    };\n};\nconst EMPTY_PATH = [];\nfunction addIssueToContext(ctx, issueData) {\n    const overrideMap = getErrorMap();\n    const issue = makeIssue({\n        issueData: issueData,\n        data: ctx.data,\n        path: ctx.path,\n        errorMaps: [\n            ctx.common.contextualErrorMap, // contextual error map is first priority\n            ctx.schemaErrorMap, // then schema-bound map if available\n            overrideMap, // then global override map\n            overrideMap === errorMap ? undefined : errorMap, // then global default map\n        ].filter((x) => !!x),\n    });\n    ctx.common.issues.push(issue);\n}\nclass ParseStatus {\n    constructor() {\n        this.value = \"valid\";\n    }\n    dirty() {\n        if (this.value === \"valid\")\n            this.value = \"dirty\";\n    }\n    abort() {\n        if (this.value !== \"aborted\")\n            this.value = \"aborted\";\n    }\n    static mergeArray(status, results) {\n        const arrayValue = [];\n        for (const s of results) {\n            if (s.status === \"aborted\")\n                return INVALID;\n            if (s.status === \"dirty\")\n                status.dirty();\n            arrayValue.push(s.value);\n        }\n        return { status: status.value, value: arrayValue };\n    }\n    static async mergeObjectAsync(status, pairs) {\n        const syncPairs = [];\n        for (const pair of pairs) {\n            const key = await pair.key;\n            const value = await pair.value;\n            syncPairs.push({\n                key,\n                value,\n            });\n        }\n        return ParseStatus.mergeObjectSync(status, syncPairs);\n    }\n    static mergeObjectSync(status, pairs) {\n        const finalObject = {};\n        for (const pair of pairs) {\n            const { key, value } = pair;\n            if (key.status === \"aborted\")\n                return INVALID;\n            if (value.status === \"aborted\")\n                return INVALID;\n            if (key.status === \"dirty\")\n                status.dirty();\n            if (value.status === \"dirty\")\n                status.dirty();\n            if (key.value !== \"__proto__\" &&\n                (typeof value.value !== \"undefined\" || pair.alwaysSet)) {\n                finalObject[key.value] = value.value;\n            }\n        }\n        return { status: status.value, value: finalObject };\n    }\n}\nconst INVALID = Object.freeze({\n    status: \"aborted\",\n});\nconst DIRTY = (value) => ({ status: \"dirty\", value });\nconst OK = (value) => ({ status: \"valid\", value });\nconst isAborted = (x) => x.status === \"aborted\";\nconst isDirty = (x) => x.status === \"dirty\";\nconst isValid = (x) => x.status === \"valid\";\nconst isAsync = (x) => typeof Promise !== \"undefined\" && x instanceof Promise;\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar errorUtil;\n(function (errorUtil) {\n    errorUtil.errToObj = (message) => typeof message === \"string\" ? { message } : message || {};\n    errorUtil.toString = (message) => typeof message === \"string\" ? message : message === null || message === void 0 ? void 0 : message.message;\n})(errorUtil || (errorUtil = {}));\n\nvar _ZodEnum_cache, _ZodNativeEnum_cache;\nclass ParseInputLazyPath {\n    constructor(parent, value, path, key) {\n        this._cachedPath = [];\n        this.parent = parent;\n        this.data = value;\n        this._path = path;\n        this._key = key;\n    }\n    get path() {\n        if (!this._cachedPath.length) {\n            if (this._key instanceof Array) {\n                this._cachedPath.push(...this._path, ...this._key);\n            }\n            else {\n                this._cachedPath.push(...this._path, this._key);\n            }\n        }\n        return this._cachedPath;\n    }\n}\nconst handleResult = (ctx, result) => {\n    if (isValid(result)) {\n        return { success: true, data: result.value };\n    }\n    else {\n        if (!ctx.common.issues.length) {\n            throw new Error(\"Validation failed but no issues detected.\");\n        }\n        return {\n            success: false,\n            get error() {\n                if (this._error)\n                    return this._error;\n                const error = new ZodError(ctx.common.issues);\n                this._error = error;\n                return this._error;\n            },\n        };\n    }\n};\nfunction processCreateParams(params) {\n    if (!params)\n        return {};\n    const { errorMap, invalid_type_error, required_error, description } = params;\n    if (errorMap && (invalid_type_error || required_error)) {\n        throw new Error(`Can't use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.`);\n    }\n    if (errorMap)\n        return { errorMap: errorMap, description };\n    const customMap = (iss, ctx) => {\n        var _a, _b;\n        const { message } = params;\n        if (iss.code === \"invalid_enum_value\") {\n            return { message: message !== null && message !== void 0 ? message : ctx.defaultError };\n        }\n        if (typeof ctx.data === \"undefined\") {\n            return { message: (_a = message !== null && message !== void 0 ? message : required_error) !== null && _a !== void 0 ? _a : ctx.defaultError };\n        }\n        if (iss.code !== \"invalid_type\")\n            return { message: ctx.defaultError };\n        return { message: (_b = message !== null && message !== void 0 ? message : invalid_type_error) !== null && _b !== void 0 ? _b : ctx.defaultError };\n    };\n    return { errorMap: customMap, description };\n}\nclass ZodType {\n    get description() {\n        return this._def.description;\n    }\n    _getType(input) {\n        return getParsedType(input.data);\n    }\n    _getOrReturnCtx(input, ctx) {\n        return (ctx || {\n            common: input.parent.common,\n            data: input.data,\n            parsedType: getParsedType(input.data),\n            schemaErrorMap: this._def.errorMap,\n            path: input.path,\n            parent: input.parent,\n        });\n    }\n    _processInputParams(input) {\n        return {\n            status: new ParseStatus(),\n            ctx: {\n                common: input.parent.common,\n                data: input.data,\n                parsedType: getParsedType(input.data),\n                schemaErrorMap: this._def.errorMap,\n                path: input.path,\n                parent: input.parent,\n            },\n        };\n    }\n    _parseSync(input) {\n        const result = this._parse(input);\n        if (isAsync(result)) {\n            throw new Error(\"Synchronous parse encountered promise.\");\n        }\n        return result;\n    }\n    _parseAsync(input) {\n        const result = this._parse(input);\n        return Promise.resolve(result);\n    }\n    parse(data, params) {\n        const result = this.safeParse(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    safeParse(data, params) {\n        var _a;\n        const ctx = {\n            common: {\n                issues: [],\n                async: (_a = params === null || params === void 0 ? void 0 : params.async) !== null && _a !== void 0 ? _a : false,\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data),\n        };\n        const result = this._parseSync({ data, path: ctx.path, parent: ctx });\n        return handleResult(ctx, result);\n    }\n    \"~validate\"(data) {\n        var _a, _b;\n        const ctx = {\n            common: {\n                issues: [],\n                async: !!this[\"~standard\"].async,\n            },\n            path: [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data),\n        };\n        if (!this[\"~standard\"].async) {\n            try {\n                const result = this._parseSync({ data, path: [], parent: ctx });\n                return isValid(result)\n                    ? {\n                        value: result.value,\n                    }\n                    : {\n                        issues: ctx.common.issues,\n                    };\n            }\n            catch (err) {\n                if ((_b = (_a = err === null || err === void 0 ? void 0 : err.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === null || _b === void 0 ? void 0 : _b.includes(\"encountered\")) {\n                    this[\"~standard\"].async = true;\n                }\n                ctx.common = {\n                    issues: [],\n                    async: true,\n                };\n            }\n        }\n        return this._parseAsync({ data, path: [], parent: ctx }).then((result) => isValid(result)\n            ? {\n                value: result.value,\n            }\n            : {\n                issues: ctx.common.issues,\n            });\n    }\n    async parseAsync(data, params) {\n        const result = await this.safeParseAsync(data, params);\n        if (result.success)\n            return result.data;\n        throw result.error;\n    }\n    async safeParseAsync(data, params) {\n        const ctx = {\n            common: {\n                issues: [],\n                contextualErrorMap: params === null || params === void 0 ? void 0 : params.errorMap,\n                async: true,\n            },\n            path: (params === null || params === void 0 ? void 0 : params.path) || [],\n            schemaErrorMap: this._def.errorMap,\n            parent: null,\n            data,\n            parsedType: getParsedType(data),\n        };\n        const maybeAsyncResult = this._parse({ data, path: ctx.path, parent: ctx });\n        const result = await (isAsync(maybeAsyncResult)\n            ? maybeAsyncResult\n            : Promise.resolve(maybeAsyncResult));\n        return handleResult(ctx, result);\n    }\n    refine(check, message) {\n        const getIssueProperties = (val) => {\n            if (typeof message === \"string\" || typeof message === \"undefined\") {\n                return { message };\n            }\n            else if (typeof message === \"function\") {\n                return message(val);\n            }\n            else {\n                return message;\n            }\n        };\n        return this._refinement((val, ctx) => {\n            const result = check(val);\n            const setError = () => ctx.addIssue({\n                code: ZodIssueCode.custom,\n                ...getIssueProperties(val),\n            });\n            if (typeof Promise !== \"undefined\" && result instanceof Promise) {\n                return result.then((data) => {\n                    if (!data) {\n                        setError();\n                        return false;\n                    }\n                    else {\n                        return true;\n                    }\n                });\n            }\n            if (!result) {\n                setError();\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    refinement(check, refinementData) {\n        return this._refinement((val, ctx) => {\n            if (!check(val)) {\n                ctx.addIssue(typeof refinementData === \"function\"\n                    ? refinementData(val, ctx)\n                    : refinementData);\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n    }\n    _refinement(refinement) {\n        return new ZodEffects({\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"refinement\", refinement },\n        });\n    }\n    superRefine(refinement) {\n        return this._refinement(refinement);\n    }\n    constructor(def) {\n        /** Alias of safeParseAsync */\n        this.spa = this.safeParseAsync;\n        this._def = def;\n        this.parse = this.parse.bind(this);\n        this.safeParse = this.safeParse.bind(this);\n        this.parseAsync = this.parseAsync.bind(this);\n        this.safeParseAsync = this.safeParseAsync.bind(this);\n        this.spa = this.spa.bind(this);\n        this.refine = this.refine.bind(this);\n        this.refinement = this.refinement.bind(this);\n        this.superRefine = this.superRefine.bind(this);\n        this.optional = this.optional.bind(this);\n        this.nullable = this.nullable.bind(this);\n        this.nullish = this.nullish.bind(this);\n        this.array = this.array.bind(this);\n        this.promise = this.promise.bind(this);\n        this.or = this.or.bind(this);\n        this.and = this.and.bind(this);\n        this.transform = this.transform.bind(this);\n        this.brand = this.brand.bind(this);\n        this.default = this.default.bind(this);\n        this.catch = this.catch.bind(this);\n        this.describe = this.describe.bind(this);\n        this.pipe = this.pipe.bind(this);\n        this.readonly = this.readonly.bind(this);\n        this.isNullable = this.isNullable.bind(this);\n        this.isOptional = this.isOptional.bind(this);\n        this[\"~standard\"] = {\n            version: 1,\n            vendor: \"zod\",\n            validate: (data) => this[\"~validate\"](data),\n        };\n    }\n    optional() {\n        return ZodOptional.create(this, this._def);\n    }\n    nullable() {\n        return ZodNullable.create(this, this._def);\n    }\n    nullish() {\n        return this.nullable().optional();\n    }\n    array() {\n        return ZodArray.create(this);\n    }\n    promise() {\n        return ZodPromise.create(this, this._def);\n    }\n    or(option) {\n        return ZodUnion.create([this, option], this._def);\n    }\n    and(incoming) {\n        return ZodIntersection.create(this, incoming, this._def);\n    }\n    transform(transform) {\n        return new ZodEffects({\n            ...processCreateParams(this._def),\n            schema: this,\n            typeName: ZodFirstPartyTypeKind.ZodEffects,\n            effect: { type: \"transform\", transform },\n        });\n    }\n    default(def) {\n        const defaultValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodDefault({\n            ...processCreateParams(this._def),\n            innerType: this,\n            defaultValue: defaultValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodDefault,\n        });\n    }\n    brand() {\n        return new ZodBranded({\n            typeName: ZodFirstPartyTypeKind.ZodBranded,\n            type: this,\n            ...processCreateParams(this._def),\n        });\n    }\n    catch(def) {\n        const catchValueFunc = typeof def === \"function\" ? def : () => def;\n        return new ZodCatch({\n            ...processCreateParams(this._def),\n            innerType: this,\n            catchValue: catchValueFunc,\n            typeName: ZodFirstPartyTypeKind.ZodCatch,\n        });\n    }\n    describe(description) {\n        const This = this.constructor;\n        return new This({\n            ...this._def,\n            description,\n        });\n    }\n    pipe(target) {\n        return ZodPipeline.create(this, target);\n    }\n    readonly() {\n        return ZodReadonly.create(this);\n    }\n    isOptional() {\n        return this.safeParse(undefined).success;\n    }\n    isNullable() {\n        return this.safeParse(null).success;\n    }\n}\nconst cuidRegex = /^c[^\\s-]{8,}$/i;\nconst cuid2Regex = /^[0-9a-z]+$/;\nconst ulidRegex = /^[0-9A-HJKMNP-TV-Z]{26}$/i;\n// const uuidRegex =\n//   /^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i;\nconst uuidRegex = /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/i;\nconst nanoidRegex = /^[a-z0-9_-]{21}$/i;\nconst jwtRegex = /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/;\nconst durationRegex = /^[-+]?P(?!$)(?:(?:[-+]?\\d+Y)|(?:[-+]?\\d+[.,]\\d+Y$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:(?:[-+]?\\d+W)|(?:[-+]?\\d+[.,]\\d+W$))?(?:(?:[-+]?\\d+D)|(?:[-+]?\\d+[.,]\\d+D$))?(?:T(?=[\\d+-])(?:(?:[-+]?\\d+H)|(?:[-+]?\\d+[.,]\\d+H$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:[-+]?\\d+(?:[.,]\\d+)?S)?)??$/;\n// from https://stackoverflow.com/a/46181/1550155\n// old version: too slow, didn't support unicode\n// const emailRegex = /^((([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+(\\.([a-z]|\\d|[!#\\$%&'\\*\\+\\-\\/=\\?\\^_`{\\|}~]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])+)*)|((\\x22)((((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(([\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x7f]|\\x21|[\\x23-\\x5b]|[\\x5d-\\x7e]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(\\\\([\\x01-\\x09\\x0b\\x0c\\x0d-\\x7f]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]))))*(((\\x20|\\x09)*(\\x0d\\x0a))?(\\x20|\\x09)+)?(\\x22)))@((([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|\\d|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))\\.)+(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])|(([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])([a-z]|\\d|-|\\.|_|~|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])*([a-z]|[\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF])))$/i;\n//old email regex\n// const emailRegex = /^(([^<>()[\\].,;:\\s@\"]+(\\.[^<>()[\\].,;:\\s@\"]+)*)|(\".+\"))@((?!-)([^<>()[\\].,;:\\s@\"]+\\.)+[^<>()[\\].,;:\\s@\"]{1,})[^-<>()[\\].,;:\\s@\"]$/i;\n// eslint-disable-next-line\n// const emailRegex =\n//   /^(([^<>()[\\]\\\\.,;:\\s@\\\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\\\"]+)*)|(\\\".+\\\"))@((\\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\])|(\\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\\.[A-Za-z]{2,})+))$/;\n// const emailRegex =\n//   /^[a-zA-Z0-9\\.\\!\\#\\$\\%\\&\\'\\*\\+\\/\\=\\?\\^\\_\\`\\{\\|\\}\\~\\-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n// const emailRegex =\n//   /^(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])$/i;\nconst emailRegex = /^(?!\\.)(?!.*\\.\\.)([A-Z0-9_'+\\-\\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\\-]*\\.)+[A-Z]{2,}$/i;\n// const emailRegex =\n//   /^[a-z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-z0-9-]+(?:\\.[a-z0-9\\-]+)*$/i;\n// from https://thekevinscott.com/emojis-in-javascript/#writing-a-regular-expression\nconst _emojiRegex = `^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$`;\nlet emojiRegex;\n// faster, simpler, safer\nconst ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/;\nconst ipv4CidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/;\n// const ipv6Regex =\n// /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;\nconst ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;\nconst ipv6CidrRegex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/;\n// https://stackoverflow.com/questions/7860392/determine-if-string-is-in-base64-using-javascript\nconst base64Regex = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/;\n// https://base64.guru/standards/base64url\nconst base64urlRegex = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/;\n// simple\n// const dateRegexSource = `\\\\d{4}-\\\\d{2}-\\\\d{2}`;\n// no leap year validation\n// const dateRegexSource = `\\\\d{4}-((0[13578]|10|12)-31|(0[13-9]|1[0-2])-30|(0[1-9]|1[0-2])-(0[1-9]|1\\\\d|2\\\\d))`;\n// with leap year validation\nconst dateRegexSource = `((\\\\d\\\\d[2468][048]|\\\\d\\\\d[13579][26]|\\\\d\\\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\\\d|30)|(02)-(0[1-9]|1\\\\d|2[0-8])))`;\nconst dateRegex = new RegExp(`^${dateRegexSource}$`);\nfunction timeRegexSource(args) {\n    // let regex = `\\\\d{2}:\\\\d{2}:\\\\d{2}`;\n    let regex = `([01]\\\\d|2[0-3]):[0-5]\\\\d:[0-5]\\\\d`;\n    if (args.precision) {\n        regex = `${regex}\\\\.\\\\d{${args.precision}}`;\n    }\n    else if (args.precision == null) {\n        regex = `${regex}(\\\\.\\\\d+)?`;\n    }\n    return regex;\n}\nfunction timeRegex(args) {\n    return new RegExp(`^${timeRegexSource(args)}$`);\n}\n// Adapted from https://stackoverflow.com/a/3143231\nfunction datetimeRegex(args) {\n    let regex = `${dateRegexSource}T${timeRegexSource(args)}`;\n    const opts = [];\n    opts.push(args.local ? `Z?` : `Z`);\n    if (args.offset)\n        opts.push(`([+-]\\\\d{2}:?\\\\d{2})`);\n    regex = `${regex}(${opts.join(\"|\")})`;\n    return new RegExp(`^${regex}$`);\n}\nfunction isValidIP(ip, version) {\n    if ((version === \"v4\" || !version) && ipv4Regex.test(ip)) {\n        return true;\n    }\n    if ((version === \"v6\" || !version) && ipv6Regex.test(ip)) {\n        return true;\n    }\n    return false;\n}\nfunction isValidJWT(jwt, alg) {\n    if (!jwtRegex.test(jwt))\n        return false;\n    try {\n        const [header] = jwt.split(\".\");\n        // Convert base64url to base64\n        const base64 = header\n            .replace(/-/g, \"+\")\n            .replace(/_/g, \"/\")\n            .padEnd(header.length + ((4 - (header.length % 4)) % 4), \"=\");\n        const decoded = JSON.parse(atob(base64));\n        if (typeof decoded !== \"object\" || decoded === null)\n            return false;\n        if (!decoded.typ || !decoded.alg)\n            return false;\n        if (alg && decoded.alg !== alg)\n            return false;\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction isValidCidr(ip, version) {\n    if ((version === \"v4\" || !version) && ipv4CidrRegex.test(ip)) {\n        return true;\n    }\n    if ((version === \"v6\" || !version) && ipv6CidrRegex.test(ip)) {\n        return true;\n    }\n    return false;\n}\nclass ZodString extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = String(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.string) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.string,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.length < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.length > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"string\",\n                        inclusive: true,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"length\") {\n                const tooBig = input.data.length > check.value;\n                const tooSmall = input.data.length < check.value;\n                if (tooBig || tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    if (tooBig) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_big,\n                            maximum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    else if (tooSmall) {\n                        addIssueToContext(ctx, {\n                            code: ZodIssueCode.too_small,\n                            minimum: check.value,\n                            type: \"string\",\n                            inclusive: true,\n                            exact: true,\n                            message: check.message,\n                        });\n                    }\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"email\") {\n                if (!emailRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"email\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"emoji\") {\n                if (!emojiRegex) {\n                    emojiRegex = new RegExp(_emojiRegex, \"u\");\n                }\n                if (!emojiRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"emoji\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"uuid\") {\n                if (!uuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"uuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"nanoid\") {\n                if (!nanoidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"nanoid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid\") {\n                if (!cuidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cuid2\") {\n                if (!cuid2Regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cuid2\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ulid\") {\n                if (!ulidRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ulid\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"url\") {\n                try {\n                    new URL(input.data);\n                }\n                catch (_a) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"url\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"regex\") {\n                check.regex.lastIndex = 0;\n                const testResult = check.regex.test(input.data);\n                if (!testResult) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"regex\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"trim\") {\n                input.data = input.data.trim();\n            }\n            else if (check.kind === \"includes\") {\n                if (!input.data.includes(check.value, check.position)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { includes: check.value, position: check.position },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"toLowerCase\") {\n                input.data = input.data.toLowerCase();\n            }\n            else if (check.kind === \"toUpperCase\") {\n                input.data = input.data.toUpperCase();\n            }\n            else if (check.kind === \"startsWith\") {\n                if (!input.data.startsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { startsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"endsWith\") {\n                if (!input.data.endsWith(check.value)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: { endsWith: check.value },\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"datetime\") {\n                const regex = datetimeRegex(check);\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: \"datetime\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"date\") {\n                const regex = dateRegex;\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: \"date\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"time\") {\n                const regex = timeRegex(check);\n                if (!regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_string,\n                        validation: \"time\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"duration\") {\n                if (!durationRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"duration\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"ip\") {\n                if (!isValidIP(input.data, check.version)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"ip\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"jwt\") {\n                if (!isValidJWT(input.data, check.alg)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"jwt\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"cidr\") {\n                if (!isValidCidr(input.data, check.version)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"cidr\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"base64\") {\n                if (!base64Regex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"base64\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"base64url\") {\n                if (!base64urlRegex.test(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        validation: \"base64url\",\n                        code: ZodIssueCode.invalid_string,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    _regex(regex, validation, message) {\n        return this.refinement((data) => regex.test(data), {\n            validation,\n            code: ZodIssueCode.invalid_string,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    _addCheck(check) {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    email(message) {\n        return this._addCheck({ kind: \"email\", ...errorUtil.errToObj(message) });\n    }\n    url(message) {\n        return this._addCheck({ kind: \"url\", ...errorUtil.errToObj(message) });\n    }\n    emoji(message) {\n        return this._addCheck({ kind: \"emoji\", ...errorUtil.errToObj(message) });\n    }\n    uuid(message) {\n        return this._addCheck({ kind: \"uuid\", ...errorUtil.errToObj(message) });\n    }\n    nanoid(message) {\n        return this._addCheck({ kind: \"nanoid\", ...errorUtil.errToObj(message) });\n    }\n    cuid(message) {\n        return this._addCheck({ kind: \"cuid\", ...errorUtil.errToObj(message) });\n    }\n    cuid2(message) {\n        return this._addCheck({ kind: \"cuid2\", ...errorUtil.errToObj(message) });\n    }\n    ulid(message) {\n        return this._addCheck({ kind: \"ulid\", ...errorUtil.errToObj(message) });\n    }\n    base64(message) {\n        return this._addCheck({ kind: \"base64\", ...errorUtil.errToObj(message) });\n    }\n    base64url(message) {\n        // base64url encoding is a modification of base64 that can safely be used in URLs and filenames\n        return this._addCheck({\n            kind: \"base64url\",\n            ...errorUtil.errToObj(message),\n        });\n    }\n    jwt(options) {\n        return this._addCheck({ kind: \"jwt\", ...errorUtil.errToObj(options) });\n    }\n    ip(options) {\n        return this._addCheck({ kind: \"ip\", ...errorUtil.errToObj(options) });\n    }\n    cidr(options) {\n        return this._addCheck({ kind: \"cidr\", ...errorUtil.errToObj(options) });\n    }\n    datetime(options) {\n        var _a, _b;\n        if (typeof options === \"string\") {\n            return this._addCheck({\n                kind: \"datetime\",\n                precision: null,\n                offset: false,\n                local: false,\n                message: options,\n            });\n        }\n        return this._addCheck({\n            kind: \"datetime\",\n            precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : false,\n            local: (_b = options === null || options === void 0 ? void 0 : options.local) !== null && _b !== void 0 ? _b : false,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    date(message) {\n        return this._addCheck({ kind: \"date\", message });\n    }\n    time(options) {\n        if (typeof options === \"string\") {\n            return this._addCheck({\n                kind: \"time\",\n                precision: null,\n                message: options,\n            });\n        }\n        return this._addCheck({\n            kind: \"time\",\n            precision: typeof (options === null || options === void 0 ? void 0 : options.precision) === \"undefined\" ? null : options === null || options === void 0 ? void 0 : options.precision,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    duration(message) {\n        return this._addCheck({ kind: \"duration\", ...errorUtil.errToObj(message) });\n    }\n    regex(regex, message) {\n        return this._addCheck({\n            kind: \"regex\",\n            regex: regex,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    includes(value, options) {\n        return this._addCheck({\n            kind: \"includes\",\n            value: value,\n            position: options === null || options === void 0 ? void 0 : options.position,\n            ...errorUtil.errToObj(options === null || options === void 0 ? void 0 : options.message),\n        });\n    }\n    startsWith(value, message) {\n        return this._addCheck({\n            kind: \"startsWith\",\n            value: value,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    endsWith(value, message) {\n        return this._addCheck({\n            kind: \"endsWith\",\n            value: value,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    min(minLength, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minLength,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    max(maxLength, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxLength,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    length(len, message) {\n        return this._addCheck({\n            kind: \"length\",\n            value: len,\n            ...errorUtil.errToObj(message),\n        });\n    }\n    /**\n     * Equivalent to `.min(1)`\n     */\n    nonempty(message) {\n        return this.min(1, errorUtil.errToObj(message));\n    }\n    trim() {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"trim\" }],\n        });\n    }\n    toLowerCase() {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toLowerCase\" }],\n        });\n    }\n    toUpperCase() {\n        return new ZodString({\n            ...this._def,\n            checks: [...this._def.checks, { kind: \"toUpperCase\" }],\n        });\n    }\n    get isDatetime() {\n        return !!this._def.checks.find((ch) => ch.kind === \"datetime\");\n    }\n    get isDate() {\n        return !!this._def.checks.find((ch) => ch.kind === \"date\");\n    }\n    get isTime() {\n        return !!this._def.checks.find((ch) => ch.kind === \"time\");\n    }\n    get isDuration() {\n        return !!this._def.checks.find((ch) => ch.kind === \"duration\");\n    }\n    get isEmail() {\n        return !!this._def.checks.find((ch) => ch.kind === \"email\");\n    }\n    get isURL() {\n        return !!this._def.checks.find((ch) => ch.kind === \"url\");\n    }\n    get isEmoji() {\n        return !!this._def.checks.find((ch) => ch.kind === \"emoji\");\n    }\n    get isUUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"uuid\");\n    }\n    get isNANOID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"nanoid\");\n    }\n    get isCUID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid\");\n    }\n    get isCUID2() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cuid2\");\n    }\n    get isULID() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ulid\");\n    }\n    get isIP() {\n        return !!this._def.checks.find((ch) => ch.kind === \"ip\");\n    }\n    get isCIDR() {\n        return !!this._def.checks.find((ch) => ch.kind === \"cidr\");\n    }\n    get isBase64() {\n        return !!this._def.checks.find((ch) => ch.kind === \"base64\");\n    }\n    get isBase64url() {\n        // base64url encoding is a modification of base64 that can safely be used in URLs and filenames\n        return !!this._def.checks.find((ch) => ch.kind === \"base64url\");\n    }\n    get minLength() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxLength() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodString.create = (params) => {\n    var _a;\n    return new ZodString({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodString,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\n// https://stackoverflow.com/questions/3966484/why-does-modulus-operator-return-fractional-number-in-javascript/31711034#31711034\nfunction floatSafeRemainder(val, step) {\n    const valDecCount = (val.toString().split(\".\")[1] || \"\").length;\n    const stepDecCount = (step.toString().split(\".\")[1] || \"\").length;\n    const decCount = valDecCount > stepDecCount ? valDecCount : stepDecCount;\n    const valInt = parseInt(val.toFixed(decCount).replace(\".\", \"\"));\n    const stepInt = parseInt(step.toFixed(decCount).replace(\".\", \"\"));\n    return (valInt % stepInt) / Math.pow(10, decCount);\n}\nclass ZodNumber extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n        this.step = this.multipleOf;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Number(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.number) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.number,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"int\") {\n                if (!util.isInteger(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.invalid_type,\n                        expected: \"integer\",\n                        received: \"float\",\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        minimum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        maximum: check.value,\n                        type: \"number\",\n                        inclusive: check.inclusive,\n                        exact: false,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (floatSafeRemainder(input.data, check.value) !== 0) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"finite\") {\n                if (!Number.isFinite(input.data)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_finite,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodNumber({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    int(message) {\n        return this._addCheck({\n            kind: \"int\",\n            message: errorUtil.toString(message),\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: 0,\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value: value,\n            message: errorUtil.toString(message),\n        });\n    }\n    finite(message) {\n        return this._addCheck({\n            kind: \"finite\",\n            message: errorUtil.toString(message),\n        });\n    }\n    safe(message) {\n        return this._addCheck({\n            kind: \"min\",\n            inclusive: true,\n            value: Number.MIN_SAFE_INTEGER,\n            message: errorUtil.toString(message),\n        })._addCheck({\n            kind: \"max\",\n            inclusive: true,\n            value: Number.MAX_SAFE_INTEGER,\n            message: errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n    get isInt() {\n        return !!this._def.checks.find((ch) => ch.kind === \"int\" ||\n            (ch.kind === \"multipleOf\" && util.isInteger(ch.value)));\n    }\n    get isFinite() {\n        let max = null, min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"finite\" ||\n                ch.kind === \"int\" ||\n                ch.kind === \"multipleOf\") {\n                return true;\n            }\n            else if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n            else if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return Number.isFinite(min) && Number.isFinite(max);\n    }\n}\nZodNumber.create = (params) => {\n    return new ZodNumber({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodNumber,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBigInt extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.min = this.gte;\n        this.max = this.lte;\n    }\n    _parse(input) {\n        if (this._def.coerce) {\n            try {\n                input.data = BigInt(input.data);\n            }\n            catch (_a) {\n                return this._getInvalidInput(input);\n            }\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.bigint) {\n            return this._getInvalidInput(input);\n        }\n        let ctx = undefined;\n        const status = new ParseStatus();\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                const tooSmall = check.inclusive\n                    ? input.data < check.value\n                    : input.data <= check.value;\n                if (tooSmall) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        type: \"bigint\",\n                        minimum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                const tooBig = check.inclusive\n                    ? input.data > check.value\n                    : input.data >= check.value;\n                if (tooBig) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        type: \"bigint\",\n                        maximum: check.value,\n                        inclusive: check.inclusive,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"multipleOf\") {\n                if (input.data % check.value !== BigInt(0)) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.not_multiple_of,\n                        multipleOf: check.value,\n                        message: check.message,\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return { status: status.value, value: input.data };\n    }\n    _getInvalidInput(input) {\n        const ctx = this._getOrReturnCtx(input);\n        addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_type,\n            expected: ZodParsedType.bigint,\n            received: ctx.parsedType,\n        });\n        return INVALID;\n    }\n    gte(value, message) {\n        return this.setLimit(\"min\", value, true, errorUtil.toString(message));\n    }\n    gt(value, message) {\n        return this.setLimit(\"min\", value, false, errorUtil.toString(message));\n    }\n    lte(value, message) {\n        return this.setLimit(\"max\", value, true, errorUtil.toString(message));\n    }\n    lt(value, message) {\n        return this.setLimit(\"max\", value, false, errorUtil.toString(message));\n    }\n    setLimit(kind, value, inclusive, message) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [\n                ...this._def.checks,\n                {\n                    kind,\n                    value,\n                    inclusive,\n                    message: errorUtil.toString(message),\n                },\n            ],\n        });\n    }\n    _addCheck(check) {\n        return new ZodBigInt({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    positive(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    negative(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: false,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonpositive(message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    nonnegative(message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: BigInt(0),\n            inclusive: true,\n            message: errorUtil.toString(message),\n        });\n    }\n    multipleOf(value, message) {\n        return this._addCheck({\n            kind: \"multipleOf\",\n            value,\n            message: errorUtil.toString(message),\n        });\n    }\n    get minValue() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min;\n    }\n    get maxValue() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max;\n    }\n}\nZodBigInt.create = (params) => {\n    var _a;\n    return new ZodBigInt({\n        checks: [],\n        typeName: ZodFirstPartyTypeKind.ZodBigInt,\n        coerce: (_a = params === null || params === void 0 ? void 0 : params.coerce) !== null && _a !== void 0 ? _a : false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodBoolean extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = Boolean(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.boolean) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.boolean,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodBoolean.create = (params) => {\n    return new ZodBoolean({\n        typeName: ZodFirstPartyTypeKind.ZodBoolean,\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDate extends ZodType {\n    _parse(input) {\n        if (this._def.coerce) {\n            input.data = new Date(input.data);\n        }\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.date) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.date,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (isNaN(input.data.getTime())) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_date,\n            });\n            return INVALID;\n        }\n        const status = new ParseStatus();\n        let ctx = undefined;\n        for (const check of this._def.checks) {\n            if (check.kind === \"min\") {\n                if (input.data.getTime() < check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_small,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        minimum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else if (check.kind === \"max\") {\n                if (input.data.getTime() > check.value) {\n                    ctx = this._getOrReturnCtx(input, ctx);\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.too_big,\n                        message: check.message,\n                        inclusive: true,\n                        exact: false,\n                        maximum: check.value,\n                        type: \"date\",\n                    });\n                    status.dirty();\n                }\n            }\n            else {\n                util.assertNever(check);\n            }\n        }\n        return {\n            status: status.value,\n            value: new Date(input.data.getTime()),\n        };\n    }\n    _addCheck(check) {\n        return new ZodDate({\n            ...this._def,\n            checks: [...this._def.checks, check],\n        });\n    }\n    min(minDate, message) {\n        return this._addCheck({\n            kind: \"min\",\n            value: minDate.getTime(),\n            message: errorUtil.toString(message),\n        });\n    }\n    max(maxDate, message) {\n        return this._addCheck({\n            kind: \"max\",\n            value: maxDate.getTime(),\n            message: errorUtil.toString(message),\n        });\n    }\n    get minDate() {\n        let min = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"min\") {\n                if (min === null || ch.value > min)\n                    min = ch.value;\n            }\n        }\n        return min != null ? new Date(min) : null;\n    }\n    get maxDate() {\n        let max = null;\n        for (const ch of this._def.checks) {\n            if (ch.kind === \"max\") {\n                if (max === null || ch.value < max)\n                    max = ch.value;\n            }\n        }\n        return max != null ? new Date(max) : null;\n    }\n}\nZodDate.create = (params) => {\n    return new ZodDate({\n        checks: [],\n        coerce: (params === null || params === void 0 ? void 0 : params.coerce) || false,\n        typeName: ZodFirstPartyTypeKind.ZodDate,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSymbol extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.symbol) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.symbol,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodSymbol.create = (params) => {\n    return new ZodSymbol({\n        typeName: ZodFirstPartyTypeKind.ZodSymbol,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUndefined extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.undefined,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodUndefined.create = (params) => {\n    return new ZodUndefined({\n        typeName: ZodFirstPartyTypeKind.ZodUndefined,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNull extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.null) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.null,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodNull.create = (params) => {\n    return new ZodNull({\n        typeName: ZodFirstPartyTypeKind.ZodNull,\n        ...processCreateParams(params),\n    });\n};\nclass ZodAny extends ZodType {\n    constructor() {\n        super(...arguments);\n        // to prevent instances of other classes from extending ZodAny. this causes issues with catchall in ZodObject.\n        this._any = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodAny.create = (params) => {\n    return new ZodAny({\n        typeName: ZodFirstPartyTypeKind.ZodAny,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnknown extends ZodType {\n    constructor() {\n        super(...arguments);\n        // required\n        this._unknown = true;\n    }\n    _parse(input) {\n        return OK(input.data);\n    }\n}\nZodUnknown.create = (params) => {\n    return new ZodUnknown({\n        typeName: ZodFirstPartyTypeKind.ZodUnknown,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNever extends ZodType {\n    _parse(input) {\n        const ctx = this._getOrReturnCtx(input);\n        addIssueToContext(ctx, {\n            code: ZodIssueCode.invalid_type,\n            expected: ZodParsedType.never,\n            received: ctx.parsedType,\n        });\n        return INVALID;\n    }\n}\nZodNever.create = (params) => {\n    return new ZodNever({\n        typeName: ZodFirstPartyTypeKind.ZodNever,\n        ...processCreateParams(params),\n    });\n};\nclass ZodVoid extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.undefined) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.void,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n}\nZodVoid.create = (params) => {\n    return new ZodVoid({\n        typeName: ZodFirstPartyTypeKind.ZodVoid,\n        ...processCreateParams(params),\n    });\n};\nclass ZodArray extends ZodType {\n    _parse(input) {\n        const { ctx, status } = this._processInputParams(input);\n        const def = this._def;\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (def.exactLength !== null) {\n            const tooBig = ctx.data.length > def.exactLength.value;\n            const tooSmall = ctx.data.length < def.exactLength.value;\n            if (tooBig || tooSmall) {\n                addIssueToContext(ctx, {\n                    code: tooBig ? ZodIssueCode.too_big : ZodIssueCode.too_small,\n                    minimum: (tooSmall ? def.exactLength.value : undefined),\n                    maximum: (tooBig ? def.exactLength.value : undefined),\n                    type: \"array\",\n                    inclusive: true,\n                    exact: true,\n                    message: def.exactLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.minLength !== null) {\n            if (ctx.data.length < def.minLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxLength !== null) {\n            if (ctx.data.length > def.maxLength.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxLength.value,\n                    type: \"array\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxLength.message,\n                });\n                status.dirty();\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.all([...ctx.data].map((item, i) => {\n                return def.type._parseAsync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n            })).then((result) => {\n                return ParseStatus.mergeArray(status, result);\n            });\n        }\n        const result = [...ctx.data].map((item, i) => {\n            return def.type._parseSync(new ParseInputLazyPath(ctx, item, ctx.path, i));\n        });\n        return ParseStatus.mergeArray(status, result);\n    }\n    get element() {\n        return this._def.type;\n    }\n    min(minLength, message) {\n        return new ZodArray({\n            ...this._def,\n            minLength: { value: minLength, message: errorUtil.toString(message) },\n        });\n    }\n    max(maxLength, message) {\n        return new ZodArray({\n            ...this._def,\n            maxLength: { value: maxLength, message: errorUtil.toString(message) },\n        });\n    }\n    length(len, message) {\n        return new ZodArray({\n            ...this._def,\n            exactLength: { value: len, message: errorUtil.toString(message) },\n        });\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodArray.create = (schema, params) => {\n    return new ZodArray({\n        type: schema,\n        minLength: null,\n        maxLength: null,\n        exactLength: null,\n        typeName: ZodFirstPartyTypeKind.ZodArray,\n        ...processCreateParams(params),\n    });\n};\nfunction deepPartialify(schema) {\n    if (schema instanceof ZodObject) {\n        const newShape = {};\n        for (const key in schema.shape) {\n            const fieldSchema = schema.shape[key];\n            newShape[key] = ZodOptional.create(deepPartialify(fieldSchema));\n        }\n        return new ZodObject({\n            ...schema._def,\n            shape: () => newShape,\n        });\n    }\n    else if (schema instanceof ZodArray) {\n        return new ZodArray({\n            ...schema._def,\n            type: deepPartialify(schema.element),\n        });\n    }\n    else if (schema instanceof ZodOptional) {\n        return ZodOptional.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodNullable) {\n        return ZodNullable.create(deepPartialify(schema.unwrap()));\n    }\n    else if (schema instanceof ZodTuple) {\n        return ZodTuple.create(schema.items.map((item) => deepPartialify(item)));\n    }\n    else {\n        return schema;\n    }\n}\nclass ZodObject extends ZodType {\n    constructor() {\n        super(...arguments);\n        this._cached = null;\n        /**\n         * @deprecated In most cases, this is no longer needed - unknown properties are now silently stripped.\n         * If you want to pass through unknown properties, use `.passthrough()` instead.\n         */\n        this.nonstrict = this.passthrough;\n        // extend<\n        //   Augmentation extends ZodRawShape,\n        //   NewOutput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_output\"]\n        //       : k extends keyof Output\n        //       ? Output[k]\n        //       : never;\n        //   }>,\n        //   NewInput extends util.flatten<{\n        //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n        //       ? Augmentation[k][\"_input\"]\n        //       : k extends keyof Input\n        //       ? Input[k]\n        //       : never;\n        //   }>\n        // >(\n        //   augmentation: Augmentation\n        // ): ZodObject<\n        //   extendShape<T, Augmentation>,\n        //   UnknownKeys,\n        //   Catchall,\n        //   NewOutput,\n        //   NewInput\n        // > {\n        //   return new ZodObject({\n        //     ...this._def,\n        //     shape: () => ({\n        //       ...this._def.shape(),\n        //       ...augmentation,\n        //     }),\n        //   }) as any;\n        // }\n        /**\n         * @deprecated Use `.extend` instead\n         *  */\n        this.augment = this.extend;\n    }\n    _getCached() {\n        if (this._cached !== null)\n            return this._cached;\n        const shape = this._def.shape();\n        const keys = util.objectKeys(shape);\n        return (this._cached = { shape, keys });\n    }\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.object) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const { status, ctx } = this._processInputParams(input);\n        const { shape, keys: shapeKeys } = this._getCached();\n        const extraKeys = [];\n        if (!(this._def.catchall instanceof ZodNever &&\n            this._def.unknownKeys === \"strip\")) {\n            for (const key in ctx.data) {\n                if (!shapeKeys.includes(key)) {\n                    extraKeys.push(key);\n                }\n            }\n        }\n        const pairs = [];\n        for (const key of shapeKeys) {\n            const keyValidator = shape[key];\n            const value = ctx.data[key];\n            pairs.push({\n                key: { status: \"valid\", value: key },\n                value: keyValidator._parse(new ParseInputLazyPath(ctx, value, ctx.path, key)),\n                alwaysSet: key in ctx.data,\n            });\n        }\n        if (this._def.catchall instanceof ZodNever) {\n            const unknownKeys = this._def.unknownKeys;\n            if (unknownKeys === \"passthrough\") {\n                for (const key of extraKeys) {\n                    pairs.push({\n                        key: { status: \"valid\", value: key },\n                        value: { status: \"valid\", value: ctx.data[key] },\n                    });\n                }\n            }\n            else if (unknownKeys === \"strict\") {\n                if (extraKeys.length > 0) {\n                    addIssueToContext(ctx, {\n                        code: ZodIssueCode.unrecognized_keys,\n                        keys: extraKeys,\n                    });\n                    status.dirty();\n                }\n            }\n            else if (unknownKeys === \"strip\") ;\n            else {\n                throw new Error(`Internal ZodObject error: invalid unknownKeys value.`);\n            }\n        }\n        else {\n            // run catchall validation\n            const catchall = this._def.catchall;\n            for (const key of extraKeys) {\n                const value = ctx.data[key];\n                pairs.push({\n                    key: { status: \"valid\", value: key },\n                    value: catchall._parse(new ParseInputLazyPath(ctx, value, ctx.path, key) //, ctx.child(key), value, getParsedType(value)\n                    ),\n                    alwaysSet: key in ctx.data,\n                });\n            }\n        }\n        if (ctx.common.async) {\n            return Promise.resolve()\n                .then(async () => {\n                const syncPairs = [];\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    const value = await pair.value;\n                    syncPairs.push({\n                        key,\n                        value,\n                        alwaysSet: pair.alwaysSet,\n                    });\n                }\n                return syncPairs;\n            })\n                .then((syncPairs) => {\n                return ParseStatus.mergeObjectSync(status, syncPairs);\n            });\n        }\n        else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get shape() {\n        return this._def.shape();\n    }\n    strict(message) {\n        errorUtil.errToObj;\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strict\",\n            ...(message !== undefined\n                ? {\n                    errorMap: (issue, ctx) => {\n                        var _a, _b, _c, _d;\n                        const defaultError = (_c = (_b = (_a = this._def).errorMap) === null || _b === void 0 ? void 0 : _b.call(_a, issue, ctx).message) !== null && _c !== void 0 ? _c : ctx.defaultError;\n                        if (issue.code === \"unrecognized_keys\")\n                            return {\n                                message: (_d = errorUtil.errToObj(message).message) !== null && _d !== void 0 ? _d : defaultError,\n                            };\n                        return {\n                            message: defaultError,\n                        };\n                    },\n                }\n                : {}),\n        });\n    }\n    strip() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"strip\",\n        });\n    }\n    passthrough() {\n        return new ZodObject({\n            ...this._def,\n            unknownKeys: \"passthrough\",\n        });\n    }\n    // const AugmentFactory =\n    //   <Def extends ZodObjectDef>(def: Def) =>\n    //   <Augmentation extends ZodRawShape>(\n    //     augmentation: Augmentation\n    //   ): ZodObject<\n    //     extendShape<ReturnType<Def[\"shape\"]>, Augmentation>,\n    //     Def[\"unknownKeys\"],\n    //     Def[\"catchall\"]\n    //   > => {\n    //     return new ZodObject({\n    //       ...def,\n    //       shape: () => ({\n    //         ...def.shape(),\n    //         ...augmentation,\n    //       }),\n    //     }) as any;\n    //   };\n    extend(augmentation) {\n        return new ZodObject({\n            ...this._def,\n            shape: () => ({\n                ...this._def.shape(),\n                ...augmentation,\n            }),\n        });\n    }\n    /**\n     * Prior to zod@1.0.12 there was a bug in the\n     * inferred type of merged objects. Please\n     * upgrade if you are experiencing issues.\n     */\n    merge(merging) {\n        const merged = new ZodObject({\n            unknownKeys: merging._def.unknownKeys,\n            catchall: merging._def.catchall,\n            shape: () => ({\n                ...this._def.shape(),\n                ...merging._def.shape(),\n            }),\n            typeName: ZodFirstPartyTypeKind.ZodObject,\n        });\n        return merged;\n    }\n    // merge<\n    //   Incoming extends AnyZodObject,\n    //   Augmentation extends Incoming[\"shape\"],\n    //   NewOutput extends {\n    //     [k in keyof Augmentation | keyof Output]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_output\"]\n    //       : k extends keyof Output\n    //       ? Output[k]\n    //       : never;\n    //   },\n    //   NewInput extends {\n    //     [k in keyof Augmentation | keyof Input]: k extends keyof Augmentation\n    //       ? Augmentation[k][\"_input\"]\n    //       : k extends keyof Input\n    //       ? Input[k]\n    //       : never;\n    //   }\n    // >(\n    //   merging: Incoming\n    // ): ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"],\n    //   NewOutput,\n    //   NewInput\n    // > {\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    setKey(key, schema) {\n        return this.augment({ [key]: schema });\n    }\n    // merge<Incoming extends AnyZodObject>(\n    //   merging: Incoming\n    // ): //ZodObject<T & Incoming[\"_shape\"], UnknownKeys, Catchall> = (merging) => {\n    // ZodObject<\n    //   extendShape<T, ReturnType<Incoming[\"_def\"][\"shape\"]>>,\n    //   Incoming[\"_def\"][\"unknownKeys\"],\n    //   Incoming[\"_def\"][\"catchall\"]\n    // > {\n    //   // const mergedShape = objectUtil.mergeShapes(\n    //   //   this._def.shape(),\n    //   //   merging._def.shape()\n    //   // );\n    //   const merged: any = new ZodObject({\n    //     unknownKeys: merging._def.unknownKeys,\n    //     catchall: merging._def.catchall,\n    //     shape: () =>\n    //       objectUtil.mergeShapes(this._def.shape(), merging._def.shape()),\n    //     typeName: ZodFirstPartyTypeKind.ZodObject,\n    //   }) as any;\n    //   return merged;\n    // }\n    catchall(index) {\n        return new ZodObject({\n            ...this._def,\n            catchall: index,\n        });\n    }\n    pick(mask) {\n        const shape = {};\n        util.objectKeys(mask).forEach((key) => {\n            if (mask[key] && this.shape[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    omit(mask) {\n        const shape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            if (!mask[key]) {\n                shape[key] = this.shape[key];\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => shape,\n        });\n    }\n    /**\n     * @deprecated\n     */\n    deepPartial() {\n        return deepPartialify(this);\n    }\n    partial(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            const fieldSchema = this.shape[key];\n            if (mask && !mask[key]) {\n                newShape[key] = fieldSchema;\n            }\n            else {\n                newShape[key] = fieldSchema.optional();\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    required(mask) {\n        const newShape = {};\n        util.objectKeys(this.shape).forEach((key) => {\n            if (mask && !mask[key]) {\n                newShape[key] = this.shape[key];\n            }\n            else {\n                const fieldSchema = this.shape[key];\n                let newField = fieldSchema;\n                while (newField instanceof ZodOptional) {\n                    newField = newField._def.innerType;\n                }\n                newShape[key] = newField;\n            }\n        });\n        return new ZodObject({\n            ...this._def,\n            shape: () => newShape,\n        });\n    }\n    keyof() {\n        return createZodEnum(util.objectKeys(this.shape));\n    }\n}\nZodObject.create = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.strictCreate = (shape, params) => {\n    return new ZodObject({\n        shape: () => shape,\n        unknownKeys: \"strict\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nZodObject.lazycreate = (shape, params) => {\n    return new ZodObject({\n        shape,\n        unknownKeys: \"strip\",\n        catchall: ZodNever.create(),\n        typeName: ZodFirstPartyTypeKind.ZodObject,\n        ...processCreateParams(params),\n    });\n};\nclass ZodUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const options = this._def.options;\n        function handleResults(results) {\n            // return first issue-free validation if it exists\n            for (const result of results) {\n                if (result.result.status === \"valid\") {\n                    return result.result;\n                }\n            }\n            for (const result of results) {\n                if (result.result.status === \"dirty\") {\n                    // add issues from dirty option\n                    ctx.common.issues.push(...result.ctx.common.issues);\n                    return result.result;\n                }\n            }\n            // return invalid\n            const unionErrors = results.map((result) => new ZodError(result.ctx.common.issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return Promise.all(options.map(async (option) => {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                return {\n                    result: await option._parseAsync({\n                        data: ctx.data,\n                        path: ctx.path,\n                        parent: childCtx,\n                    }),\n                    ctx: childCtx,\n                };\n            })).then(handleResults);\n        }\n        else {\n            let dirty = undefined;\n            const issues = [];\n            for (const option of options) {\n                const childCtx = {\n                    ...ctx,\n                    common: {\n                        ...ctx.common,\n                        issues: [],\n                    },\n                    parent: null,\n                };\n                const result = option._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: childCtx,\n                });\n                if (result.status === \"valid\") {\n                    return result;\n                }\n                else if (result.status === \"dirty\" && !dirty) {\n                    dirty = { result, ctx: childCtx };\n                }\n                if (childCtx.common.issues.length) {\n                    issues.push(childCtx.common.issues);\n                }\n            }\n            if (dirty) {\n                ctx.common.issues.push(...dirty.ctx.common.issues);\n                return dirty.result;\n            }\n            const unionErrors = issues.map((issues) => new ZodError(issues));\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union,\n                unionErrors,\n            });\n            return INVALID;\n        }\n    }\n    get options() {\n        return this._def.options;\n    }\n}\nZodUnion.create = (types, params) => {\n    return new ZodUnion({\n        options: types,\n        typeName: ZodFirstPartyTypeKind.ZodUnion,\n        ...processCreateParams(params),\n    });\n};\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\n//////////                                 //////////\n//////////      ZodDiscriminatedUnion      //////////\n//////////                                 //////////\n/////////////////////////////////////////////////////\n/////////////////////////////////////////////////////\nconst getDiscriminator = (type) => {\n    if (type instanceof ZodLazy) {\n        return getDiscriminator(type.schema);\n    }\n    else if (type instanceof ZodEffects) {\n        return getDiscriminator(type.innerType());\n    }\n    else if (type instanceof ZodLiteral) {\n        return [type.value];\n    }\n    else if (type instanceof ZodEnum) {\n        return type.options;\n    }\n    else if (type instanceof ZodNativeEnum) {\n        // eslint-disable-next-line ban/ban\n        return util.objectValues(type.enum);\n    }\n    else if (type instanceof ZodDefault) {\n        return getDiscriminator(type._def.innerType);\n    }\n    else if (type instanceof ZodUndefined) {\n        return [undefined];\n    }\n    else if (type instanceof ZodNull) {\n        return [null];\n    }\n    else if (type instanceof ZodOptional) {\n        return [undefined, ...getDiscriminator(type.unwrap())];\n    }\n    else if (type instanceof ZodNullable) {\n        return [null, ...getDiscriminator(type.unwrap())];\n    }\n    else if (type instanceof ZodBranded) {\n        return getDiscriminator(type.unwrap());\n    }\n    else if (type instanceof ZodReadonly) {\n        return getDiscriminator(type.unwrap());\n    }\n    else if (type instanceof ZodCatch) {\n        return getDiscriminator(type._def.innerType);\n    }\n    else {\n        return [];\n    }\n};\nclass ZodDiscriminatedUnion extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const discriminator = this.discriminator;\n        const discriminatorValue = ctx.data[discriminator];\n        const option = this.optionsMap.get(discriminatorValue);\n        if (!option) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_union_discriminator,\n                options: Array.from(this.optionsMap.keys()),\n                path: [discriminator],\n            });\n            return INVALID;\n        }\n        if (ctx.common.async) {\n            return option._parseAsync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n        else {\n            return option._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n        }\n    }\n    get discriminator() {\n        return this._def.discriminator;\n    }\n    get options() {\n        return this._def.options;\n    }\n    get optionsMap() {\n        return this._def.optionsMap;\n    }\n    /**\n     * The constructor of the discriminated union schema. Its behaviour is very similar to that of the normal z.union() constructor.\n     * However, it only allows a union of objects, all of which need to share a discriminator property. This property must\n     * have a different value for each object in the union.\n     * @param discriminator the name of the discriminator property\n     * @param types an array of object schemas\n     * @param params\n     */\n    static create(discriminator, options, params) {\n        // Get all the valid discriminator values\n        const optionsMap = new Map();\n        // try {\n        for (const type of options) {\n            const discriminatorValues = getDiscriminator(type.shape[discriminator]);\n            if (!discriminatorValues.length) {\n                throw new Error(`A discriminator value for key \\`${discriminator}\\` could not be extracted from all schema options`);\n            }\n            for (const value of discriminatorValues) {\n                if (optionsMap.has(value)) {\n                    throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);\n                }\n                optionsMap.set(value, type);\n            }\n        }\n        return new ZodDiscriminatedUnion({\n            typeName: ZodFirstPartyTypeKind.ZodDiscriminatedUnion,\n            discriminator,\n            options,\n            optionsMap,\n            ...processCreateParams(params),\n        });\n    }\n}\nfunction mergeValues(a, b) {\n    const aType = getParsedType(a);\n    const bType = getParsedType(b);\n    if (a === b) {\n        return { valid: true, data: a };\n    }\n    else if (aType === ZodParsedType.object && bType === ZodParsedType.object) {\n        const bKeys = util.objectKeys(b);\n        const sharedKeys = util\n            .objectKeys(a)\n            .filter((key) => bKeys.indexOf(key) !== -1);\n        const newObj = { ...a, ...b };\n        for (const key of sharedKeys) {\n            const sharedValue = mergeValues(a[key], b[key]);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newObj[key] = sharedValue.data;\n        }\n        return { valid: true, data: newObj };\n    }\n    else if (aType === ZodParsedType.array && bType === ZodParsedType.array) {\n        if (a.length !== b.length) {\n            return { valid: false };\n        }\n        const newArray = [];\n        for (let index = 0; index < a.length; index++) {\n            const itemA = a[index];\n            const itemB = b[index];\n            const sharedValue = mergeValues(itemA, itemB);\n            if (!sharedValue.valid) {\n                return { valid: false };\n            }\n            newArray.push(sharedValue.data);\n        }\n        return { valid: true, data: newArray };\n    }\n    else if (aType === ZodParsedType.date &&\n        bType === ZodParsedType.date &&\n        +a === +b) {\n        return { valid: true, data: a };\n    }\n    else {\n        return { valid: false };\n    }\n}\nclass ZodIntersection extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const handleParsed = (parsedLeft, parsedRight) => {\n            if (isAborted(parsedLeft) || isAborted(parsedRight)) {\n                return INVALID;\n            }\n            const merged = mergeValues(parsedLeft.value, parsedRight.value);\n            if (!merged.valid) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.invalid_intersection_types,\n                });\n                return INVALID;\n            }\n            if (isDirty(parsedLeft) || isDirty(parsedRight)) {\n                status.dirty();\n            }\n            return { status: status.value, value: merged.data };\n        };\n        if (ctx.common.async) {\n            return Promise.all([\n                this._def.left._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n                this._def.right._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                }),\n            ]).then(([left, right]) => handleParsed(left, right));\n        }\n        else {\n            return handleParsed(this._def.left._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }), this._def.right._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            }));\n        }\n    }\n}\nZodIntersection.create = (left, right, params) => {\n    return new ZodIntersection({\n        left: left,\n        right: right,\n        typeName: ZodFirstPartyTypeKind.ZodIntersection,\n        ...processCreateParams(params),\n    });\n};\nclass ZodTuple extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.array) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.array,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        if (ctx.data.length < this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_small,\n                minimum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            return INVALID;\n        }\n        const rest = this._def.rest;\n        if (!rest && ctx.data.length > this._def.items.length) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.too_big,\n                maximum: this._def.items.length,\n                inclusive: true,\n                exact: false,\n                type: \"array\",\n            });\n            status.dirty();\n        }\n        const items = [...ctx.data]\n            .map((item, itemIndex) => {\n            const schema = this._def.items[itemIndex] || this._def.rest;\n            if (!schema)\n                return null;\n            return schema._parse(new ParseInputLazyPath(ctx, item, ctx.path, itemIndex));\n        })\n            .filter((x) => !!x); // filter nulls\n        if (ctx.common.async) {\n            return Promise.all(items).then((results) => {\n                return ParseStatus.mergeArray(status, results);\n            });\n        }\n        else {\n            return ParseStatus.mergeArray(status, items);\n        }\n    }\n    get items() {\n        return this._def.items;\n    }\n    rest(rest) {\n        return new ZodTuple({\n            ...this._def,\n            rest,\n        });\n    }\n}\nZodTuple.create = (schemas, params) => {\n    if (!Array.isArray(schemas)) {\n        throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");\n    }\n    return new ZodTuple({\n        items: schemas,\n        typeName: ZodFirstPartyTypeKind.ZodTuple,\n        rest: null,\n        ...processCreateParams(params),\n    });\n};\nclass ZodRecord extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.object) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.object,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const pairs = [];\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        for (const key in ctx.data) {\n            pairs.push({\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, key)),\n                value: valueType._parse(new ParseInputLazyPath(ctx, ctx.data[key], ctx.path, key)),\n                alwaysSet: key in ctx.data,\n            });\n        }\n        if (ctx.common.async) {\n            return ParseStatus.mergeObjectAsync(status, pairs);\n        }\n        else {\n            return ParseStatus.mergeObjectSync(status, pairs);\n        }\n    }\n    get element() {\n        return this._def.valueType;\n    }\n    static create(first, second, third) {\n        if (second instanceof ZodType) {\n            return new ZodRecord({\n                keyType: first,\n                valueType: second,\n                typeName: ZodFirstPartyTypeKind.ZodRecord,\n                ...processCreateParams(third),\n            });\n        }\n        return new ZodRecord({\n            keyType: ZodString.create(),\n            valueType: first,\n            typeName: ZodFirstPartyTypeKind.ZodRecord,\n            ...processCreateParams(second),\n        });\n    }\n}\nclass ZodMap extends ZodType {\n    get keySchema() {\n        return this._def.keyType;\n    }\n    get valueSchema() {\n        return this._def.valueType;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.map) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.map,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const keyType = this._def.keyType;\n        const valueType = this._def.valueType;\n        const pairs = [...ctx.data.entries()].map(([key, value], index) => {\n            return {\n                key: keyType._parse(new ParseInputLazyPath(ctx, key, ctx.path, [index, \"key\"])),\n                value: valueType._parse(new ParseInputLazyPath(ctx, value, ctx.path, [index, \"value\"])),\n            };\n        });\n        if (ctx.common.async) {\n            const finalMap = new Map();\n            return Promise.resolve().then(async () => {\n                for (const pair of pairs) {\n                    const key = await pair.key;\n                    const value = await pair.value;\n                    if (key.status === \"aborted\" || value.status === \"aborted\") {\n                        return INVALID;\n                    }\n                    if (key.status === \"dirty\" || value.status === \"dirty\") {\n                        status.dirty();\n                    }\n                    finalMap.set(key.value, value.value);\n                }\n                return { status: status.value, value: finalMap };\n            });\n        }\n        else {\n            const finalMap = new Map();\n            for (const pair of pairs) {\n                const key = pair.key;\n                const value = pair.value;\n                if (key.status === \"aborted\" || value.status === \"aborted\") {\n                    return INVALID;\n                }\n                if (key.status === \"dirty\" || value.status === \"dirty\") {\n                    status.dirty();\n                }\n                finalMap.set(key.value, value.value);\n            }\n            return { status: status.value, value: finalMap };\n        }\n    }\n}\nZodMap.create = (keyType, valueType, params) => {\n    return new ZodMap({\n        valueType,\n        keyType,\n        typeName: ZodFirstPartyTypeKind.ZodMap,\n        ...processCreateParams(params),\n    });\n};\nclass ZodSet extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.set) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.set,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const def = this._def;\n        if (def.minSize !== null) {\n            if (ctx.data.size < def.minSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_small,\n                    minimum: def.minSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.minSize.message,\n                });\n                status.dirty();\n            }\n        }\n        if (def.maxSize !== null) {\n            if (ctx.data.size > def.maxSize.value) {\n                addIssueToContext(ctx, {\n                    code: ZodIssueCode.too_big,\n                    maximum: def.maxSize.value,\n                    type: \"set\",\n                    inclusive: true,\n                    exact: false,\n                    message: def.maxSize.message,\n                });\n                status.dirty();\n            }\n        }\n        const valueType = this._def.valueType;\n        function finalizeSet(elements) {\n            const parsedSet = new Set();\n            for (const element of elements) {\n                if (element.status === \"aborted\")\n                    return INVALID;\n                if (element.status === \"dirty\")\n                    status.dirty();\n                parsedSet.add(element.value);\n            }\n            return { status: status.value, value: parsedSet };\n        }\n        const elements = [...ctx.data.values()].map((item, i) => valueType._parse(new ParseInputLazyPath(ctx, item, ctx.path, i)));\n        if (ctx.common.async) {\n            return Promise.all(elements).then((elements) => finalizeSet(elements));\n        }\n        else {\n            return finalizeSet(elements);\n        }\n    }\n    min(minSize, message) {\n        return new ZodSet({\n            ...this._def,\n            minSize: { value: minSize, message: errorUtil.toString(message) },\n        });\n    }\n    max(maxSize, message) {\n        return new ZodSet({\n            ...this._def,\n            maxSize: { value: maxSize, message: errorUtil.toString(message) },\n        });\n    }\n    size(size, message) {\n        return this.min(size, message).max(size, message);\n    }\n    nonempty(message) {\n        return this.min(1, message);\n    }\n}\nZodSet.create = (valueType, params) => {\n    return new ZodSet({\n        valueType,\n        minSize: null,\n        maxSize: null,\n        typeName: ZodFirstPartyTypeKind.ZodSet,\n        ...processCreateParams(params),\n    });\n};\nclass ZodFunction extends ZodType {\n    constructor() {\n        super(...arguments);\n        this.validate = this.implement;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.function) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.function,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        function makeArgsIssue(args, error) {\n            return makeIssue({\n                data: args,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_arguments,\n                    argumentsError: error,\n                },\n            });\n        }\n        function makeReturnsIssue(returns, error) {\n            return makeIssue({\n                data: returns,\n                path: ctx.path,\n                errorMaps: [\n                    ctx.common.contextualErrorMap,\n                    ctx.schemaErrorMap,\n                    getErrorMap(),\n                    errorMap,\n                ].filter((x) => !!x),\n                issueData: {\n                    code: ZodIssueCode.invalid_return_type,\n                    returnTypeError: error,\n                },\n            });\n        }\n        const params = { errorMap: ctx.common.contextualErrorMap };\n        const fn = ctx.data;\n        if (this._def.returns instanceof ZodPromise) {\n            // Would love a way to avoid disabling this rule, but we need\n            // an alias (using an arrow function was what caused 2651).\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            const me = this;\n            return OK(async function (...args) {\n                const error = new ZodError([]);\n                const parsedArgs = await me._def.args\n                    .parseAsync(args, params)\n                    .catch((e) => {\n                    error.addIssue(makeArgsIssue(args, e));\n                    throw error;\n                });\n                const result = await Reflect.apply(fn, this, parsedArgs);\n                const parsedReturns = await me._def.returns._def.type\n                    .parseAsync(result, params)\n                    .catch((e) => {\n                    error.addIssue(makeReturnsIssue(result, e));\n                    throw error;\n                });\n                return parsedReturns;\n            });\n        }\n        else {\n            // Would love a way to avoid disabling this rule, but we need\n            // an alias (using an arrow function was what caused 2651).\n            // eslint-disable-next-line @typescript-eslint/no-this-alias\n            const me = this;\n            return OK(function (...args) {\n                const parsedArgs = me._def.args.safeParse(args, params);\n                if (!parsedArgs.success) {\n                    throw new ZodError([makeArgsIssue(args, parsedArgs.error)]);\n                }\n                const result = Reflect.apply(fn, this, parsedArgs.data);\n                const parsedReturns = me._def.returns.safeParse(result, params);\n                if (!parsedReturns.success) {\n                    throw new ZodError([makeReturnsIssue(result, parsedReturns.error)]);\n                }\n                return parsedReturns.data;\n            });\n        }\n    }\n    parameters() {\n        return this._def.args;\n    }\n    returnType() {\n        return this._def.returns;\n    }\n    args(...items) {\n        return new ZodFunction({\n            ...this._def,\n            args: ZodTuple.create(items).rest(ZodUnknown.create()),\n        });\n    }\n    returns(returnType) {\n        return new ZodFunction({\n            ...this._def,\n            returns: returnType,\n        });\n    }\n    implement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    strictImplement(func) {\n        const validatedFunc = this.parse(func);\n        return validatedFunc;\n    }\n    static create(args, returns, params) {\n        return new ZodFunction({\n            args: (args\n                ? args\n                : ZodTuple.create([]).rest(ZodUnknown.create())),\n            returns: returns || ZodUnknown.create(),\n            typeName: ZodFirstPartyTypeKind.ZodFunction,\n            ...processCreateParams(params),\n        });\n    }\n}\nclass ZodLazy extends ZodType {\n    get schema() {\n        return this._def.getter();\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const lazySchema = this._def.getter();\n        return lazySchema._parse({ data: ctx.data, path: ctx.path, parent: ctx });\n    }\n}\nZodLazy.create = (getter, params) => {\n    return new ZodLazy({\n        getter: getter,\n        typeName: ZodFirstPartyTypeKind.ZodLazy,\n        ...processCreateParams(params),\n    });\n};\nclass ZodLiteral extends ZodType {\n    _parse(input) {\n        if (input.data !== this._def.value) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_literal,\n                expected: this._def.value,\n            });\n            return INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n    get value() {\n        return this._def.value;\n    }\n}\nZodLiteral.create = (value, params) => {\n    return new ZodLiteral({\n        value: value,\n        typeName: ZodFirstPartyTypeKind.ZodLiteral,\n        ...processCreateParams(params),\n    });\n};\nfunction createZodEnum(values, params) {\n    return new ZodEnum({\n        values,\n        typeName: ZodFirstPartyTypeKind.ZodEnum,\n        ...processCreateParams(params),\n    });\n}\nclass ZodEnum extends ZodType {\n    constructor() {\n        super(...arguments);\n        _ZodEnum_cache.set(this, void 0);\n    }\n    _parse(input) {\n        if (typeof input.data !== \"string\") {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type,\n            });\n            return INVALID;\n        }\n        if (!__classPrivateFieldGet(this, _ZodEnum_cache, \"f\")) {\n            __classPrivateFieldSet(this, _ZodEnum_cache, new Set(this._def.values), \"f\");\n        }\n        if (!__classPrivateFieldGet(this, _ZodEnum_cache, \"f\").has(input.data)) {\n            const ctx = this._getOrReturnCtx(input);\n            const expectedValues = this._def.values;\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get options() {\n        return this._def.values;\n    }\n    get enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Values() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    get Enum() {\n        const enumValues = {};\n        for (const val of this._def.values) {\n            enumValues[val] = val;\n        }\n        return enumValues;\n    }\n    extract(values, newDef = this._def) {\n        return ZodEnum.create(values, {\n            ...this._def,\n            ...newDef,\n        });\n    }\n    exclude(values, newDef = this._def) {\n        return ZodEnum.create(this.options.filter((opt) => !values.includes(opt)), {\n            ...this._def,\n            ...newDef,\n        });\n    }\n}\n_ZodEnum_cache = new WeakMap();\nZodEnum.create = createZodEnum;\nclass ZodNativeEnum extends ZodType {\n    constructor() {\n        super(...arguments);\n        _ZodNativeEnum_cache.set(this, void 0);\n    }\n    _parse(input) {\n        const nativeEnumValues = util.getValidEnumValues(this._def.values);\n        const ctx = this._getOrReturnCtx(input);\n        if (ctx.parsedType !== ZodParsedType.string &&\n            ctx.parsedType !== ZodParsedType.number) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                expected: util.joinValues(expectedValues),\n                received: ctx.parsedType,\n                code: ZodIssueCode.invalid_type,\n            });\n            return INVALID;\n        }\n        if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, \"f\")) {\n            __classPrivateFieldSet(this, _ZodNativeEnum_cache, new Set(util.getValidEnumValues(this._def.values)), \"f\");\n        }\n        if (!__classPrivateFieldGet(this, _ZodNativeEnum_cache, \"f\").has(input.data)) {\n            const expectedValues = util.objectValues(nativeEnumValues);\n            addIssueToContext(ctx, {\n                received: ctx.data,\n                code: ZodIssueCode.invalid_enum_value,\n                options: expectedValues,\n            });\n            return INVALID;\n        }\n        return OK(input.data);\n    }\n    get enum() {\n        return this._def.values;\n    }\n}\n_ZodNativeEnum_cache = new WeakMap();\nZodNativeEnum.create = (values, params) => {\n    return new ZodNativeEnum({\n        values: values,\n        typeName: ZodFirstPartyTypeKind.ZodNativeEnum,\n        ...processCreateParams(params),\n    });\n};\nclass ZodPromise extends ZodType {\n    unwrap() {\n        return this._def.type;\n    }\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        if (ctx.parsedType !== ZodParsedType.promise &&\n            ctx.common.async === false) {\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.promise,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        const promisified = ctx.parsedType === ZodParsedType.promise\n            ? ctx.data\n            : Promise.resolve(ctx.data);\n        return OK(promisified.then((data) => {\n            return this._def.type.parseAsync(data, {\n                path: ctx.path,\n                errorMap: ctx.common.contextualErrorMap,\n            });\n        }));\n    }\n}\nZodPromise.create = (schema, params) => {\n    return new ZodPromise({\n        type: schema,\n        typeName: ZodFirstPartyTypeKind.ZodPromise,\n        ...processCreateParams(params),\n    });\n};\nclass ZodEffects extends ZodType {\n    innerType() {\n        return this._def.schema;\n    }\n    sourceType() {\n        return this._def.schema._def.typeName === ZodFirstPartyTypeKind.ZodEffects\n            ? this._def.schema.sourceType()\n            : this._def.schema;\n    }\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        const effect = this._def.effect || null;\n        const checkCtx = {\n            addIssue: (arg) => {\n                addIssueToContext(ctx, arg);\n                if (arg.fatal) {\n                    status.abort();\n                }\n                else {\n                    status.dirty();\n                }\n            },\n            get path() {\n                return ctx.path;\n            },\n        };\n        checkCtx.addIssue = checkCtx.addIssue.bind(checkCtx);\n        if (effect.type === \"preprocess\") {\n            const processed = effect.transform(ctx.data, checkCtx);\n            if (ctx.common.async) {\n                return Promise.resolve(processed).then(async (processed) => {\n                    if (status.value === \"aborted\")\n                        return INVALID;\n                    const result = await this._def.schema._parseAsync({\n                        data: processed,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                    if (result.status === \"aborted\")\n                        return INVALID;\n                    if (result.status === \"dirty\")\n                        return DIRTY(result.value);\n                    if (status.value === \"dirty\")\n                        return DIRTY(result.value);\n                    return result;\n                });\n            }\n            else {\n                if (status.value === \"aborted\")\n                    return INVALID;\n                const result = this._def.schema._parseSync({\n                    data: processed,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (result.status === \"aborted\")\n                    return INVALID;\n                if (result.status === \"dirty\")\n                    return DIRTY(result.value);\n                if (status.value === \"dirty\")\n                    return DIRTY(result.value);\n                return result;\n            }\n        }\n        if (effect.type === \"refinement\") {\n            const executeRefinement = (acc) => {\n                const result = effect.refinement(acc, checkCtx);\n                if (ctx.common.async) {\n                    return Promise.resolve(result);\n                }\n                if (result instanceof Promise) {\n                    throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");\n                }\n                return acc;\n            };\n            if (ctx.common.async === false) {\n                const inner = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inner.status === \"aborted\")\n                    return INVALID;\n                if (inner.status === \"dirty\")\n                    status.dirty();\n                // return value is ignored\n                executeRefinement(inner.value);\n                return { status: status.value, value: inner.value };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((inner) => {\n                    if (inner.status === \"aborted\")\n                        return INVALID;\n                    if (inner.status === \"dirty\")\n                        status.dirty();\n                    return executeRefinement(inner.value).then(() => {\n                        return { status: status.value, value: inner.value };\n                    });\n                });\n            }\n        }\n        if (effect.type === \"transform\") {\n            if (ctx.common.async === false) {\n                const base = this._def.schema._parseSync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (!isValid(base))\n                    return base;\n                const result = effect.transform(base.value, checkCtx);\n                if (result instanceof Promise) {\n                    throw new Error(`Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.`);\n                }\n                return { status: status.value, value: result };\n            }\n            else {\n                return this._def.schema\n                    ._parseAsync({ data: ctx.data, path: ctx.path, parent: ctx })\n                    .then((base) => {\n                    if (!isValid(base))\n                        return base;\n                    return Promise.resolve(effect.transform(base.value, checkCtx)).then((result) => ({ status: status.value, value: result }));\n                });\n            }\n        }\n        util.assertNever(effect);\n    }\n}\nZodEffects.create = (schema, effect, params) => {\n    return new ZodEffects({\n        schema,\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        effect,\n        ...processCreateParams(params),\n    });\n};\nZodEffects.createWithPreprocess = (preprocess, schema, params) => {\n    return new ZodEffects({\n        schema,\n        effect: { type: \"preprocess\", transform: preprocess },\n        typeName: ZodFirstPartyTypeKind.ZodEffects,\n        ...processCreateParams(params),\n    });\n};\nclass ZodOptional extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.undefined) {\n            return OK(undefined);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodOptional.create = (type, params) => {\n    return new ZodOptional({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodOptional,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNullable extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType === ZodParsedType.null) {\n            return OK(null);\n        }\n        return this._def.innerType._parse(input);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodNullable.create = (type, params) => {\n    return new ZodNullable({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodNullable,\n        ...processCreateParams(params),\n    });\n};\nclass ZodDefault extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        let data = ctx.data;\n        if (ctx.parsedType === ZodParsedType.undefined) {\n            data = this._def.defaultValue();\n        }\n        return this._def.innerType._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    removeDefault() {\n        return this._def.innerType;\n    }\n}\nZodDefault.create = (type, params) => {\n    return new ZodDefault({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodDefault,\n        defaultValue: typeof params.default === \"function\"\n            ? params.default\n            : () => params.default,\n        ...processCreateParams(params),\n    });\n};\nclass ZodCatch extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        // newCtx is used to not collect issues from inner types in ctx\n        const newCtx = {\n            ...ctx,\n            common: {\n                ...ctx.common,\n                issues: [],\n            },\n        };\n        const result = this._def.innerType._parse({\n            data: newCtx.data,\n            path: newCtx.path,\n            parent: {\n                ...newCtx,\n            },\n        });\n        if (isAsync(result)) {\n            return result.then((result) => {\n                return {\n                    status: \"valid\",\n                    value: result.status === \"valid\"\n                        ? result.value\n                        : this._def.catchValue({\n                            get error() {\n                                return new ZodError(newCtx.common.issues);\n                            },\n                            input: newCtx.data,\n                        }),\n                };\n            });\n        }\n        else {\n            return {\n                status: \"valid\",\n                value: result.status === \"valid\"\n                    ? result.value\n                    : this._def.catchValue({\n                        get error() {\n                            return new ZodError(newCtx.common.issues);\n                        },\n                        input: newCtx.data,\n                    }),\n            };\n        }\n    }\n    removeCatch() {\n        return this._def.innerType;\n    }\n}\nZodCatch.create = (type, params) => {\n    return new ZodCatch({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodCatch,\n        catchValue: typeof params.catch === \"function\" ? params.catch : () => params.catch,\n        ...processCreateParams(params),\n    });\n};\nclass ZodNaN extends ZodType {\n    _parse(input) {\n        const parsedType = this._getType(input);\n        if (parsedType !== ZodParsedType.nan) {\n            const ctx = this._getOrReturnCtx(input);\n            addIssueToContext(ctx, {\n                code: ZodIssueCode.invalid_type,\n                expected: ZodParsedType.nan,\n                received: ctx.parsedType,\n            });\n            return INVALID;\n        }\n        return { status: \"valid\", value: input.data };\n    }\n}\nZodNaN.create = (params) => {\n    return new ZodNaN({\n        typeName: ZodFirstPartyTypeKind.ZodNaN,\n        ...processCreateParams(params),\n    });\n};\nconst BRAND = Symbol(\"zod_brand\");\nclass ZodBranded extends ZodType {\n    _parse(input) {\n        const { ctx } = this._processInputParams(input);\n        const data = ctx.data;\n        return this._def.type._parse({\n            data,\n            path: ctx.path,\n            parent: ctx,\n        });\n    }\n    unwrap() {\n        return this._def.type;\n    }\n}\nclass ZodPipeline extends ZodType {\n    _parse(input) {\n        const { status, ctx } = this._processInputParams(input);\n        if (ctx.common.async) {\n            const handleAsync = async () => {\n                const inResult = await this._def.in._parseAsync({\n                    data: ctx.data,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n                if (inResult.status === \"aborted\")\n                    return INVALID;\n                if (inResult.status === \"dirty\") {\n                    status.dirty();\n                    return DIRTY(inResult.value);\n                }\n                else {\n                    return this._def.out._parseAsync({\n                        data: inResult.value,\n                        path: ctx.path,\n                        parent: ctx,\n                    });\n                }\n            };\n            return handleAsync();\n        }\n        else {\n            const inResult = this._def.in._parseSync({\n                data: ctx.data,\n                path: ctx.path,\n                parent: ctx,\n            });\n            if (inResult.status === \"aborted\")\n                return INVALID;\n            if (inResult.status === \"dirty\") {\n                status.dirty();\n                return {\n                    status: \"dirty\",\n                    value: inResult.value,\n                };\n            }\n            else {\n                return this._def.out._parseSync({\n                    data: inResult.value,\n                    path: ctx.path,\n                    parent: ctx,\n                });\n            }\n        }\n    }\n    static create(a, b) {\n        return new ZodPipeline({\n            in: a,\n            out: b,\n            typeName: ZodFirstPartyTypeKind.ZodPipeline,\n        });\n    }\n}\nclass ZodReadonly extends ZodType {\n    _parse(input) {\n        const result = this._def.innerType._parse(input);\n        const freeze = (data) => {\n            if (isValid(data)) {\n                data.value = Object.freeze(data.value);\n            }\n            return data;\n        };\n        return isAsync(result)\n            ? result.then((data) => freeze(data))\n            : freeze(result);\n    }\n    unwrap() {\n        return this._def.innerType;\n    }\n}\nZodReadonly.create = (type, params) => {\n    return new ZodReadonly({\n        innerType: type,\n        typeName: ZodFirstPartyTypeKind.ZodReadonly,\n        ...processCreateParams(params),\n    });\n};\nfunction custom(check, params = {}, \n/**\n * @deprecated\n *\n * Pass `fatal` into the params object instead:\n *\n * ```ts\n * z.string().custom((val) => val.length > 5, { fatal: false })\n * ```\n *\n */\nfatal) {\n    if (check)\n        return ZodAny.create().superRefine((data, ctx) => {\n            var _a, _b;\n            if (!check(data)) {\n                const p = typeof params === \"function\"\n                    ? params(data)\n                    : typeof params === \"string\"\n                        ? { message: params }\n                        : params;\n                const _fatal = (_b = (_a = p.fatal) !== null && _a !== void 0 ? _a : fatal) !== null && _b !== void 0 ? _b : true;\n                const p2 = typeof p === \"string\" ? { message: p } : p;\n                ctx.addIssue({ code: \"custom\", ...p2, fatal: _fatal });\n            }\n        });\n    return ZodAny.create();\n}\nconst late = {\n    object: ZodObject.lazycreate,\n};\nvar ZodFirstPartyTypeKind;\n(function (ZodFirstPartyTypeKind) {\n    ZodFirstPartyTypeKind[\"ZodString\"] = \"ZodString\";\n    ZodFirstPartyTypeKind[\"ZodNumber\"] = \"ZodNumber\";\n    ZodFirstPartyTypeKind[\"ZodNaN\"] = \"ZodNaN\";\n    ZodFirstPartyTypeKind[\"ZodBigInt\"] = \"ZodBigInt\";\n    ZodFirstPartyTypeKind[\"ZodBoolean\"] = \"ZodBoolean\";\n    ZodFirstPartyTypeKind[\"ZodDate\"] = \"ZodDate\";\n    ZodFirstPartyTypeKind[\"ZodSymbol\"] = \"ZodSymbol\";\n    ZodFirstPartyTypeKind[\"ZodUndefined\"] = \"ZodUndefined\";\n    ZodFirstPartyTypeKind[\"ZodNull\"] = \"ZodNull\";\n    ZodFirstPartyTypeKind[\"ZodAny\"] = \"ZodAny\";\n    ZodFirstPartyTypeKind[\"ZodUnknown\"] = \"ZodUnknown\";\n    ZodFirstPartyTypeKind[\"ZodNever\"] = \"ZodNever\";\n    ZodFirstPartyTypeKind[\"ZodVoid\"] = \"ZodVoid\";\n    ZodFirstPartyTypeKind[\"ZodArray\"] = \"ZodArray\";\n    ZodFirstPartyTypeKind[\"ZodObject\"] = \"ZodObject\";\n    ZodFirstPartyTypeKind[\"ZodUnion\"] = \"ZodUnion\";\n    ZodFirstPartyTypeKind[\"ZodDiscriminatedUnion\"] = \"ZodDiscriminatedUnion\";\n    ZodFirstPartyTypeKind[\"ZodIntersection\"] = \"ZodIntersection\";\n    ZodFirstPartyTypeKind[\"ZodTuple\"] = \"ZodTuple\";\n    ZodFirstPartyTypeKind[\"ZodRecord\"] = \"ZodRecord\";\n    ZodFirstPartyTypeKind[\"ZodMap\"] = \"ZodMap\";\n    ZodFirstPartyTypeKind[\"ZodSet\"] = \"ZodSet\";\n    ZodFirstPartyTypeKind[\"ZodFunction\"] = \"ZodFunction\";\n    ZodFirstPartyTypeKind[\"ZodLazy\"] = \"ZodLazy\";\n    ZodFirstPartyTypeKind[\"ZodLiteral\"] = \"ZodLiteral\";\n    ZodFirstPartyTypeKind[\"ZodEnum\"] = \"ZodEnum\";\n    ZodFirstPartyTypeKind[\"ZodEffects\"] = \"ZodEffects\";\n    ZodFirstPartyTypeKind[\"ZodNativeEnum\"] = \"ZodNativeEnum\";\n    ZodFirstPartyTypeKind[\"ZodOptional\"] = \"ZodOptional\";\n    ZodFirstPartyTypeKind[\"ZodNullable\"] = \"ZodNullable\";\n    ZodFirstPartyTypeKind[\"ZodDefault\"] = \"ZodDefault\";\n    ZodFirstPartyTypeKind[\"ZodCatch\"] = \"ZodCatch\";\n    ZodFirstPartyTypeKind[\"ZodPromise\"] = \"ZodPromise\";\n    ZodFirstPartyTypeKind[\"ZodBranded\"] = \"ZodBranded\";\n    ZodFirstPartyTypeKind[\"ZodPipeline\"] = \"ZodPipeline\";\n    ZodFirstPartyTypeKind[\"ZodReadonly\"] = \"ZodReadonly\";\n})(ZodFirstPartyTypeKind || (ZodFirstPartyTypeKind = {}));\nconst instanceOfType = (\n// const instanceOfType = <T extends new (...args: any[]) => any>(\ncls, params = {\n    message: `Input not instance of ${cls.name}`,\n}) => custom((data) => data instanceof cls, params);\nconst stringType = ZodString.create;\nconst numberType = ZodNumber.create;\nconst nanType = ZodNaN.create;\nconst bigIntType = ZodBigInt.create;\nconst booleanType = ZodBoolean.create;\nconst dateType = ZodDate.create;\nconst symbolType = ZodSymbol.create;\nconst undefinedType = ZodUndefined.create;\nconst nullType = ZodNull.create;\nconst anyType = ZodAny.create;\nconst unknownType = ZodUnknown.create;\nconst neverType = ZodNever.create;\nconst voidType = ZodVoid.create;\nconst arrayType = ZodArray.create;\nconst objectType = ZodObject.create;\nconst strictObjectType = ZodObject.strictCreate;\nconst unionType = ZodUnion.create;\nconst discriminatedUnionType = ZodDiscriminatedUnion.create;\nconst intersectionType = ZodIntersection.create;\nconst tupleType = ZodTuple.create;\nconst recordType = ZodRecord.create;\nconst mapType = ZodMap.create;\nconst setType = ZodSet.create;\nconst functionType = ZodFunction.create;\nconst lazyType = ZodLazy.create;\nconst literalType = ZodLiteral.create;\nconst enumType = ZodEnum.create;\nconst nativeEnumType = ZodNativeEnum.create;\nconst promiseType = ZodPromise.create;\nconst effectsType = ZodEffects.create;\nconst optionalType = ZodOptional.create;\nconst nullableType = ZodNullable.create;\nconst preprocessType = ZodEffects.createWithPreprocess;\nconst pipelineType = ZodPipeline.create;\nconst ostring = () => stringType().optional();\nconst onumber = () => numberType().optional();\nconst oboolean = () => booleanType().optional();\nconst coerce = {\n    string: ((arg) => ZodString.create({ ...arg, coerce: true })),\n    number: ((arg) => ZodNumber.create({ ...arg, coerce: true })),\n    boolean: ((arg) => ZodBoolean.create({\n        ...arg,\n        coerce: true,\n    })),\n    bigint: ((arg) => ZodBigInt.create({ ...arg, coerce: true })),\n    date: ((arg) => ZodDate.create({ ...arg, coerce: true })),\n};\nconst NEVER = INVALID;\n\nvar z = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    defaultErrorMap: errorMap,\n    setErrorMap: setErrorMap,\n    getErrorMap: getErrorMap,\n    makeIssue: makeIssue,\n    EMPTY_PATH: EMPTY_PATH,\n    addIssueToContext: addIssueToContext,\n    ParseStatus: ParseStatus,\n    INVALID: INVALID,\n    DIRTY: DIRTY,\n    OK: OK,\n    isAborted: isAborted,\n    isDirty: isDirty,\n    isValid: isValid,\n    isAsync: isAsync,\n    get util () { return util; },\n    get objectUtil () { return objectUtil; },\n    ZodParsedType: ZodParsedType,\n    getParsedType: getParsedType,\n    ZodType: ZodType,\n    datetimeRegex: datetimeRegex,\n    ZodString: ZodString,\n    ZodNumber: ZodNumber,\n    ZodBigInt: ZodBigInt,\n    ZodBoolean: ZodBoolean,\n    ZodDate: ZodDate,\n    ZodSymbol: ZodSymbol,\n    ZodUndefined: ZodUndefined,\n    ZodNull: ZodNull,\n    ZodAny: ZodAny,\n    ZodUnknown: ZodUnknown,\n    ZodNever: ZodNever,\n    ZodVoid: ZodVoid,\n    ZodArray: ZodArray,\n    ZodObject: ZodObject,\n    ZodUnion: ZodUnion,\n    ZodDiscriminatedUnion: ZodDiscriminatedUnion,\n    ZodIntersection: ZodIntersection,\n    ZodTuple: ZodTuple,\n    ZodRecord: ZodRecord,\n    ZodMap: ZodMap,\n    ZodSet: ZodSet,\n    ZodFunction: ZodFunction,\n    ZodLazy: ZodLazy,\n    ZodLiteral: ZodLiteral,\n    ZodEnum: ZodEnum,\n    ZodNativeEnum: ZodNativeEnum,\n    ZodPromise: ZodPromise,\n    ZodEffects: ZodEffects,\n    ZodTransformer: ZodEffects,\n    ZodOptional: ZodOptional,\n    ZodNullable: ZodNullable,\n    ZodDefault: ZodDefault,\n    ZodCatch: ZodCatch,\n    ZodNaN: ZodNaN,\n    BRAND: BRAND,\n    ZodBranded: ZodBranded,\n    ZodPipeline: ZodPipeline,\n    ZodReadonly: ZodReadonly,\n    custom: custom,\n    Schema: ZodType,\n    ZodSchema: ZodType,\n    late: late,\n    get ZodFirstPartyTypeKind () { return ZodFirstPartyTypeKind; },\n    coerce: coerce,\n    any: anyType,\n    array: arrayType,\n    bigint: bigIntType,\n    boolean: booleanType,\n    date: dateType,\n    discriminatedUnion: discriminatedUnionType,\n    effect: effectsType,\n    'enum': enumType,\n    'function': functionType,\n    'instanceof': instanceOfType,\n    intersection: intersectionType,\n    lazy: lazyType,\n    literal: literalType,\n    map: mapType,\n    nan: nanType,\n    nativeEnum: nativeEnumType,\n    never: neverType,\n    'null': nullType,\n    nullable: nullableType,\n    number: numberType,\n    object: objectType,\n    oboolean: oboolean,\n    onumber: onumber,\n    optional: optionalType,\n    ostring: ostring,\n    pipeline: pipelineType,\n    preprocess: preprocessType,\n    promise: promiseType,\n    record: recordType,\n    set: setType,\n    strictObject: strictObjectType,\n    string: stringType,\n    symbol: symbolType,\n    transformer: effectsType,\n    tuple: tupleType,\n    'undefined': undefinedType,\n    union: unionType,\n    unknown: unknownType,\n    'void': voidType,\n    NEVER: NEVER,\n    ZodIssueCode: ZodIssueCode,\n    quotelessJson: quotelessJson,\n    ZodError: ZodError\n});\n\nexport { BRAND, DIRTY, EMPTY_PATH, INVALID, NEVER, OK, ParseStatus, ZodType as Schema, ZodAny, ZodArray, ZodBigInt, ZodBoolean, ZodBranded, ZodCatch, ZodDate, ZodDefault, ZodDiscriminatedUnion, ZodEffects, ZodEnum, ZodError, ZodFirstPartyTypeKind, ZodFunction, ZodIntersection, ZodIssueCode, ZodLazy, ZodLiteral, ZodMap, ZodNaN, ZodNativeEnum, ZodNever, ZodNull, ZodNullable, ZodNumber, ZodObject, ZodOptional, ZodParsedType, ZodPipeline, ZodPromise, ZodReadonly, ZodRecord, ZodType as ZodSchema, ZodSet, ZodString, ZodSymbol, ZodEffects as ZodTransformer, ZodTuple, ZodType, ZodUndefined, ZodUnion, ZodUnknown, ZodVoid, addIssueToContext, anyType as any, arrayType as array, bigIntType as bigint, booleanType as boolean, coerce, custom, dateType as date, datetimeRegex, z as default, errorMap as defaultErrorMap, discriminatedUnionType as discriminatedUnion, effectsType as effect, enumType as enum, functionType as function, getErrorMap, getParsedType, instanceOfType as instanceof, intersectionType as intersection, isAborted, isAsync, isDirty, isValid, late, lazyType as lazy, literalType as literal, makeIssue, mapType as map, nanType as nan, nativeEnumType as nativeEnum, neverType as never, nullType as null, nullableType as nullable, numberType as number, objectType as object, objectUtil, oboolean, onumber, optionalType as optional, ostring, pipelineType as pipeline, preprocessType as preprocess, promiseType as promise, quotelessJson, recordType as record, setType as set, setErrorMap, strictObjectType as strictObject, stringType as string, symbolType as symbol, effectsType as transformer, tupleType as tuple, undefinedType as undefined, unionType as union, unknownType as unknown, util, voidType as void, z };\n", "import { ZodObject, ZodOptional, ZodSchema } from \"zod\";\nimport type { Endpoint, EndpointOptions } from \"./endpoint\";\n\nexport type OpenAPISchemaType = \"string\" | \"number\" | \"integer\" | \"boolean\" | \"array\" | \"object\";\n\nexport interface OpenAPIParameter {\n\tin: \"query\" | \"path\" | \"header\" | \"cookie\";\n\tname?: string;\n\tdescription?: string;\n\trequired?: boolean;\n\tschema?: {\n\t\ttype: OpenAPISchemaType;\n\t\tformat?: string;\n\t\titems?: {\n\t\t\ttype: OpenAPISchemaType;\n\t\t};\n\t\tenum?: string[];\n\t\tminLength?: number;\n\t\tdescription?: string;\n\t\tdefault?: string;\n\t\texample?: string;\n\t};\n}\n\nexport interface Path {\n\tget?: {\n\t\ttags?: string[];\n\t\toperationId?: string;\n\t\tdescription?: string;\n\t\tsecurity?: [{ bearerAuth: string[] }];\n\t\tparameters?: OpenAPIParameter[];\n\t\tresponses?: {\n\t\t\t[key in string]: {\n\t\t\t\tdescription?: string;\n\t\t\t\tcontent: {\n\t\t\t\t\t\"application/json\": {\n\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t};\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t};\n\tpost?: {\n\t\ttags?: string[];\n\t\toperationId?: string;\n\t\tdescription?: string;\n\t\tsecurity?: [{ bearerAuth: string[] }];\n\t\tparameters?: OpenAPIParameter[];\n\t\trequestBody?: {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t\tresponses?: {\n\t\t\t[key in string]: {\n\t\t\t\tdescription?: string;\n\t\t\t\tcontent: {\n\t\t\t\t\t\"application/json\": {\n\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\ttype?: OpenAPISchemaType;\n\t\t\t\t\t\t\tproperties?: Record<string, any>;\n\t\t\t\t\t\t\trequired?: string[];\n\t\t\t\t\t\t\t$ref?: string;\n\t\t\t\t\t\t};\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t};\n\t\t};\n\t};\n}\nconst paths: Record<string, Path> = {};\n\nfunction getTypeFromZodType(zodType: ZodSchema) {\n\tswitch (zodType.constructor.name) {\n\t\tcase \"ZodString\":\n\t\t\treturn \"string\";\n\t\tcase \"ZodNumber\":\n\t\t\treturn \"number\";\n\t\tcase \"ZodBoolean\":\n\t\t\treturn \"boolean\";\n\t\tcase \"ZodObject\":\n\t\t\treturn \"object\";\n\t\tcase \"ZodArray\":\n\t\t\treturn \"array\";\n\t\tdefault:\n\t\t\treturn \"string\";\n\t}\n}\n\nfunction getParameters(options: EndpointOptions) {\n\tconst parameters: OpenAPIParameter[] = [];\n\tif (options.metadata?.openapi?.parameters) {\n\t\tparameters.push(...options.metadata.openapi.parameters);\n\t\treturn parameters;\n\t}\n\tif (options.query instanceof ZodObject) {\n\t\tObject.entries(options.query.shape).forEach(([key, value]) => {\n\t\t\tif (value instanceof ZodSchema) {\n\t\t\t\tparameters.push({\n\t\t\t\t\tname: key,\n\t\t\t\t\tin: \"query\",\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: getTypeFromZodType(value),\n\t\t\t\t\t\t...(\"minLength\" in value && value.minLength\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\tminLength: value.minLength as number,\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t: {}),\n\t\t\t\t\t\tdescription: value.description,\n\t\t\t\t\t},\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n\treturn parameters;\n}\n\nfunction getRequestBody(options: EndpointOptions): any {\n\tif (options.metadata?.openapi?.requestBody) {\n\t\treturn options.metadata.openapi.requestBody;\n\t}\n\tif (!options.body) return undefined;\n\tif (options.body instanceof ZodObject || options.body instanceof ZodOptional) {\n\t\t// @ts-ignore\n\t\tconst shape = options.body.shape;\n\t\tif (!shape) return undefined;\n\t\tconst properties: Record<string, any> = {};\n\t\tconst required: string[] = [];\n\t\tObject.entries(shape).forEach(([key, value]) => {\n\t\t\tif (value instanceof ZodSchema) {\n\t\t\t\tproperties[key] = {\n\t\t\t\t\ttype: getTypeFromZodType(value),\n\t\t\t\t\tdescription: value.description,\n\t\t\t\t};\n\t\t\t\tif (!(value instanceof ZodOptional)) {\n\t\t\t\t\trequired.push(key);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\treturn {\n\t\t\trequired: options.body instanceof ZodOptional ? false : options.body ? true : false,\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties,\n\t\t\t\t\t\trequired,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t}\n\treturn undefined;\n}\n\nfunction getResponse(responses?: Record<string, any>) {\n\treturn {\n\t\t\"400\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t\trequired: [\"message\"],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: \"Bad Request. Usually due to missing parameters, or invalid parameters.\",\n\t\t},\n\t\t\"401\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t\trequired: [\"message\"],\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: \"Unauthorized. Due to missing or invalid authentication.\",\n\t\t},\n\t\t\"403\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription:\n\t\t\t\t\"Forbidden. You do not have permission to access this resource or to perform this action.\",\n\t\t},\n\t\t\"404\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: \"Not Found. The requested resource was not found.\",\n\t\t},\n\t\t\"429\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription: \"Too Many Requests. You have exceeded the rate limit. Try again later.\",\n\t\t},\n\t\t\"500\": {\n\t\t\tcontent: {\n\t\t\t\t\"application/json\": {\n\t\t\t\t\tschema: {\n\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\tproperties: {\n\t\t\t\t\t\t\tmessage: {\n\t\t\t\t\t\t\t\ttype: \"string\",\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t},\n\t\t\tdescription:\n\t\t\t\t\"Internal Server Error. This is a problem with the server that you cannot fix.\",\n\t\t},\n\t\t...responses,\n\t} as any;\n}\n\nexport async function generator(\n\tendpoints: Record<string, Endpoint>,\n\tconfig?: {\n\t\turl: string;\n\t},\n) {\n\tconst components = {\n\t\tschemas: {},\n\t};\n\n\tObject.entries(endpoints).forEach(([_, value]) => {\n\t\tconst options = value.options as EndpointOptions;\n\t\tif (options.metadata?.SERVER_ONLY) return;\n\t\tif (options.method === \"GET\") {\n\t\t\tpaths[value.path] = {\n\t\t\t\tget: {\n\t\t\t\t\ttags: [\"Default\", ...(options.metadata?.openapi?.tags || [])],\n\t\t\t\t\tdescription: options.metadata?.openapi?.description,\n\t\t\t\t\toperationId: options.metadata?.openapi?.operationId,\n\t\t\t\t\tsecurity: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tbearerAuth: [],\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tparameters: getParameters(options),\n\t\t\t\t\tresponses: getResponse(options.metadata?.openapi?.responses),\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (options.method === \"POST\") {\n\t\t\tconst body = getRequestBody(options);\n\t\t\tpaths[value.path] = {\n\t\t\t\tpost: {\n\t\t\t\t\ttags: [\"Default\", ...(options.metadata?.openapi?.tags || [])],\n\t\t\t\t\tdescription: options.metadata?.openapi?.description,\n\t\t\t\t\toperationId: options.metadata?.openapi?.operationId,\n\t\t\t\t\tsecurity: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tbearerAuth: [],\n\t\t\t\t\t\t},\n\t\t\t\t\t],\n\t\t\t\t\tparameters: getParameters(options),\n\t\t\t\t\t...(body\n\t\t\t\t\t\t? { requestBody: body }\n\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\trequestBody: {\n\t\t\t\t\t\t\t\t\t//set body none\n\t\t\t\t\t\t\t\t\tcontent: {\n\t\t\t\t\t\t\t\t\t\t\"application/json\": {\n\t\t\t\t\t\t\t\t\t\t\tschema: {\n\t\t\t\t\t\t\t\t\t\t\t\ttype: \"object\",\n\t\t\t\t\t\t\t\t\t\t\t\tproperties: {},\n\t\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t}),\n\t\t\t\t\tresponses: getResponse(options.metadata?.openapi?.responses),\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\t});\n\n\tconst res = {\n\t\topenapi: \"3.1.1\",\n\t\tinfo: {\n\t\t\ttitle: \"Better Auth\",\n\t\t\tdescription: \"API Reference for your Better Auth Instance\",\n\t\t\tversion: \"1.1.0\",\n\t\t},\n\t\tcomponents,\n\t\tsecurity: [\n\t\t\t{\n\t\t\t\tapiKeyCookie: [],\n\t\t\t},\n\t\t],\n\t\tservers: [\n\t\t\t{\n\t\t\t\turl: config?.url,\n\t\t\t},\n\t\t],\n\t\ttags: [\n\t\t\t{\n\t\t\t\tname: \"Default\",\n\t\t\t\tdescription:\n\t\t\t\t\t\"Default endpoints that are included with Better Auth by default. These endpoints are not part of any plugin.\",\n\t\t\t},\n\t\t],\n\t\tpaths,\n\t};\n\treturn res;\n}\n\nexport const getHTML = (\n\tapiReference: Record<string, any>,\n\tconfig?: {\n\t\tlogo?: string;\n\t\ttheme?: string;\n\t\ttitle?: string;\n\t\tdescription?: string;\n\t},\n) => `<!doctype html>\n<html>\n  <head>\n    <title>Scalar API Reference</title>\n    <meta charset=\"utf-8\" />\n    <meta\n      name=\"viewport\"\n      content=\"width=device-width, initial-scale=1\" />\n  </head>\n  <body>\n    <script\n      id=\"api-reference\"\n      type=\"application/json\">\n    ${JSON.stringify(apiReference)}\n    </script>\n\t <script>\n      var configuration = {\n\t  \tfavicon: ${config?.logo ? `data:image/svg+xml;utf8,${encodeURIComponent(config.logo)}` : undefined} ,\n\t   \ttheme: ${config?.theme || \"saturn\"},\n        metaData: {\n\t\t\ttitle: ${config?.title || \"Open API Reference\"},\n\t\t\tdescription: ${config?.description || \"Better Call Open API\"},\n\t\t}\n      }\n      document.getElementById('api-reference').dataset.configuration =\n        JSON.stringify(configuration)\n    </script>\n\t  <script src=\"https://cdn.jsdelivr.net/npm/@scalar/api-reference\"></script>\n  </body>\n</html>`;\n"], "names": ["options", "headers", "createEndpoint", "util", "objectUtil", "errorUtil", "errorMap", "ctx", "result", "issues", "elements", "processed", "ZodFirstPartyTypeKind", "createEndpoint"], "mappings": ";;;;;;;;;;;;;;;;;AIAA,SAAS,cAAc;;AKAvB,SAAS,gBAAgB,kBAAkB,UAAU,WAAW,qBAAqB;ATA9E,IAAM,cAAc;IAC1B,IAAI;IACJ,SAAS;IACT,UAAU;IACV,YAAY;IACZ,kBAAkB;IAClB,mBAAmB;IACnB,OAAO;IACP,WAAW;IACX,cAAc;IACd,oBAAoB;IACpB,aAAa;IACb,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,oBAAoB;IACpB,gBAAgB;IAChB,+BAA+B;IAC/B,iBAAiB;IACjB,UAAU;IACV,MAAM;IACN,iBAAiB;IACjB,qBAAqB;IACrB,mBAAmB;IACnB,cAAc;IACd,wBAAwB;IACxB,uBAAuB;IACvB,oBAAoB;IACpB,gBAAgB;IAChB,qBAAqB;IACrB,sBAAsB;IACtB,QAAQ;IACR,mBAAmB;IACnB,WAAW;IACX,kBAAkB;IAClB,uBAAuB;IACvB,mBAAmB;IACnB,iCAAiC;IACjC,+BAA+B;IAC/B,uBAAuB;IACvB,iBAAiB;IACjB,aAAa;IACb,qBAAqB;IACrB,iBAAiB;IACjB,4BAA4B;IAC5B,yBAAyB;IACzB,sBAAsB;IACtB,eAAe;IACf,cAAc;IACd,iCAAiC;AAClC;AAmEO,IAAM,WAAN,cAAuB,MAAM;IACnC,YACQ,SAA4C,uBAAA,EAC5C,OAKQ,KAAA,CAAA,EACR,UAAuB,CAAC,CAAA,EACxB,aAAa,OAAO,WAAW,WAAW,SAAS,WAAA,CAAY,MAAM,CAAA,CAC3E;QACD,KAAA,CAAM,MAAM,OAAO;QAVZ,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,IAAA,GAAA;QAMA,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;QAGP,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,IAAA,GAAO,OACT;YACA,MAAM,MAAM,SACT,YAAY,EACb,QAAQ,MAAM,GAAG,EACjB,QAAQ,eAAe,EAAE;YAC3B,GAAG,IAAA;QACJ,IACC,KAAA;QACH,IAAA,CAAK,KAAA,GAAQ;IACd;AACD;;AChJA,eAAsB,QAAQ,OAAA,EAAkB;IAC/C,MAAM,cAAc,QAAQ,OAAA,CAAQ,GAAA,CAAI,cAAc,KAAK;IAE3D,IAAI,CAAC,QAAQ,IAAA,EAAM;QAClB,OAAO,KAAA;IACR;IAEA,IAAI,YAAY,QAAA,CAAS,kBAAkB,GAAG;QAC7C,OAAO,MAAM,QAAQ,IAAA,CAAK;IAC3B;IAEA,IAAI,YAAY,QAAA,CAAS,mCAAmC,GAAG;QAC9D,MAAM,WAAW,MAAM,QAAQ,QAAA,CAAS;QACxC,MAAM,SAAiC,CAAC;QACxC,SAAS,OAAA,CAAQ,CAAC,OAAO,QAAQ;YAChC,MAAA,CAAO,GAAG,CAAA,GAAI,MAAM,QAAA,CAAS;QAC9B,CAAC;QACD,OAAO;IACR;IAEA,IAAI,YAAY,QAAA,CAAS,qBAAqB,GAAG;QAChD,MAAM,WAAW,MAAM,QAAQ,QAAA,CAAS;QACxC,MAAM,SAA8B,CAAC;QACrC,SAAS,OAAA,CAAQ,CAAC,OAAO,QAAQ;YAChC,MAAA,CAAO,GAAG,CAAA,GAAI;QACf,CAAC;QACD,OAAO;IACR;IAEA,IAAI,YAAY,QAAA,CAAS,YAAY,GAAG;QACvC,OAAO,MAAM,QAAQ,IAAA,CAAK;IAC3B;IAEA,IAAI,YAAY,QAAA,CAAS,0BAA0B,GAAG;QACrD,OAAO,MAAM,QAAQ,WAAA,CAAY;IAClC;IAEA,IACC,YAAY,QAAA,CAAS,iBAAiB,KACtC,YAAY,QAAA,CAAS,QAAQ,KAC7B,YAAY,QAAA,CAAS,QAAQ,GAC5B;QACD,MAAM,OAAO,MAAM,QAAQ,IAAA,CAAK;QAChC,OAAO;IACR;IAEA,IAAI,YAAY,QAAA,CAAS,oBAAoB,KAAK,QAAQ,IAAA,YAAgB,gBAAgB;QACzF,OAAO,QAAQ,IAAA;IAChB;IAEA,OAAO,MAAM,QAAQ,IAAA,CAAK;AAC3B;AAEO,SAAS,WAAW,KAAA,EAAY;IACtC,OAAO,iBAAiB,YAAY,OAAO,SAAS;AACrD;AAEO,SAAS,UAAU,GAAA,EAAa;IACtC,IAAI;QACH,OAAO,IAAI,QAAA,CAAS,GAAG,IAAI,mBAAmB,GAAG,IAAI;IACtD,EAAA,OAAQ;QACP,OAAO;IACR;AACD;;AC9DA,SAAS,mBAAmB,KAAA,EAAY;IACvC,IAAI,UAAU,KAAA,GAAW;QACxB,OAAO;IACR;IACA,MAAM,IAAI,OAAO;IACjB,IAAI,MAAM,YAAY,MAAM,YAAY,MAAM,aAAa,MAAM,MAAM;QACtE,OAAO;IACR;IACA,IAAI,MAAM,UAAU;QACnB,OAAO;IACR;IACA,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;QACzB,OAAO;IACR;IACA,IAAI,MAAM,MAAA,EAAQ;QACjB,OAAO;IACR;IACA,OACE,MAAM,WAAA,IAAe,MAAM,WAAA,CAAY,IAAA,KAAS,YACjD,OAAO,MAAM,MAAA,KAAW;AAE1B;AAEO,SAAS,WAAW,IAAA,EAAY,IAAA,EAA+B;IACrE,IAAI,gBAAgB,UAAU;QAC7B,IAAI,MAAM,mBAAmB,SAAS;YACrC,KAAK,OAAA,CAAQ,OAAA,CAAQ,CAAC,OAAO,QAAQ;gBACpC,KAAK,OAAA,CAAQ,GAAA,CAAI,KAAK,KAAK;YAC5B,CAAC;QACF;QACA,OAAO;IACR;IACA,IAAI,MAAM,UAAU,QAAQ;QAC3B,MAAM,iBAAiB,KAAK,cAAA;QAC5B,IAAI,0BAA0B,UAAU;YACvC,OAAO;QACR;QACA,OAAO,WAAW,KAAK,IAAA,EAAM;YAC5B,SAAS,KAAK,OAAA;YACd,QAAQ,KAAK,MAAA;QACd,CAAC;IACF;IACA,IAAI,WAAW,IAAI,GAAG;QACrB,OAAO,WAAW,KAAK,IAAA,EAAM;YAC5B,QAAQ,KAAK,UAAA;YACb,YAAY,KAAK,MAAA,CAAO,QAAA,CAAS;YACjC,SAAS,MAAM,WAAW,KAAK,OAAA;QAChC,CAAC;IACF;IACA,IAAI,OAAO;IACX,IAAI,UAAU,IAAI,QAAQ,MAAM,OAAO;IACvC,IAAI,CAAC,MAAM;QACV,IAAI,SAAS,MAAM;YAClB,OAAO,KAAK,SAAA,CAAU,IAAI;QAC3B;QACA,QAAQ,GAAA,CAAI,gBAAgB,kBAAkB;IAC/C,OAAA,IAAW,OAAO,SAAS,UAAU;QACpC,OAAO;QACP,QAAQ,GAAA,CAAI,gBAAgB,YAAY;IACzC,OAAA,IAAW,gBAAgB,eAAe,YAAY,MAAA,CAAO,IAAI,GAAG;QACnE,OAAO;QACP,QAAQ,GAAA,CAAI,gBAAgB,0BAA0B;IACvD,OAAA,IAAW,gBAAgB,MAAM;QAChC,OAAO;QACP,QAAQ,GAAA,CAAI,gBAAgB,KAAK,IAAA,IAAQ,0BAA0B;IACpE,OAAA,IAAW,gBAAgB,UAAU;QACpC,OAAO;IACR,OAAA,IAAW,gBAAgB,iBAAiB;QAC3C,OAAO;QACP,QAAQ,GAAA,CAAI,gBAAgB,mCAAmC;IAChE,OAAA,IAAW,gBAAgB,gBAAgB;QAC1C,OAAO;QACP,QAAQ,GAAA,CAAI,gBAAgB,0BAA0B;IACvD,OAAA,IAAW,mBAAmB,IAAI,GAAG;QACpC,OAAO,KAAK,SAAA,CAAU,MAAM,CAAC,KAAK,UAAU;YAC3C,IAAI,OAAO,UAAU,UAAU;gBAC9B,OAAO,MAAM,QAAA,CAAS;YACvB;YACA,OAAO;QACR,CAAC;QACD,QAAQ,GAAA,CAAI,gBAAgB,kBAAkB;IAC/C;IAEA,OAAO,IAAI,SAAS,MAAM;QACzB,GAAG,IAAA;QACH;IACD,CAAC;AACF;;ACnEA,eAAsB,cACrB,OAAA,EACA,UAAkC,CAAC,CAAA,EACL;IAC9B,IAAI,UAAU;QACb,MAAM,QAAQ,IAAA;QACd,OAAO,QAAQ,KAAA;IAChB;IAIA,IAAI,QAAQ,IAAA,EAAM;QACjB,MAAM,SAAS,MAAM,QAAQ,IAAA,CAAK,WAAW,CAAA,CAAE,QAAA,CAAS,QAAQ,IAAI;QACpE,IAAI,OAAO,MAAA,EAAQ;YAClB,OAAO;gBACN,MAAM;gBACN,OAAO,UAAU,OAAO,MAAA,EAAQ,MAAM;YACvC;QACD;QACA,QAAQ,IAAA,GAAO,OAAO,KAAA;IACvB;IAEA,IAAI,QAAQ,KAAA,EAAO;QAClB,MAAM,SAAS,MAAM,QAAQ,KAAA,CAAM,WAAW,CAAA,CAAE,QAAA,CAAS,QAAQ,KAAK;QACtE,IAAI,OAAO,MAAA,EAAQ;YAClB,OAAO;gBACN,MAAM;gBACN,OAAO,UAAU,OAAO,MAAA,EAAQ,OAAO;YACxC;QACD;QACA,QAAQ,KAAA,GAAQ,OAAO,KAAA;IACxB;IACA,IAAI,QAAQ,cAAA,IAAkB,CAAC,QAAQ,OAAA,EAAS;QAC/C,OAAO;YACN,MAAM;YACN,OAAO;gBAAE,SAAS;YAAsB;QACzC;IACD;IACA,IAAI,QAAQ,cAAA,IAAkB,CAAC,QAAQ,OAAA,EAAS;QAC/C,OAAO;YACN,MAAM;YACN,OAAO;gBAAE,SAAS;YAAsB;QACzC;IACD;IACA,OAAO;QACN,MAAM;QACN,OAAO;IACR;AACD;AAEO,SAAS,UAAU,KAAA,EAA0C,UAAA,EAAoB;IACvF,MAAM,gBAA0B,CAAC,CAAA;IAEjC,KAAA,MAAW,SAAS,MAAO;QAC1B,MAAM,UAAU,MAAM,OAAA;QACtB,cAAc,IAAA,CAAK,OAAO;IAC3B;IACA,OAAO;QACN,SAAS,CAAA,QAAA,EAAW,UAAU,CAAA,WAAA,CAAA;IAC/B;AACD;;ACjFA,IAAM,YAAY;IAAE,MAAM;IAAQ,MAAM;AAAU;AAE3C,IAAM,eAAe,OAAO,WAAkC;IACpE,MAAM,YAAY,OAAO,WAAW,WAAW,IAAI,YAAY,EAAE,MAAA,CAAO,MAAM,IAAI;IAClF,OAAO,iNAAM,SAAA,CAAO,SAAA,CAAU,OAAO,WAAW,WAAW,OAAO;QAAC;QAAQ,QAAQ;KAAC;AACrF;AAEO,IAAM,kBAAkB,OAC9B,iBACA,OACA,WACsB;IACtB,IAAI;QACH,MAAM,kBAAkB,KAAK,eAAe;QAC5C,MAAM,YAAY,IAAI,WAAW,gBAAgB,MAAM;QACvD,IAAA,IAAS,IAAI,GAAG,MAAM,gBAAgB,MAAA,EAAQ,IAAI,KAAK,IAAK;YAC3D,SAAA,CAAU,CAAC,CAAA,GAAI,gBAAgB,UAAA,CAAW,CAAC;QAC5C;QACA,OAAO,iNAAM,SAAA,CAAO,MAAA,CAAO,WAAW,QAAQ,WAAW,IAAI,YAAY,EAAE,MAAA,CAAO,KAAK,CAAC;IACzF,EAAA,OAAS,GAAG;QACX,OAAO;IACR;AACD;AAEA,IAAM,gBAAgB,OAAO,OAAe,WAAmD;IAC9F,MAAM,MAAM,MAAM,aAAa,MAAM;IACrC,MAAM,YAAY,iNAAM,SAAA,CAAO,IAAA,CAAK,UAAU,IAAA,EAAM,KAAK,IAAI,YAAY,EAAE,MAAA,CAAO,KAAK,CAAC;IAExF,OAAO,KAAK,OAAO,YAAA,CAAa,GAAG,IAAI,WAAW,SAAS,CAAC,CAAC;AAC9D;AAEO,IAAM,kBAAkB,OAAO,OAAe,WAAkC;IACtF,MAAM,YAAY,MAAM,cAAc,OAAO,MAAM;IACnD,QAAQ,GAAG,KAAK,CAAA,CAAA,EAAI,SAAS,EAAA;IAC7B,QAAQ,mBAAmB,KAAK;IAChC,OAAO;AACR;;ACsDO,IAAM,eAAe,CAAC,KAAa,WAAiC;IAC1E,IAAI,WAAW;IACf,IAAI,QAAQ;QACX,IAAI,WAAW,UAAU;YACxB,WAAW,cAAc;QAC1B,OAAA,IAAW,WAAW,QAAQ;YAC7B,WAAW,YAAY;QACxB,OAAO;YACN,OAAO,KAAA;QACR;IACD;IACA,OAAO;AACR;AAUO,SAAS,aAAa,GAAA,EAAa;IACzC,IAAI,OAAO,QAAQ,UAAU;QAC5B,MAAM,IAAI,UAAU,+BAA+B;IACpD;IAEA,MAAM,UAA+B,aAAA,GAAA,IAAI,IAAI;IAE7C,IAAI,QAAQ;IACZ,MAAO,QAAQ,IAAI,MAAA,CAAQ;QAC1B,MAAM,QAAQ,IAAI,OAAA,CAAQ,KAAK,KAAK;QAEpC,IAAI,UAAU,CAAA,GAAI;YACjB;QACD;QAEA,IAAI,SAAS,IAAI,OAAA,CAAQ,KAAK,KAAK;QAEnC,IAAI,WAAW,CAAA,GAAI;YAClB,SAAS,IAAI,MAAA;QACd,OAAA,IAAW,SAAS,OAAO;YAC1B,QAAQ,IAAI,WAAA,CAAY,KAAK,QAAQ,CAAC,IAAI;YAC1C;QACD;QAEA,MAAM,MAAM,IAAI,KAAA,CAAM,OAAO,KAAK,EAAE,IAAA,CAAK;QACzC,IAAI,CAAC,QAAQ,GAAA,CAAI,GAAG,GAAG;YACtB,IAAI,MAAM,IAAI,KAAA,CAAM,QAAQ,GAAG,MAAM,EAAE,IAAA,CAAK;YAC5C,IAAI,IAAI,WAAA,CAAY,CAAC,MAAM,IAAM;gBAChC,MAAM,IAAI,KAAA,CAAM,GAAG,CAAA,CAAE;YACtB;YACA,QAAQ,GAAA,CAAI,KAAK,UAAU,GAAG,CAAC;QAChC;QAEA,QAAQ,SAAS;IAClB;IAEA,OAAO;AACR;AAEA,IAAM,aAAa,CAAC,KAAa,OAAe,MAAqB,CAAC,CAAA,KAAM;IAC3E,IAAI;IAEJ,IAAI,KAAK,WAAW,UAAU;QAC7B,SAAS,GAAG,CAAA,SAAA,EAAY,GAAG,EAAE,CAAA,CAAA,EAAI,KAAK,EAAA;IACvC,OAAA,IAAW,KAAK,WAAW,QAAQ;QAClC,SAAS,GAAG,CAAA,OAAA,EAAU,GAAG,EAAE,CAAA,CAAA,EAAI,KAAK,EAAA;IACrC,OAAO;QACN,SAAS,GAAG,GAAG,CAAA,CAAA,EAAI,KAAK,EAAA;IACzB;IAEA,IAAI,IAAI,UAAA,CAAW,WAAW,KAAK,CAAC,IAAI,MAAA,EAAQ;QAC/C,IAAI,MAAA,GAAS;IACd;IAEA,IAAI,IAAI,UAAA,CAAW,SAAS,GAAG;QAC9B,IAAI,CAAC,IAAI,MAAA,EAAQ;YAChB,IAAI,MAAA,GAAS;QACd;QAEA,IAAI,IAAI,IAAA,KAAS,KAAK;YACrB,IAAI,IAAA,GAAO;QACZ;QAEA,IAAI,IAAI,MAAA,EAAQ;YACf,IAAI,MAAA,GAAS,KAAA;QACd;IACD;IAEA,IAAI,OAAO,OAAO,IAAI,MAAA,KAAW,YAAY,IAAI,MAAA,IAAU,GAAG;QAC7D,IAAI,IAAI,MAAA,GAAS,QAAU;YAC1B,MAAM,IAAI,MACT;QAEF;QACA,UAAU,CAAA,UAAA,EAAa,KAAK,KAAA,CAAM,IAAI,MAAM,CAAC,EAAA;IAC9C;IAEA,IAAI,IAAI,MAAA,IAAU,IAAI,MAAA,KAAW,QAAQ;QACxC,UAAU,CAAA,SAAA,EAAY,IAAI,MAAM,EAAA;IACjC;IAEA,IAAI,IAAI,IAAA,EAAM;QACb,UAAU,CAAA,OAAA,EAAU,IAAI,IAAI,EAAA;IAC7B;IAEA,IAAI,IAAI,OAAA,EAAS;QAChB,IAAI,IAAI,OAAA,CAAQ,OAAA,CAAQ,IAAI,KAAK,GAAA,CAAI,IAAI,QAAc;YACtD,MAAM,IAAI,MACT;QAEF;QACA,UAAU,CAAA,UAAA,EAAa,IAAI,OAAA,CAAQ,WAAA,CAAY,CAAC,EAAA;IACjD;IAEA,IAAI,IAAI,QAAA,EAAU;QACjB,UAAU;IACX;IAEA,IAAI,IAAI,MAAA,EAAQ;QACf,UAAU;IACX;IAEA,IAAI,IAAI,QAAA,EAAU;QACjB,UAAU,CAAA,WAAA,EAAc,IAAI,QAAA,CAAS,MAAA,CAAO,CAAC,EAAE,WAAA,CAAY,IAAI,IAAI,QAAA,CAAS,KAAA,CAAM,CAAC,CAAC,EAAA;IACrF;IAEA,IAAI,IAAI,WAAA,EAAa;QACpB,IAAI,CAAC,IAAI,MAAA,EAAQ;YAChB,IAAI,MAAA,GAAS;QACd;QACA,UAAU;IACX;IAEA,OAAO;AACR;AAEO,IAAM,kBAAkB,CAAC,KAAa,OAAe,QAAwB;IACnF,QAAQ,mBAAmB,KAAK;IAChC,OAAO,WAAW,KAAK,OAAO,GAAG;AAClC;AAEO,IAAM,wBAAwB,OACpC,KACA,OACA,QACA,QACI;IACJ,QAAQ,MAAM,gBAAgB,OAAO,MAAM;IAC3C,OAAO,WAAW,KAAK,OAAO,GAAG;AAClC;;ACvEO,IAAM,wBAAwB,OACpC,SACA,EACC,OAAA,EACA,IAAA,EACD,KAII;IACJ,MAAM,UAAU,IAAI,QAAQ;IAC5B,MAAM,EAAE,IAAA,EAAM,KAAA,CAAM,CAAA,GAAI,MAAM,cAAc,SAAS,OAAO;IAC5D,IAAI,OAAO;QACV,MAAM,IAAI,SAAS,KAAK;YACvB,SAAS,MAAM,OAAA;YACf,MAAM;QACP,CAAC;IACF;IACA,MAAM,iBACL,aAAa,UACV,QAAQ,OAAA,YAAmB,UAC1B,QAAQ,OAAA,GACR,IAAI,QAAQ,QAAQ,OAAO,IAC5B,aAAa,WAAW,QAAQ,OAAA,YAAmB,UAClD,QAAQ,OAAA,CAAQ,OAAA,GAChB;IACL,MAAM,iBAAiB,gBAAgB,IAAI,QAAQ;IACnD,MAAM,gBAAgB,iBAAiB,aAAa,cAAc,IAAI,KAAA;IACtE,MAAM,kBAAkB;QACvB,GAAG,OAAA;QACH,MAAM,KAAK,IAAA;QACX,OAAO,KAAK,KAAA;QACZ,MAAM,QAAQ,IAAA,IAAQ;QACtB,SAAS,aAAa,WAAW,QAAQ,OAAA,GAAU,QAAQ,OAAA,GAAU,CAAC;QACtE,UAAU,KAAA;QACV,SAAS,SAAS;QAClB,SAAS,SAAS;QAClB,QAAQ,YAAY,UAAU,QAAQ,MAAA,GAAS,KAAA;QAC/C,QAAQ,QAAQ,MAAA;QAChB,WAAW,CAAC,KAAa,UAAkB;YAC1C,QAAQ,GAAA,CAAI,KAAK,KAAK;QACvB;QACA,WAAW,CAAC,QAAgB;YAC3B,IAAI,CAAC,eAAgB,CAAA,OAAO;YAC5B,OAAO,eAAe,GAAA,CAAI,GAAG;QAC9B;QACA,WAAW,CAAC,KAAa,WAAiC;YACzD,MAAM,WAAW,aAAa,KAAK,MAAM;YACzC,IAAI,CAAC,UAAU;gBACd,OAAO;YACR;YACA,OAAO,eAAe,IAAI,QAAQ,KAAK;QACxC;QACA,iBAAiB,OAAO,KAAa,QAAgB,WAAiC;YACrF,MAAM,WAAW,aAAa,KAAK,MAAM;YACzC,IAAI,CAAC,UAAU;gBACd,OAAO;YACR;YACA,MAAM,QAAQ,eAAe,IAAI,QAAQ;YACzC,IAAI,CAAC,OAAO;gBACX,OAAO;YACR;YACA,MAAM,oBAAoB,MAAM,WAAA,CAAY,GAAG;YAC/C,IAAI,oBAAoB,GAAG;gBAC1B,OAAO;YACR;YACA,MAAM,cAAc,MAAM,SAAA,CAAU,GAAG,iBAAiB;YACxD,MAAM,YAAY,MAAM,SAAA,CAAU,oBAAoB,CAAC;YACvD,IAAI,UAAU,MAAA,KAAW,MAAM,CAAC,UAAU,QAAA,CAAS,GAAG,GAAG;gBACxD,OAAO;YACR;YACA,MAAM,YAAY,MAAM,aAAa,MAAM;YAC3C,MAAM,aAAa,MAAM,gBAAgB,WAAW,aAAa,SAAS;YAC1E,OAAO,aAAa,cAAc;QACnC;QACA,WAAW,CAAC,KAAa,OAAeA,aAA4B;YACnE,MAAM,SAAS,gBAAgB,KAAK,OAAOA,QAAO;YAClD,QAAQ,MAAA,CAAO,cAAc,MAAM;YACnC,OAAO;QACR;QACA,iBAAiB,OAChB,KACA,OACA,QACAA,aACI;YACJ,MAAM,SAAS,MAAM,sBAAsB,KAAK,OAAO,QAAQA,QAAO;YACtE,QAAQ,MAAA,CAAO,cAAc,MAAM;YACnC,OAAO;QACR;QACA,UAAU,CAAC,QAAgB;YAC1B,QAAQ,GAAA,CAAI,YAAY,GAAG;YAC3B,OAAO,IAAI,SAAS,SAAS,KAAA,GAAW,OAAO;QAChD;QACA,OAAO,CACN,QACA,MAMAC,aACI;YACJ,OAAO,IAAI,SAAS,QAAQ,MAAMA,QAAO;QAC1C;QACA,MAAM,CACL,MACA,mBAQI;YACJ,IAAI,CAAC,QAAQ,UAAA,EAAY;gBACxB,OAAO;YACR;YACA,OAAO;gBACN,MAAM,gBAAgB,QAAQ;gBAC9B;gBACA,OAAO;YACR;QACD;QACA,iBAAiB;IAClB;IAEA,KAAA,MAAW,cAAc,QAAQ,GAAA,IAAO,CAAC,CAAA,CAAG;QAC3C,MAAM,WAAY,MAAM,WAAW;YAClC,GAAG,eAAA;YACH,eAAe;YACf,YAAY;QACb,CAAC;QAID,IAAI,SAAS,QAAA,EAAU;YACtB,OAAO,MAAA,CAAO,gBAAgB,OAAA,EAAS,SAAS,QAAQ;QACzD;QAIA,IAAI,SAAS,OAAA,EAAS;YACrB,SAAS,OAAA,CAAQ,OAAA,CAAQ,CAAC,OAAO,QAAQ;gBACxC,gBAAgB,eAAA,CAAgB,GAAA,CAAI,KAAK,KAAK;YAC/C,CAAC;QACF;IACD;IACA,OAAO;AACR;;ACzLO,SAAS,iBAAiB,gBAAA,EAAuB,OAAA,EAAe;IACtE,MAAM,kBAAkB,OAAO,aAAqC;QACnE,MAAM,UAAU;QAChB,MAAM,WAAW,OAAO,qBAAqB,aAAa,mBAAmB;QAC7E,MAAM,UAAU,OAAO,qBAAqB,aAAa,CAAC,IAAI;QAC9D,MAAM,kBAAkB,MAAM,sBAAsB,SAAS;YAC5D;YACA,MAAM;QACP,CAAC;QAED,IAAI,CAAC,UAAU;YACd,MAAM,IAAI,MAAM,yBAAyB;QAC1C;QACA,MAAM,WAAW,MAAM,SAAS,eAAsB;QACtD,MAAM,UAAU,gBAAgB,eAAA;QAChC,OAAO,QAAQ,aAAA,GACZ;YACA;YACA;QACD,IACC;IACJ;IACA,gBAAgB,OAAA,GAAU,OAAO,qBAAqB,aAAa,CAAC,IAAI;IACxE,OAAO;AACR;AAkBA,iBAAiB,MAAA,GAAS,CAKzB,SACI;IASJ,SAAS,GAAG,gBAAA,EAAuB,OAAA,EAAe;QACjD,IAAI,OAAO,qBAAqB,YAAY;YAC3C,OAAO,iBACN;gBACC,KAAK,MAAM;YACZ,GACA;QAEF;QACA,IAAI,CAAC,SAAS;YACb,MAAM,IAAI,MAAM,gCAAgC;QACjD;QACA,MAAM,aAAa,iBAClB;YACC,GAAG,gBAAA;YACH,QAAQ;YACR,KAAK,CAAC;mBAAI,MAAM,OAAO,CAAC,CAAA,EAAI;mBAAI,iBAAiB,GAAA,IAAO,CAAC,CAAE;aAAA;QAC5D,GACA;QAED,OAAO;IACR;IACA,OAAO;AACR;;ACkGO,IAAMC,kBAAiB,CAC7B,MACA,SACA,YACI;IAEJ,MAAM,kBAAkB,OAAA,GAIpB,aAGC;QACJ,MAAM,UAAW,QAAA,CAAS,CAAC,CAAA,IAAK,CAAC;QACjC,MAAM,kBAAkB,MAAM,sBAAsB,SAAS;YAC5D;YACA;QACD,CAAC;QACD,MAAM,WAAW,MAAM,QAAQ,eAAsB,EAAE,KAAA,CAAM,OAAO,MAAM;YACzE,IAAI,WAAW,CAAC,GAAG;gBAClB,MAAM,aAAa,QAAQ,UAAA;gBAC3B,IAAI,YAAY;oBACf,MAAM,WAAW,CAAC;gBACnB;gBACA,IAAI,QAAQ,UAAA,EAAY;oBACvB,OAAO;gBACR;YACD;YACA,MAAM;QACP,CAAC;QACD,MAAM,UAAU,gBAAgB,eAAA;QAOhC,OACC,QAAQ,UAAA,GACL,WAAW,UAAU;YACrB;QACD,CAAC,IACA,QAAQ,aAAA,GACP;YACA;YACA;QACD,IACC;IAEN;IACA,gBAAgB,OAAA,GAAU;IAC1B,gBAAgB,IAAA,GAAO;IACvB,OAAO;AACR;AAEAA,gBAAe,MAAA,GAAS,CAAmC,SAAa;IACvE,OAAO,CACN,MACA,SACA,YACI;QACJ,OAAOA,gBACN,MACA;YACC,GAAG,OAAA;YACH,KAAK,CAAC;mBAAI,SAAS,OAAO,CAAC,CAAA,EAAI;mBAAI,MAAM,OAAO,CAAC,CAAE;aAAA;QACpD,GACA;IAEF;AACD;;;AEnYA,IAAI;AAAA,CACH,SAAUC,KAAAA,EAAM;IACbA,MAAK,WAAA,GAAc,CAAC,MAAQ;IAC5B,SAAS,SAAS,IAAA,EAAM,CAAE;IAC1BA,MAAK,QAAA,GAAW;IAChB,SAAS,YAAY,EAAA,EAAI;QACrB,MAAM,IAAI,MAAM;IACpB;IACAA,MAAK,WAAA,GAAc;IACnBA,MAAK,WAAA,GAAc,CAAC,UAAU;QAC1B,MAAM,MAAM,CAAC;QACb,KAAA,MAAW,QAAQ,MAAO;YACtB,GAAA,CAAI,IAAI,CAAA,GAAI;QAChB;QACA,OAAO;IACX;IACAA,MAAK,kBAAA,GAAqB,CAAC,QAAQ;QAC/B,MAAM,YAAYA,MAAK,UAAA,CAAW,GAAG,EAAE,MAAA,CAAO,CAAC,IAAM,OAAO,GAAA,CAAI,GAAA,CAAI,CAAC,CAAC,CAAA,KAAM,QAAQ;QACpF,MAAM,WAAW,CAAC;QAClB,KAAA,MAAW,KAAK,UAAW;YACvB,QAAA,CAAS,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA;QACvB;QACA,OAAOA,MAAK,YAAA,CAAa,QAAQ;IACrC;IACAA,MAAK,YAAA,GAAe,CAAC,QAAQ;QACzB,OAAOA,MAAK,UAAA,CAAW,GAAG,EAAE,GAAA,CAAI,SAAU,CAAA,EAAG;YACzC,OAAO,GAAA,CAAI,CAAC,CAAA;QAChB,CAAC;IACL;IACAA,MAAK,UAAA,GAAa,OAAO,OAAO,IAAA,KAAS,aACnC,CAAC,MAAQ,OAAO,IAAA,CAAK,GAAG,IACxB,CAAC,WAAW;QACV,MAAM,OAAO,CAAC,CAAA;QACd,IAAA,MAAW,OAAO,OAAQ;YACtB,IAAI,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,QAAQ,GAAG,GAAG;gBACnD,KAAK,IAAA,CAAK,GAAG;YACjB;QACJ;QACA,OAAO;IACX;IACJA,MAAK,IAAA,GAAO,CAAC,KAAK,YAAY;QAC1B,KAAA,MAAW,QAAQ,IAAK;YACpB,IAAI,QAAQ,IAAI,GACZ,OAAO;QACf;QACA,OAAO,KAAA;IACX;IACAA,MAAK,SAAA,GAAY,OAAO,OAAO,SAAA,KAAc,aACvC,CAAC,MAAQ,OAAO,SAAA,CAAU,GAAG,IAC7B,CAAC,MAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG,KAAK,KAAK,KAAA,CAAM,GAAG,MAAM;IAC/E,SAAS,WAAW,KAAA,EAAO,YAAY,KAAA,EAAO;QAC1C,OAAO,MACF,GAAA,CAAI,CAAC,MAAS,OAAO,QAAQ,WAAW,CAAA,CAAA,EAAI,GAAG,CAAA,CAAA,CAAA,GAAM,GAAI,EACzD,IAAA,CAAK,SAAS;IACvB;IACAA,MAAK,UAAA,GAAa;IAClBA,MAAK,qBAAA,GAAwB,CAAC,GAAG,UAAU;QACvC,IAAI,OAAO,UAAU,UAAU;YAC3B,OAAO,MAAM,QAAA,CAAS;QAC1B;QACA,OAAO;IACX;AACJ,CAAA,EAAG,QAAA,CAAS,OAAO,CAAC,CAAA,CAAE;AACtB,IAAI;AAAA,CACH,SAAUC,WAAAA,EAAY;IACnBA,YAAW,WAAA,GAAc,CAAC,OAAO,WAAW;QACxC,OAAO;YACH,GAAG,KAAA;YACH,GAAG,MAAA;QACP;IACJ;AACJ,CAAA,EAAG,cAAA,CAAe,aAAa,CAAC,CAAA,CAAE;AAClC,IAAM,gBAAgB,KAAK,WAAA,CAAY;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,IAAM,gBAAgB,CAAC,SAAS;IAC5B,MAAM,IAAI,OAAO;IACjB,OAAQ,GAAG;QACP,KAAK;YACD,OAAO,cAAc,SAAA;QACzB,KAAK;YACD,OAAO,cAAc,MAAA;QACzB,KAAK;YACD,OAAO,MAAM,IAAI,IAAI,cAAc,GAAA,GAAM,cAAc,MAAA;QAC3D,KAAK;YACD,OAAO,cAAc,OAAA;QACzB,KAAK;YACD,OAAO,cAAc,QAAA;QACzB,KAAK;YACD,OAAO,cAAc,MAAA;QACzB,KAAK;YACD,OAAO,cAAc,MAAA;QACzB,KAAK;YACD,IAAI,MAAM,OAAA,CAAQ,IAAI,GAAG;gBACrB,OAAO,cAAc,KAAA;YACzB;YACA,IAAI,SAAS,MAAM;gBACf,OAAO,cAAc,IAAA;YACzB;YACA,IAAI,KAAK,IAAA,IACL,OAAO,KAAK,IAAA,KAAS,cACrB,KAAK,KAAA,IACL,OAAO,KAAK,KAAA,KAAU,YAAY;gBAClC,OAAO,cAAc,OAAA;YACzB;YACA,IAAI,OAAO,QAAQ,eAAe,gBAAgB,KAAK;gBACnD,OAAO,cAAc,GAAA;YACzB;YACA,IAAI,OAAO,QAAQ,eAAe,gBAAgB,KAAK;gBACnD,OAAO,cAAc,GAAA;YACzB;YACA,IAAI,OAAO,SAAS,eAAe,gBAAgB,MAAM;gBACrD,OAAO,cAAc,IAAA;YACzB;YACA,OAAO,cAAc,MAAA;QACzB;YACI,OAAO,cAAc,OAAA;IAC7B;AACJ;AAEA,IAAM,eAAe,KAAK,WAAA,CAAY;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAKD,IAAM,WAAN,MAAM,kBAAiB,MAAM;IACzB,IAAI,SAAS;QACT,OAAO,IAAA,CAAK,MAAA;IAChB;IACA,YAAY,MAAA,CAAQ;QAChB,KAAA,CAAM;QACN,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA;QACf,IAAA,CAAK,QAAA,GAAW,CAAC,QAAQ;YACrB,IAAA,CAAK,MAAA,GAAS,CAAC;mBAAG,IAAA,CAAK,MAAA;gBAAQ,GAAG;aAAA;QACtC;QACA,IAAA,CAAK,SAAA,GAAY,CAAC,OAAO,CAAC,CAAA,KAAM;YAC5B,IAAA,CAAK,MAAA,GAAS,CAAC;mBAAG,IAAA,CAAK,MAAA,EAAQ;mBAAG,IAAI;aAAA;QAC1C;QACA,MAAM,cAAc,WAAW,SAAA;QAC/B,IAAI,OAAO,cAAA,EAAgB;YAEvB,OAAO,cAAA,CAAe,IAAA,EAAM,WAAW;QAC3C,OACK;YACD,IAAA,CAAK,SAAA,GAAY;QACrB;QACA,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,MAAA,GAAS;IAClB;IACA,OAAO,OAAA,EAAS;QACZ,MAAM,SAAS,WACX,SAAU,KAAA,EAAO;YACb,OAAO,MAAM,OAAA;QACjB;QACJ,MAAM,cAAc;YAAE,SAAS,CAAC,CAAA;QAAE;QAClC,MAAM,eAAe,CAAC,UAAU;YAC5B,KAAA,MAAW,SAAS,MAAM,MAAA,CAAQ;gBAC9B,IAAI,MAAM,IAAA,KAAS,iBAAiB;oBAChC,MAAM,WAAA,CAAY,GAAA,CAAI,YAAY;gBACtC,OAAA,IACS,MAAM,IAAA,KAAS,uBAAuB;oBAC3C,aAAa,MAAM,eAAe;gBACtC,OAAA,IACS,MAAM,IAAA,KAAS,qBAAqB;oBACzC,aAAa,MAAM,cAAc;gBACrC,OAAA,IACS,MAAM,IAAA,CAAK,MAAA,KAAW,GAAG;oBAC9B,YAAY,OAAA,CAAQ,IAAA,CAAK,OAAO,KAAK,CAAC;gBAC1C,OACK;oBACD,IAAI,OAAO;oBACX,IAAI,IAAI;oBACR,MAAO,IAAI,MAAM,IAAA,CAAK,MAAA,CAAQ;wBAC1B,MAAM,KAAK,MAAM,IAAA,CAAK,CAAC,CAAA;wBACvB,MAAM,WAAW,MAAM,MAAM,IAAA,CAAK,MAAA,GAAS;wBAC3C,IAAI,CAAC,UAAU;4BACX,IAAA,CAAK,EAAE,CAAA,GAAI,IAAA,CAAK,EAAE,CAAA,IAAK;gCAAE,SAAS,CAAC,CAAA;4BAAE;wBAQzC,OACK;4BACD,IAAA,CAAK,EAAE,CAAA,GAAI,IAAA,CAAK,EAAE,CAAA,IAAK;gCAAE,SAAS,CAAC,CAAA;4BAAE;4BACrC,IAAA,CAAK,EAAE,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,OAAO,KAAK,CAAC;wBACvC;wBACA,OAAO,IAAA,CAAK,EAAE,CAAA;wBACd;oBACJ;gBACJ;YACJ;QACJ;QACA,aAAa,IAAI;QACjB,OAAO;IACX;IACA,OAAO,OAAO,KAAA,EAAO;QACjB,IAAI,CAAA,CAAE,iBAAiB,SAAA,GAAW;YAC9B,MAAM,IAAI,MAAM,CAAA,gBAAA,EAAmB,KAAK,EAAE;QAC9C;IACJ;IACA,WAAW;QACP,OAAO,IAAA,CAAK,OAAA;IAChB;IACA,IAAI,UAAU;QACV,OAAO,KAAK,SAAA,CAAU,IAAA,CAAK,MAAA,EAAQ,KAAK,qBAAA,EAAuB,CAAC;IACpE;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW;IAClC;IACA,QAAQ,SAAS,CAAC,QAAU,MAAM,OAAA,EAAS;QACvC,MAAM,cAAc,CAAC;QACrB,MAAM,aAAa,CAAC,CAAA;QACpB,KAAA,MAAW,OAAO,IAAA,CAAK,MAAA,CAAQ;YAC3B,IAAI,IAAI,IAAA,CAAK,MAAA,GAAS,GAAG;gBACrB,WAAA,CAAY,IAAI,IAAA,CAAK,CAAC,CAAC,CAAA,GAAI,WAAA,CAAY,IAAI,IAAA,CAAK,CAAC,CAAC,CAAA,IAAK,CAAC,CAAA;gBACxD,WAAA,CAAY,IAAI,IAAA,CAAK,CAAC,CAAC,CAAA,CAAE,IAAA,CAAK,OAAO,GAAG,CAAC;YAC7C,OACK;gBACD,WAAW,IAAA,CAAK,OAAO,GAAG,CAAC;YAC/B;QACJ;QACA,OAAO;YAAE;YAAY;QAAY;IACrC;IACA,IAAI,aAAa;QACb,OAAO,IAAA,CAAK,OAAA,CAAQ;IACxB;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,WAAW;IAC1B,MAAM,QAAQ,IAAI,SAAS,MAAM;IACjC,OAAO;AACX;AAEA,IAAM,WAAW,CAAC,OAAO,SAAS;IAC9B,IAAI;IACJ,OAAQ,MAAM,IAAA,EAAM;QAChB,KAAK,aAAa,YAAA;YACd,IAAI,MAAM,QAAA,KAAa,cAAc,SAAA,EAAW;gBAC5C,UAAU;YACd,OACK;gBACD,UAAU,CAAA,SAAA,EAAY,MAAM,QAAQ,CAAA,WAAA,EAAc,MAAM,QAAQ,EAAA;YACpE;YACA;QACJ,KAAK,aAAa,eAAA;YACd,UAAU,CAAA,gCAAA,EAAmC,KAAK,SAAA,CAAU,MAAM,QAAA,EAAU,KAAK,qBAAqB,CAAC,EAAA;YACvG;QACJ,KAAK,aAAa,iBAAA;YACd,UAAU,CAAA,+BAAA,EAAkC,KAAK,UAAA,CAAW,MAAM,IAAA,EAAM,IAAI,CAAC,EAAA;YAC7E;QACJ,KAAK,aAAa,aAAA;YACd,UAAU,CAAA,aAAA,CAAA;YACV;QACJ,KAAK,aAAa,2BAAA;YACd,UAAU,CAAA,sCAAA,EAAyC,KAAK,UAAA,CAAW,MAAM,OAAO,CAAC,EAAA;YACjF;QACJ,KAAK,aAAa,kBAAA;YACd,UAAU,CAAA,6BAAA,EAAgC,KAAK,UAAA,CAAW,MAAM,OAAO,CAAC,CAAA,YAAA,EAAe,MAAM,QAAQ,CAAA,CAAA,CAAA;YACrG;QACJ,KAAK,aAAa,iBAAA;YACd,UAAU,CAAA,0BAAA,CAAA;YACV;QACJ,KAAK,aAAa,mBAAA;YACd,UAAU,CAAA,4BAAA,CAAA;YACV;QACJ,KAAK,aAAa,YAAA;YACd,UAAU,CAAA,YAAA,CAAA;YACV;QACJ,KAAK,aAAa,cAAA;YACd,IAAI,OAAO,MAAM,UAAA,KAAe,UAAU;gBACtC,IAAI,cAAc,MAAM,UAAA,EAAY;oBAChC,UAAU,CAAA,6BAAA,EAAgC,MAAM,UAAA,CAAW,QAAQ,CAAA,CAAA,CAAA;oBACnE,IAAI,OAAO,MAAM,UAAA,CAAW,QAAA,KAAa,UAAU;wBAC/C,UAAU,GAAG,OAAO,CAAA,mDAAA,EAAsD,MAAM,UAAA,CAAW,QAAQ,EAAA;oBACvG;gBACJ,OAAA,IACS,gBAAgB,MAAM,UAAA,EAAY;oBACvC,UAAU,CAAA,gCAAA,EAAmC,MAAM,UAAA,CAAW,UAAU,CAAA,CAAA,CAAA;gBAC5E,OAAA,IACS,cAAc,MAAM,UAAA,EAAY;oBACrC,UAAU,CAAA,8BAAA,EAAiC,MAAM,UAAA,CAAW,QAAQ,CAAA,CAAA,CAAA;gBACxE,OACK;oBACD,KAAK,WAAA,CAAY,MAAM,UAAU;gBACrC;YACJ,OAAA,IACS,MAAM,UAAA,KAAe,SAAS;gBACnC,UAAU,CAAA,QAAA,EAAW,MAAM,UAAU,EAAA;YACzC,OACK;gBACD,UAAU;YACd;YACA;QACJ,KAAK,aAAa,SAAA;YACd,IAAI,MAAM,IAAA,KAAS,SACf,UAAU,CAAA,mBAAA,EAAsB,MAAM,KAAA,GAAQ,YAAY,MAAM,SAAA,GAAY,CAAA,QAAA,CAAA,GAAa,CAAA,SAAA,CAAW,CAAA,CAAA,EAAI,MAAM,OAAO,CAAA,WAAA,CAAA;iBAAA,IAChH,MAAM,IAAA,KAAS,UACpB,UAAU,CAAA,oBAAA,EAAuB,MAAM,KAAA,GAAQ,YAAY,MAAM,SAAA,GAAY,CAAA,QAAA,CAAA,GAAa,CAAA,IAAA,CAAM,CAAA,CAAA,EAAI,MAAM,OAAO,CAAA,aAAA,CAAA;iBAAA,IAC5G,MAAM,IAAA,KAAS,UACpB,UAAU,CAAA,eAAA,EAAkB,MAAM,KAAA,GAC5B,CAAA,iBAAA,CAAA,GACA,MAAM,SAAA,GACF,CAAA,yBAAA,CAAA,GACA,CAAA,aAAA,CAAe,GAAG,MAAM,OAAO,EAAA;iBAAA,IACpC,MAAM,IAAA,KAAS,QACpB,UAAU,CAAA,aAAA,EAAgB,MAAM,KAAA,GAC1B,CAAA,iBAAA,CAAA,GACA,MAAM,SAAA,GACF,CAAA,yBAAA,CAAA,GACA,CAAA,aAAA,CAAe,GAAG,IAAI,KAAK,OAAO,MAAM,OAAO,CAAC,CAAC,EAAA;iBAE3D,UAAU;YACd;QACJ,KAAK,aAAa,OAAA;YACd,IAAI,MAAM,IAAA,KAAS,SACf,UAAU,CAAA,mBAAA,EAAsB,MAAM,KAAA,GAAQ,CAAA,OAAA,CAAA,GAAY,MAAM,SAAA,GAAY,CAAA,OAAA,CAAA,GAAY,CAAA,SAAA,CAAW,CAAA,CAAA,EAAI,MAAM,OAAO,CAAA,WAAA,CAAA;iBAAA,IAC/G,MAAM,IAAA,KAAS,UACpB,UAAU,CAAA,oBAAA,EAAuB,MAAM,KAAA,GAAQ,CAAA,OAAA,CAAA,GAAY,MAAM,SAAA,GAAY,CAAA,OAAA,CAAA,GAAY,CAAA,KAAA,CAAO,CAAA,CAAA,EAAI,MAAM,OAAO,CAAA,aAAA,CAAA;iBAAA,IAC5G,MAAM,IAAA,KAAS,UACpB,UAAU,CAAA,eAAA,EAAkB,MAAM,KAAA,GAC5B,CAAA,OAAA,CAAA,GACA,MAAM,SAAA,GACF,CAAA,qBAAA,CAAA,GACA,CAAA,SAAA,CAAW,CAAA,CAAA,EAAI,MAAM,OAAO,EAAA;iBAAA,IACjC,MAAM,IAAA,KAAS,UACpB,UAAU,CAAA,eAAA,EAAkB,MAAM,KAAA,GAC5B,CAAA,OAAA,CAAA,GACA,MAAM,SAAA,GACF,CAAA,qBAAA,CAAA,GACA,CAAA,SAAA,CAAW,CAAA,CAAA,EAAI,MAAM,OAAO,EAAA;iBAAA,IACjC,MAAM,IAAA,KAAS,QACpB,UAAU,CAAA,aAAA,EAAgB,MAAM,KAAA,GAC1B,CAAA,OAAA,CAAA,GACA,MAAM,SAAA,GACF,CAAA,wBAAA,CAAA,GACA,CAAA,YAAA,CAAc,CAAA,CAAA,EAAI,IAAI,KAAK,OAAO,MAAM,OAAO,CAAC,CAAC,EAAA;iBAE3D,UAAU;YACd;QACJ,KAAK,aAAa,MAAA;YACd,UAAU,CAAA,aAAA,CAAA;YACV;QACJ,KAAK,aAAa,0BAAA;YACd,UAAU,CAAA,wCAAA,CAAA;YACV;QACJ,KAAK,aAAa,eAAA;YACd,UAAU,CAAA,6BAAA,EAAgC,MAAM,UAAU,EAAA;YAC1D;QACJ,KAAK,aAAa,UAAA;YACd,UAAU;YACV;QACJ;YACI,UAAU,KAAK,YAAA;YACf,KAAK,WAAA,CAAY,KAAK;IAC9B;IACA,OAAO;QAAE;IAAQ;AACrB;AAEA,IAAI,mBAAmB;AAIvB,SAAS,cAAc;IACnB,OAAO;AACX;AAEA,IAAM,YAAY,CAAC,WAAW;IAC1B,MAAM,EAAE,IAAA,EAAM,IAAA,EAAM,SAAA,EAAW,SAAA,CAAU,CAAA,GAAI;IAC7C,MAAM,WAAW,CAAC;WAAG,MAAM;WAAI,UAAU,IAAA,IAAQ,CAAC,CAAE;KAAA;IACpD,MAAM,YAAY;QACd,GAAG,SAAA;QACH,MAAM;IACV;IACA,IAAI,UAAU,OAAA,KAAY,KAAA,GAAW;QACjC,OAAO;YACH,GAAG,SAAA;YACH,MAAM;YACN,SAAS,UAAU,OAAA;QACvB;IACJ;IACA,IAAI,eAAe;IACnB,MAAM,OAAO,UACR,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC,EACjB,KAAA,CAAM,EACN,OAAA,CAAQ;IACb,KAAA,MAAW,OAAO,KAAM;QACpB,eAAe,IAAI,WAAW;YAAE;YAAM,cAAc;QAAa,CAAC,EAAE,OAAA;IACxE;IACA,OAAO;QACH,GAAG,SAAA;QACH,MAAM;QACN,SAAS;IACb;AACJ;AAEA,SAAS,kBAAkB,GAAA,EAAK,SAAA,EAAW;IACvC,MAAM,cAAc,YAAY;IAChC,MAAM,QAAQ,UAAU;QACpB;QACA,MAAM,IAAI,IAAA;QACV,MAAM,IAAI,IAAA;QACV,WAAW;YACP,IAAI,MAAA,CAAO,kBAAA;YAAA,yCAAA;YACX,IAAI,cAAA;YAAA,qCAAA;YACJ;YAAA,2BAAA;YACA,gBAAgB,WAAW,KAAA,IAAY;SAC3C,CAAE,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,KAAK;AAChC;AACA,IAAM,cAAN,MAAM,aAAY;IACd,aAAc;QACV,IAAA,CAAK,KAAA,GAAQ;IACjB;IACA,QAAQ;QACJ,IAAI,IAAA,CAAK,KAAA,KAAU,SACf,IAAA,CAAK,KAAA,GAAQ;IACrB;IACA,QAAQ;QACJ,IAAI,IAAA,CAAK,KAAA,KAAU,WACf,IAAA,CAAK,KAAA,GAAQ;IACrB;IACA,OAAO,WAAW,MAAA,EAAQ,OAAA,EAAS;QAC/B,MAAM,aAAa,CAAC,CAAA;QACpB,KAAA,MAAW,KAAK,QAAS;YACrB,IAAI,EAAE,MAAA,KAAW,WACb,OAAO;YACX,IAAI,EAAE,MAAA,KAAW,SACb,OAAO,KAAA,CAAM;YACjB,WAAW,IAAA,CAAK,EAAE,KAAK;QAC3B;QACA,OAAO;YAAE,QAAQ,OAAO,KAAA;YAAO,OAAO;QAAW;IACrD;IACA,aAAa,iBAAiB,MAAA,EAAQ,KAAA,EAAO;QACzC,MAAM,YAAY,CAAC,CAAA;QACnB,KAAA,MAAW,QAAQ,MAAO;YACtB,MAAM,MAAM,MAAM,KAAK,GAAA;YACvB,MAAM,QAAQ,MAAM,KAAK,KAAA;YACzB,UAAU,IAAA,CAAK;gBACX;gBACA;YACJ,CAAC;QACL;QACA,OAAO,aAAY,eAAA,CAAgB,QAAQ,SAAS;IACxD;IACA,OAAO,gBAAgB,MAAA,EAAQ,KAAA,EAAO;QAClC,MAAM,cAAc,CAAC;QACrB,KAAA,MAAW,QAAQ,MAAO;YACtB,MAAM,EAAE,GAAA,EAAK,KAAA,CAAM,CAAA,GAAI;YACvB,IAAI,IAAI,MAAA,KAAW,WACf,OAAO;YACX,IAAI,MAAM,MAAA,KAAW,WACjB,OAAO;YACX,IAAI,IAAI,MAAA,KAAW,SACf,OAAO,KAAA,CAAM;YACjB,IAAI,MAAM,MAAA,KAAW,SACjB,OAAO,KAAA,CAAM;YACjB,IAAI,IAAI,KAAA,KAAU,eAAA,CACb,OAAO,MAAM,KAAA,KAAU,eAAe,KAAK,SAAA,GAAY;gBACxD,WAAA,CAAY,IAAI,KAAK,CAAA,GAAI,MAAM,KAAA;YACnC;QACJ;QACA,OAAO;YAAE,QAAQ,OAAO,KAAA;YAAO,OAAO;QAAY;IACtD;AACJ;AACA,IAAM,UAAU,OAAO,MAAA,CAAO;IAC1B,QAAQ;AACZ,CAAC;AACD,IAAM,QAAQ,CAAC,QAAA,CAAW;QAAE,QAAQ;QAAS;IAAM,CAAA;AACnD,IAAM,KAAK,CAAC,QAAA,CAAW;QAAE,QAAQ;QAAS;IAAM,CAAA;AAChD,IAAM,YAAY,CAAC,IAAM,EAAE,MAAA,KAAW;AACtC,IAAM,UAAU,CAAC,IAAM,EAAE,MAAA,KAAW;AACpC,IAAM,UAAU,CAAC,IAAM,EAAE,MAAA,KAAW;AACpC,IAAM,UAAU,CAAC,IAAM,OAAO,YAAY,eAAe,aAAa;AAiBtE,SAAS,uBAAuB,QAAA,EAAU,KAAA,EAAO,IAAA,EAAM,CAAA,EAAG;IACtD,IAAI,SAAS,OAAO,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,+CAA+C;IAC3F,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAA,CAAI,QAAQ,EAAG,CAAA,MAAM,IAAI,UAAU,0EAA0E;IACjL,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAA,CAAK,QAAQ,IAAI,IAAI,EAAE,KAAA,GAAQ,MAAM,GAAA,CAAI,QAAQ;AAChG;AAEA,SAAS,uBAAuB,QAAA,EAAU,KAAA,EAAO,KAAA,EAAO,IAAA,EAAM,CAAA,EAAG;IAC7D,IAAI,SAAS,IAAK,CAAA,MAAM,IAAI,UAAU,gCAAgC;IACtE,IAAI,SAAS,OAAO,CAAC,EAAG,CAAA,MAAM,IAAI,UAAU,+CAA+C;IAC3F,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAA,CAAI,QAAQ,EAAG,CAAA,MAAM,IAAI,UAAU,yEAAyE;IAChL,OAAQ,SAAS,MAAM,EAAE,IAAA,CAAK,UAAU,KAAK,IAAI,IAAI,EAAE,KAAA,GAAQ,QAAQ,MAAM,GAAA,CAAI,UAAU,KAAK,GAAI;AACxG;AAOA,IAAI;AAAA,CACH,SAAUC,UAAAA,EAAW;IAClBA,WAAU,QAAA,GAAW,CAAC,UAAY,OAAO,YAAY,WAAW;YAAE;QAAQ,IAAI,WAAW,CAAC;IAC1FA,WAAU,QAAA,GAAW,CAAC,UAAY,OAAO,YAAY,WAAW,UAAU,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,OAAA;AACxI,CAAA,EAAG,aAAA,CAAc,YAAY,CAAC,CAAA,CAAE;AAEhC,IAAI;AAAJ,IAAoB;AACpB,IAAM,qBAAN,MAAyB;IACrB,YAAY,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,GAAA,CAAK;QAClC,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA;QACpB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,IAAA,GAAO;IAChB;IACA,IAAI,OAAO;QACP,IAAI,CAAC,IAAA,CAAK,WAAA,CAAY,MAAA,EAAQ;YAC1B,IAAI,IAAA,CAAK,IAAA,YAAgB,OAAO;gBAC5B,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,GAAG,IAAA,CAAK,KAAA,EAAO,GAAG,IAAA,CAAK,IAAI;YACrD,OACK;gBACD,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,GAAG,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,IAAI;YAClD;QACJ;QACA,OAAO,IAAA,CAAK,WAAA;IAChB;AACJ;AACA,IAAM,eAAe,CAAC,KAAK,WAAW;IAClC,IAAI,QAAQ,MAAM,GAAG;QACjB,OAAO;YAAE,SAAS;YAAM,MAAM,OAAO,KAAA;QAAM;IAC/C,OACK;QACD,IAAI,CAAC,IAAI,MAAA,CAAO,MAAA,CAAO,MAAA,EAAQ;YAC3B,MAAM,IAAI,MAAM,2CAA2C;QAC/D;QACA,OAAO;YACH,SAAS;YACT,IAAI,SAAQ;gBACR,IAAI,IAAA,CAAK,MAAA,EACL,OAAO,IAAA,CAAK,MAAA;gBAChB,MAAM,QAAQ,IAAI,SAAS,IAAI,MAAA,CAAO,MAAM;gBAC5C,IAAA,CAAK,MAAA,GAAS;gBACd,OAAO,IAAA,CAAK,MAAA;YAChB;QACJ;IACJ;AACJ;AACA,SAAS,oBAAoB,MAAA,EAAQ;IACjC,IAAI,CAAC,QACD,OAAO,CAAC;IACZ,MAAM,EAAE,UAAAC,SAAAA,EAAU,kBAAA,EAAoB,cAAA,EAAgB,WAAA,CAAY,CAAA,GAAI;IACtE,IAAIA,aAAAA,CAAa,sBAAsB,cAAA,GAAiB;QACpD,MAAM,IAAI,MAAM,CAAA,wFAAA,CAA0F;IAC9G;IACA,IAAIA,WACA,OAAO;QAAE,UAAUA;QAAU;IAAY;IAC7C,MAAM,YAAY,CAAC,KAAK,QAAQ;QAC5B,IAAI,IAAI;QACR,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI;QACpB,IAAI,IAAI,IAAA,KAAS,sBAAsB;YACnC,OAAO;gBAAE,SAAS,YAAY,QAAQ,YAAY,KAAA,IAAS,UAAU,IAAI,YAAA;YAAa;QAC1F;QACA,IAAI,OAAO,IAAI,IAAA,KAAS,aAAa;YACjC,OAAO;gBAAE,SAAA,CAAU,KAAK,YAAY,QAAQ,YAAY,KAAA,IAAS,UAAU,cAAA,MAAoB,QAAQ,OAAO,KAAA,IAAS,KAAK,IAAI,YAAA;YAAa;QACjJ;QACA,IAAI,IAAI,IAAA,KAAS,gBACb,OAAO;YAAE,SAAS,IAAI,YAAA;QAAa;QACvC,OAAO;YAAE,SAAA,CAAU,KAAK,YAAY,QAAQ,YAAY,KAAA,IAAS,UAAU,kBAAA,MAAwB,QAAQ,OAAO,KAAA,IAAS,KAAK,IAAI,YAAA;QAAa;IACrJ;IACA,OAAO;QAAE,UAAU;QAAW;IAAY;AAC9C;AACA,IAAM,UAAN,MAAc;IACV,IAAI,cAAc;QACd,OAAO,IAAA,CAAK,IAAA,CAAK,WAAA;IACrB;IACA,SAAS,KAAA,EAAO;QACZ,OAAO,cAAc,MAAM,IAAI;IACnC;IACA,gBAAgB,KAAA,EAAO,GAAA,EAAK;QACxB,OAAQ,OAAO;YACX,QAAQ,MAAM,MAAA,CAAO,MAAA;YACrB,MAAM,MAAM,IAAA;YACZ,YAAY,cAAc,MAAM,IAAI;YACpC,gBAAgB,IAAA,CAAK,IAAA,CAAK,QAAA;YAC1B,MAAM,MAAM,IAAA;YACZ,QAAQ,MAAM,MAAA;QAClB;IACJ;IACA,oBAAoB,KAAA,EAAO;QACvB,OAAO;YACH,QAAQ,IAAI,YAAY;YACxB,KAAK;gBACD,QAAQ,MAAM,MAAA,CAAO,MAAA;gBACrB,MAAM,MAAM,IAAA;gBACZ,YAAY,cAAc,MAAM,IAAI;gBACpC,gBAAgB,IAAA,CAAK,IAAA,CAAK,QAAA;gBAC1B,MAAM,MAAM,IAAA;gBACZ,QAAQ,MAAM,MAAA;YAClB;QACJ;IACJ;IACA,WAAW,KAAA,EAAO;QACd,MAAM,SAAS,IAAA,CAAK,MAAA,CAAO,KAAK;QAChC,IAAI,QAAQ,MAAM,GAAG;YACjB,MAAM,IAAI,MAAM,wCAAwC;QAC5D;QACA,OAAO;IACX;IACA,YAAY,KAAA,EAAO;QACf,MAAM,SAAS,IAAA,CAAK,MAAA,CAAO,KAAK;QAChC,OAAO,QAAQ,OAAA,CAAQ,MAAM;IACjC;IACA,MAAM,IAAA,EAAM,MAAA,EAAQ;QAChB,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,MAAM,MAAM;QAC1C,IAAI,OAAO,OAAA,EACP,OAAO,OAAO,IAAA;QAClB,MAAM,OAAO,KAAA;IACjB;IACA,UAAU,IAAA,EAAM,MAAA,EAAQ;QACpB,IAAI;QACJ,MAAM,MAAM;YACR,QAAQ;gBACJ,QAAQ,CAAC,CAAA;gBACT,OAAA,CAAQ,KAAK,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,KAAA,MAAW,QAAQ,OAAO,KAAA,IAAS,KAAK;gBAC5G,oBAAoB,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,QAAA;YAC/E;YACA,MAAA,CAAO,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,IAAA,KAAS,CAAC,CAAA;YACxE,gBAAgB,IAAA,CAAK,IAAA,CAAK,QAAA;YAC1B,QAAQ;YACR;YACA,YAAY,cAAc,IAAI;QAClC;QACA,MAAM,SAAS,IAAA,CAAK,UAAA,CAAW;YAAE;YAAM,MAAM,IAAI,IAAA;YAAM,QAAQ;QAAI,CAAC;QACpE,OAAO,aAAa,KAAK,MAAM;IACnC;IACA,YAAY,IAAA,EAAM;QACd,IAAI,IAAI;QACR,MAAM,MAAM;YACR,QAAQ;gBACJ,QAAQ,CAAC,CAAA;gBACT,OAAO,CAAC,CAAC,IAAA,CAAK,WAAW,CAAA,CAAE,KAAA;YAC/B;YACA,MAAM,CAAC,CAAA;YACP,gBAAgB,IAAA,CAAK,IAAA,CAAK,QAAA;YAC1B,QAAQ;YACR;YACA,YAAY,cAAc,IAAI;QAClC;QACA,IAAI,CAAC,IAAA,CAAK,WAAW,CAAA,CAAE,KAAA,EAAO;YAC1B,IAAI;gBACA,MAAM,SAAS,IAAA,CAAK,UAAA,CAAW;oBAAE;oBAAM,MAAM,CAAC,CAAA;oBAAG,QAAQ;gBAAI,CAAC;gBAC9D,OAAO,QAAQ,MAAM,IACf;oBACE,OAAO,OAAO,KAAA;gBAClB,IACE;oBACE,QAAQ,IAAI,MAAA,CAAO,MAAA;gBACvB;YACR,EAAA,OACO,KAAK;gBACR,IAAA,CAAK,KAAA,CAAM,KAAK,QAAQ,QAAQ,QAAQ,KAAA,IAAS,KAAA,IAAS,IAAI,OAAA,MAAa,QAAQ,OAAO,KAAA,IAAS,KAAA,IAAS,GAAG,WAAA,CAAY,CAAA,MAAO,QAAQ,OAAO,KAAA,IAAS,KAAA,IAAS,GAAG,QAAA,CAAS,aAAa,GAAG;oBAC3L,IAAA,CAAK,WAAW,CAAA,CAAE,KAAA,GAAQ;gBAC9B;gBACA,IAAI,MAAA,GAAS;oBACT,QAAQ,CAAC,CAAA;oBACT,OAAO;gBACX;YACJ;QACJ;QACA,OAAO,IAAA,CAAK,WAAA,CAAY;YAAE;YAAM,MAAM,CAAC,CAAA;YAAG,QAAQ;QAAI,CAAC,EAAE,IAAA,CAAK,CAAC,SAAW,QAAQ,MAAM,IAClF;gBACE,OAAO,OAAO,KAAA;YAClB,IACE;gBACE,QAAQ,IAAI,MAAA,CAAO,MAAA;YACvB,CAAC;IACT;IACA,MAAM,WAAW,IAAA,EAAM,MAAA,EAAQ;QAC3B,MAAM,SAAS,MAAM,IAAA,CAAK,cAAA,CAAe,MAAM,MAAM;QACrD,IAAI,OAAO,OAAA,EACP,OAAO,OAAO,IAAA;QAClB,MAAM,OAAO,KAAA;IACjB;IACA,MAAM,eAAe,IAAA,EAAM,MAAA,EAAQ;QAC/B,MAAM,MAAM;YACR,QAAQ;gBACJ,QAAQ,CAAC,CAAA;gBACT,oBAAoB,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,QAAA;gBAC3E,OAAO;YACX;YACA,MAAA,CAAO,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,IAAA,KAAS,CAAC,CAAA;YACxE,gBAAgB,IAAA,CAAK,IAAA,CAAK,QAAA;YAC1B,QAAQ;YACR;YACA,YAAY,cAAc,IAAI;QAClC;QACA,MAAM,mBAAmB,IAAA,CAAK,MAAA,CAAO;YAAE;YAAM,MAAM,IAAI,IAAA;YAAM,QAAQ;QAAI,CAAC;QAC1E,MAAM,SAAS,MAAA,CAAO,QAAQ,gBAAgB,IACxC,mBACA,QAAQ,OAAA,CAAQ,gBAAgB,CAAA;QACtC,OAAO,aAAa,KAAK,MAAM;IACnC;IACA,OAAO,KAAA,EAAO,OAAA,EAAS;QACnB,MAAM,qBAAqB,CAAC,QAAQ;YAChC,IAAI,OAAO,YAAY,YAAY,OAAO,YAAY,aAAa;gBAC/D,OAAO;oBAAE;gBAAQ;YACrB,OAAA,IACS,OAAO,YAAY,YAAY;gBACpC,OAAO,QAAQ,GAAG;YACtB,OACK;gBACD,OAAO;YACX;QACJ;QACA,OAAO,IAAA,CAAK,WAAA,CAAY,CAAC,KAAK,QAAQ;YAClC,MAAM,SAAS,MAAM,GAAG;YACxB,MAAM,WAAW,IAAM,IAAI,QAAA,CAAS;oBAChC,MAAM,aAAa,MAAA;oBACnB,GAAG,mBAAmB,GAAG,CAAA;gBAC7B,CAAC;YACD,IAAI,OAAO,YAAY,eAAe,kBAAkB,SAAS;gBAC7D,OAAO,OAAO,IAAA,CAAK,CAAC,SAAS;oBACzB,IAAI,CAAC,MAAM;wBACP,SAAS;wBACT,OAAO;oBACX,OACK;wBACD,OAAO;oBACX;gBACJ,CAAC;YACL;YACA,IAAI,CAAC,QAAQ;gBACT,SAAS;gBACT,OAAO;YACX,OACK;gBACD,OAAO;YACX;QACJ,CAAC;IACL;IACA,WAAW,KAAA,EAAO,cAAA,EAAgB;QAC9B,OAAO,IAAA,CAAK,WAAA,CAAY,CAAC,KAAK,QAAQ;YAClC,IAAI,CAAC,MAAM,GAAG,GAAG;gBACb,IAAI,QAAA,CAAS,OAAO,mBAAmB,aACjC,eAAe,KAAK,GAAG,IACvB,cAAc;gBACpB,OAAO;YACX,OACK;gBACD,OAAO;YACX;QACJ,CAAC;IACL;IACA,YAAY,UAAA,EAAY;QACpB,OAAO,IAAI,WAAW;YAClB,QAAQ,IAAA;YACR,UAAU,sBAAsB,UAAA;YAChC,QAAQ;gBAAE,MAAM;gBAAc;YAAW;QAC7C,CAAC;IACL;IACA,YAAY,UAAA,EAAY;QACpB,OAAO,IAAA,CAAK,WAAA,CAAY,UAAU;IACtC;IACA,YAAY,GAAA,CAAK;QAEb,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,cAAA;QAChB,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI;QACzC,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAI;QAC3C,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,IAAI;QACnD,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,IAAI;QAC7B,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI;QACnC,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAI;QAC3C,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAI;QAC7C,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAI;QACvC,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAI;QACvC,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;QACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;QACjC,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;QACrC,IAAA,CAAK,EAAA,GAAK,IAAA,CAAK,EAAA,CAAG,IAAA,CAAK,IAAI;QAC3B,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,IAAI;QAC7B,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAI;QACzC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;QACjC,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAI;QACrC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI;QACjC,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAI;QACvC,IAAA,CAAK,IAAA,GAAO,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,IAAI;QAC/B,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAI;QACvC,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAI;QAC3C,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAI;QAC3C,IAAA,CAAK,WAAW,CAAA,GAAI;YAChB,SAAS;YACT,QAAQ;YACR,UAAU,CAAC,OAAS,IAAA,CAAK,WAAW,CAAA,CAAE,IAAI;QAC9C;IACJ;IACA,WAAW;QACP,OAAO,YAAY,MAAA,CAAO,IAAA,EAAM,IAAA,CAAK,IAAI;IAC7C;IACA,WAAW;QACP,OAAO,YAAY,MAAA,CAAO,IAAA,EAAM,IAAA,CAAK,IAAI;IAC7C;IACA,UAAU;QACN,OAAO,IAAA,CAAK,QAAA,CAAS,EAAE,QAAA,CAAS;IACpC;IACA,QAAQ;QACJ,OAAO,SAAS,MAAA,CAAO,IAAI;IAC/B;IACA,UAAU;QACN,OAAO,WAAW,MAAA,CAAO,IAAA,EAAM,IAAA,CAAK,IAAI;IAC5C;IACA,GAAG,MAAA,EAAQ;QACP,OAAO,SAAS,MAAA,CAAO;YAAC,IAAA;YAAM,MAAM;SAAA,EAAG,IAAA,CAAK,IAAI;IACpD;IACA,IAAI,QAAA,EAAU;QACV,OAAO,gBAAgB,MAAA,CAAO,IAAA,EAAM,UAAU,IAAA,CAAK,IAAI;IAC3D;IACA,UAAU,SAAA,EAAW;QACjB,OAAO,IAAI,WAAW;YAClB,GAAG,oBAAoB,IAAA,CAAK,IAAI,CAAA;YAChC,QAAQ,IAAA;YACR,UAAU,sBAAsB,UAAA;YAChC,QAAQ;gBAAE,MAAM;gBAAa;YAAU;QAC3C,CAAC;IACL;IACA,QAAQ,GAAA,EAAK;QACT,MAAM,mBAAmB,OAAO,QAAQ,aAAa,MAAM,IAAM;QACjE,OAAO,IAAI,WAAW;YAClB,GAAG,oBAAoB,IAAA,CAAK,IAAI,CAAA;YAChC,WAAW,IAAA;YACX,cAAc;YACd,UAAU,sBAAsB,UAAA;QACpC,CAAC;IACL;IACA,QAAQ;QACJ,OAAO,IAAI,WAAW;YAClB,UAAU,sBAAsB,UAAA;YAChC,MAAM,IAAA;YACN,GAAG,oBAAoB,IAAA,CAAK,IAAI,CAAA;QACpC,CAAC;IACL;IACA,MAAM,GAAA,EAAK;QACP,MAAM,iBAAiB,OAAO,QAAQ,aAAa,MAAM,IAAM;QAC/D,OAAO,IAAI,SAAS;YAChB,GAAG,oBAAoB,IAAA,CAAK,IAAI,CAAA;YAChC,WAAW,IAAA;YACX,YAAY;YACZ,UAAU,sBAAsB,QAAA;QACpC,CAAC;IACL;IACA,SAAS,WAAA,EAAa;QAClB,MAAM,OAAO,IAAA,CAAK,WAAA;QAClB,OAAO,IAAI,KAAK;YACZ,GAAG,IAAA,CAAK,IAAA;YACR;QACJ,CAAC;IACL;IACA,KAAK,MAAA,EAAQ;QACT,OAAO,YAAY,MAAA,CAAO,IAAA,EAAM,MAAM;IAC1C;IACA,WAAW;QACP,OAAO,YAAY,MAAA,CAAO,IAAI;IAClC;IACA,aAAa;QACT,OAAO,IAAA,CAAK,SAAA,CAAU,KAAA,CAAS,EAAE,OAAA;IACrC;IACA,aAAa;QACT,OAAO,IAAA,CAAK,SAAA,CAAU,IAAI,EAAE,OAAA;IAChC;AACJ;AACA,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,YAAY;AAGlB,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,WAAW;AACjB,IAAM,gBAAgB;AAatB,IAAM,aAAa;AAInB,IAAM,cAAc,CAAA,oDAAA,CAAA;AACpB,IAAI;AAEJ,IAAM,YAAY;AAClB,IAAM,gBAAgB;AAGtB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AAEtB,IAAM,cAAc;AAEpB,IAAM,iBAAiB;AAMvB,IAAM,kBAAkB,CAAA,iMAAA,CAAA;AACxB,IAAM,YAAY,IAAI,OAAO,CAAA,CAAA,EAAI,eAAe,CAAA,CAAA,CAAG;AACnD,SAAS,gBAAgB,IAAA,EAAM;IAE3B,IAAI,QAAQ,CAAA,kCAAA,CAAA;IACZ,IAAI,KAAK,SAAA,EAAW;QAChB,QAAQ,GAAG,KAAK,CAAA,OAAA,EAAU,KAAK,SAAS,CAAA,CAAA,CAAA;IAC5C,OAAA,IACS,KAAK,SAAA,IAAa,MAAM;QAC7B,QAAQ,GAAG,KAAK,CAAA,UAAA,CAAA;IACpB;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAA,EAAM;IACrB,OAAO,IAAI,OAAO,CAAA,CAAA,EAAI,gBAAgB,IAAI,CAAC,CAAA,CAAA,CAAG;AAClD;AAEA,SAAS,cAAc,IAAA,EAAM;IACzB,IAAI,QAAQ,GAAG,eAAe,CAAA,CAAA,EAAI,gBAAgB,IAAI,CAAC,EAAA;IACvD,MAAM,OAAO,CAAC,CAAA;IACd,KAAK,IAAA,CAAK,KAAK,KAAA,GAAQ,CAAA,EAAA,CAAA,GAAO,CAAA,CAAA,CAAG;IACjC,IAAI,KAAK,MAAA,EACL,KAAK,IAAA,CAAK,CAAA,oBAAA,CAAsB;IACpC,QAAQ,GAAG,KAAK,CAAA,CAAA,EAAI,KAAK,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,CAAA;IAClC,OAAO,IAAI,OAAO,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG;AAClC;AACA,SAAS,UAAU,EAAA,EAAI,OAAA,EAAS;IAC5B,IAAA,CAAK,YAAY,QAAQ,CAAC,OAAA,KAAY,UAAU,IAAA,CAAK,EAAE,GAAG;QACtD,OAAO;IACX;IACA,IAAA,CAAK,YAAY,QAAQ,CAAC,OAAA,KAAY,UAAU,IAAA,CAAK,EAAE,GAAG;QACtD,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAA,EAAK,GAAA,EAAK;IAC1B,IAAI,CAAC,SAAS,IAAA,CAAK,GAAG,GAClB,OAAO;IACX,IAAI;QACA,MAAM,CAAC,MAAM,CAAA,GAAI,IAAI,KAAA,CAAM,GAAG;QAE9B,MAAM,SAAS,OACV,OAAA,CAAQ,MAAM,GAAG,EACjB,OAAA,CAAQ,MAAM,GAAG,EACjB,MAAA,CAAO,OAAO,MAAA,GAAA,CAAW,IAAK,OAAO,MAAA,GAAS,CAAA,IAAM,GAAI,GAAG;QAChE,MAAM,UAAU,KAAK,KAAA,CAAM,KAAK,MAAM,CAAC;QACvC,IAAI,OAAO,YAAY,YAAY,YAAY,MAC3C,OAAO;QACX,IAAI,CAAC,QAAQ,GAAA,IAAO,CAAC,QAAQ,GAAA,EACzB,OAAO;QACX,IAAI,OAAO,QAAQ,GAAA,KAAQ,KACvB,OAAO;QACX,OAAO;IACX,EAAA,OACO,IAAI;QACP,OAAO;IACX;AACJ;AACA,SAAS,YAAY,EAAA,EAAI,OAAA,EAAS;IAC9B,IAAA,CAAK,YAAY,QAAQ,CAAC,OAAA,KAAY,cAAc,IAAA,CAAK,EAAE,GAAG;QAC1D,OAAO;IACX;IACA,IAAA,CAAK,YAAY,QAAQ,CAAC,OAAA,KAAY,cAAc,IAAA,CAAK,EAAE,GAAG;QAC1D,OAAO;IACX;IACA,OAAO;AACX;AACA,IAAM,YAAN,MAAM,mBAAkB,QAAQ;IAC5B,OAAO,KAAA,EAAO;QACV,IAAI,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ;YAClB,MAAM,IAAA,GAAO,OAAO,MAAM,IAAI;QAClC;QACA,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,MAAA,EAAQ;YACrC,MAAMC,OAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkBA,MAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAUA,KAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,SAAS,IAAI,YAAY;QAC/B,IAAI,MAAM,KAAA;QACV,KAAA,MAAW,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAClC,IAAI,MAAM,IAAA,KAAS,OAAO;gBACtB,IAAI,MAAM,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA,EAAO;oBACjC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,SAAA;wBACnB,SAAS,MAAM,KAAA;wBACf,MAAM;wBACN,WAAW;wBACX,OAAO;wBACP,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,IAAI,MAAM,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA,EAAO;oBACjC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,OAAA;wBACnB,SAAS,MAAM,KAAA;wBACf,MAAM;wBACN,WAAW;wBACX,OAAO;wBACP,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,UAAU;gBAC9B,MAAM,SAAS,MAAM,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA;gBACzC,MAAM,WAAW,MAAM,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA;gBAC3C,IAAI,UAAU,UAAU;oBACpB,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,IAAI,QAAQ;wBACR,kBAAkB,KAAK;4BACnB,MAAM,aAAa,OAAA;4BACnB,SAAS,MAAM,KAAA;4BACf,MAAM;4BACN,WAAW;4BACX,OAAO;4BACP,SAAS,MAAM,OAAA;wBACnB,CAAC;oBACL,OAAA,IACS,UAAU;wBACf,kBAAkB,KAAK;4BACnB,MAAM,aAAa,SAAA;4BACnB,SAAS,MAAM,KAAA;4BACf,MAAM;4BACN,WAAW;4BACX,OAAO;4BACP,SAAS,MAAM,OAAA;wBACnB,CAAC;oBACL;oBACA,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,SAAS;gBAC7B,IAAI,CAAC,WAAW,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC9B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,SAAS;gBAC7B,IAAI,CAAC,YAAY;oBACb,aAAa,IAAI,OAAO,aAAa,GAAG;gBAC5C;gBACA,IAAI,CAAC,WAAW,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC9B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,IAAI,CAAC,UAAU,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC7B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,UAAU;gBAC9B,IAAI,CAAC,YAAY,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC/B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,IAAI,CAAC,UAAU,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC7B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,SAAS;gBAC7B,IAAI,CAAC,WAAW,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC9B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,IAAI,CAAC,UAAU,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC7B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,IAAI;oBACA,IAAI,IAAI,MAAM,IAAI;gBACtB,EAAA,OACO,IAAI;oBACP,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,SAAS;gBAC7B,MAAM,KAAA,CAAM,SAAA,GAAY;gBACxB,MAAM,aAAa,MAAM,KAAA,CAAM,IAAA,CAAK,MAAM,IAAI;gBAC9C,IAAI,CAAC,YAAY;oBACb,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,MAAM,IAAA,GAAO,MAAM,IAAA,CAAK,IAAA,CAAK;YACjC,OAAA,IACS,MAAM,IAAA,KAAS,YAAY;gBAChC,IAAI,CAAC,MAAM,IAAA,CAAK,QAAA,CAAS,MAAM,KAAA,EAAO,MAAM,QAAQ,GAAG;oBACnD,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;4BAAE,UAAU,MAAM,KAAA;4BAAO,UAAU,MAAM,QAAA;wBAAS;wBAC9D,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,eAAe;gBACnC,MAAM,IAAA,GAAO,MAAM,IAAA,CAAK,WAAA,CAAY;YACxC,OAAA,IACS,MAAM,IAAA,KAAS,eAAe;gBACnC,MAAM,IAAA,GAAO,MAAM,IAAA,CAAK,WAAA,CAAY;YACxC,OAAA,IACS,MAAM,IAAA,KAAS,cAAc;gBAClC,IAAI,CAAC,MAAM,IAAA,CAAK,UAAA,CAAW,MAAM,KAAK,GAAG;oBACrC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;4BAAE,YAAY,MAAM,KAAA;wBAAM;wBACtC,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,YAAY;gBAChC,IAAI,CAAC,MAAM,IAAA,CAAK,QAAA,CAAS,MAAM,KAAK,GAAG;oBACnC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;4BAAE,UAAU,MAAM,KAAA;wBAAM;wBACpC,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,YAAY;gBAChC,MAAM,QAAQ,cAAc,KAAK;gBACjC,IAAI,CAAC,MAAM,IAAA,CAAK,MAAM,IAAI,GAAG;oBACzB,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;wBACZ,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,MAAM,QAAQ;gBACd,IAAI,CAAC,MAAM,IAAA,CAAK,MAAM,IAAI,GAAG;oBACzB,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;wBACZ,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,MAAM,QAAQ,UAAU,KAAK;gBAC7B,IAAI,CAAC,MAAM,IAAA,CAAK,MAAM,IAAI,GAAG;oBACzB,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,cAAA;wBACnB,YAAY;wBACZ,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,YAAY;gBAChC,IAAI,CAAC,cAAc,IAAA,CAAK,MAAM,IAAI,GAAG;oBACjC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,MAAM;gBAC1B,IAAI,CAAC,UAAU,MAAM,IAAA,EAAM,MAAM,OAAO,GAAG;oBACvC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,IAAI,CAAC,WAAW,MAAM,IAAA,EAAM,MAAM,GAAG,GAAG;oBACpC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,QAAQ;gBAC5B,IAAI,CAAC,YAAY,MAAM,IAAA,EAAM,MAAM,OAAO,GAAG;oBACzC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,UAAU;gBAC9B,IAAI,CAAC,YAAY,IAAA,CAAK,MAAM,IAAI,GAAG;oBAC/B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,aAAa;gBACjC,IAAI,CAAC,eAAe,IAAA,CAAK,MAAM,IAAI,GAAG;oBAClC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,YAAY;wBACZ,MAAM,aAAa,cAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OACK;gBACD,KAAK,WAAA,CAAY,KAAK;YAC1B;QACJ;QACA,OAAO;YAAE,QAAQ,OAAO,KAAA;YAAO,OAAO,MAAM,IAAA;QAAK;IACrD;IACA,OAAO,KAAA,EAAO,UAAA,EAAY,OAAA,EAAS;QAC/B,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,OAAS,MAAM,IAAA,CAAK,IAAI,GAAG;YAC/C;YACA,MAAM,aAAa,cAAA;YACnB,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,UAAU,KAAA,EAAO;QACb,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ,KAAK;aAAA;QACvC,CAAC;IACL;IACA,MAAM,OAAA,EAAS;QACX,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAS,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC3E;IACA,IAAI,OAAA,EAAS;QACT,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAO,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IACzE;IACA,MAAM,OAAA,EAAS;QACX,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAS,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC3E;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAQ,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC1E;IACA,OAAO,OAAA,EAAS;QACZ,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAU,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC5E;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAQ,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC1E;IACA,MAAM,OAAA,EAAS;QACX,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAS,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC3E;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAQ,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC1E;IACA,OAAO,OAAA,EAAS;QACZ,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAU,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC5E;IACA,UAAU,OAAA,EAAS;QAEf,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,IAAI,OAAA,EAAS;QACT,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAO,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IACzE;IACA,GAAG,OAAA,EAAS;QACR,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAM,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IACxE;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAQ,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC1E;IACA,SAAS,OAAA,EAAS;QACd,IAAI,IAAI;QACR,IAAI,OAAO,YAAY,UAAU;YAC7B,OAAO,IAAA,CAAK,SAAA,CAAU;gBAClB,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,OAAO;gBACP,SAAS;YACb,CAAC;QACL;QACA,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,WAAW,OAAA,CAAQ,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,SAAA,MAAe,cAAc,OAAO,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,SAAA;YAC3K,QAAA,CAAS,KAAK,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,MAAA,MAAY,QAAQ,OAAO,KAAA,IAAS,KAAK;YACjH,OAAA,CAAQ,KAAK,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,KAAA,MAAW,QAAQ,OAAO,KAAA,IAAS,KAAK;YAC/G,GAAG,UAAU,QAAA,CAAS,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,OAAO,CAAA;QAC3F,CAAC;IACL;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAQ;QAAQ,CAAC;IACnD;IACA,KAAK,OAAA,EAAS;QACV,IAAI,OAAO,YAAY,UAAU;YAC7B,OAAO,IAAA,CAAK,SAAA,CAAU;gBAClB,MAAM;gBACN,WAAW;gBACX,SAAS;YACb,CAAC;QACL;QACA,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,WAAW,OAAA,CAAQ,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,SAAA,MAAe,cAAc,OAAO,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,SAAA;YAC3K,GAAG,UAAU,QAAA,CAAS,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,OAAO,CAAA;QAC3F,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,SAAA,CAAU;YAAE,MAAM;YAAY,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QAAE,CAAC;IAC9E;IACA,MAAM,KAAA,EAAO,OAAA,EAAS;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,SAAS,KAAA,EAAO,OAAA,EAAS;QACrB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,UAAU,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,QAAA;YACpE,GAAG,UAAU,QAAA,CAAS,YAAY,QAAQ,YAAY,KAAA,IAAS,KAAA,IAAS,QAAQ,OAAO,CAAA;QAC3F,CAAC;IACL;IACA,WAAW,KAAA,EAAO,OAAA,EAAS;QACvB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,SAAS,KAAA,EAAO,OAAA,EAAS;QACrB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,IAAI,SAAA,EAAW,OAAA,EAAS;QACpB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,IAAI,SAAA,EAAW,OAAA,EAAS;QACpB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IACA,OAAO,GAAA,EAAK,OAAA,EAAS;QACjB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,GAAG,UAAU,QAAA,CAAS,OAAO,CAAA;QACjC,CAAC;IACL;IAAA;;GAAA,GAIA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG,UAAU,QAAA,CAAS,OAAO,CAAC;IAClD;IACA,OAAO;QACH,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ;oBAAE,MAAM;gBAAO,CAAC;aAAA;QAClD,CAAC;IACL;IACA,cAAc;QACV,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ;oBAAE,MAAM;gBAAc,CAAC;aAAA;QACzD,CAAC;IACL;IACA,cAAc;QACV,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ;oBAAE,MAAM;gBAAc,CAAC;aAAA;QACzD,CAAC;IACL;IACA,IAAI,aAAa;QACb,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,UAAU;IACjE;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,aAAa;QACb,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,UAAU;IACjE;IACA,IAAI,UAAU;QACV,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,OAAO;IAC9D;IACA,IAAI,QAAQ;QACR,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,KAAK;IAC5D;IACA,IAAI,UAAU;QACV,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,OAAO;IAC9D;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,WAAW;QACX,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,QAAQ;IAC/D;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,UAAU;QACV,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,OAAO;IAC9D;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,OAAO;QACP,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,IAAI;IAC3D;IACA,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,MAAM;IAC7D;IACA,IAAI,WAAW;QACX,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,QAAQ;IAC/D;IACA,IAAI,cAAc;QAEd,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,WAAW;IAClE;IACA,IAAI,YAAY;QACZ,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;IACA,IAAI,YAAY;QACZ,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;AACJ;AACA,UAAU,MAAA,GAAS,CAAC,WAAW;IAC3B,IAAI;IACJ,OAAO,IAAI,UAAU;QACjB,QAAQ,CAAC,CAAA;QACT,UAAU,sBAAsB,SAAA;QAChC,QAAA,CAAS,KAAK,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,MAAA,MAAY,QAAQ,OAAO,KAAA,IAAS,KAAK;QAC9G,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AAEA,SAAS,mBAAmB,GAAA,EAAK,IAAA,EAAM;IACnC,MAAM,cAAA,CAAe,IAAI,QAAA,CAAS,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAA,EAAI,MAAA;IACzD,MAAM,eAAA,CAAgB,KAAK,QAAA,CAAS,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAA,EAAI,MAAA;IAC3D,MAAM,WAAW,cAAc,eAAe,cAAc;IAC5D,MAAM,SAAS,SAAS,IAAI,OAAA,CAAQ,QAAQ,EAAE,OAAA,CAAQ,KAAK,EAAE,CAAC;IAC9D,MAAM,UAAU,SAAS,KAAK,OAAA,CAAQ,QAAQ,EAAE,OAAA,CAAQ,KAAK,EAAE,CAAC;IAChE,OAAQ,SAAS,UAAW,KAAK,GAAA,CAAI,IAAI,QAAQ;AACrD;AACA,IAAM,YAAN,MAAM,mBAAkB,QAAQ;IAC5B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA;QAChB,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA;QAChB,IAAA,CAAK,IAAA,GAAO,IAAA,CAAK,UAAA;IACrB;IACA,OAAO,KAAA,EAAO;QACV,IAAI,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ;YAClB,MAAM,IAAA,GAAO,OAAO,MAAM,IAAI;QAClC;QACA,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,MAAA,EAAQ;YACrC,MAAMA,OAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkBA,MAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAUA,KAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,IAAI,MAAM,KAAA;QACV,MAAM,SAAS,IAAI,YAAY;QAC/B,KAAA,MAAW,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAClC,IAAI,MAAM,IAAA,KAAS,OAAO;gBACtB,IAAI,CAAC,KAAK,SAAA,CAAU,MAAM,IAAI,GAAG;oBAC7B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,YAAA;wBACnB,UAAU;wBACV,UAAU;wBACV,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,MAAM,WAAW,MAAM,SAAA,GACjB,MAAM,IAAA,GAAO,MAAM,KAAA,GACnB,MAAM,IAAA,IAAQ,MAAM,KAAA;gBAC1B,IAAI,UAAU;oBACV,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,SAAA;wBACnB,SAAS,MAAM,KAAA;wBACf,MAAM;wBACN,WAAW,MAAM,SAAA;wBACjB,OAAO;wBACP,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,MAAM,SAAS,MAAM,SAAA,GACf,MAAM,IAAA,GAAO,MAAM,KAAA,GACnB,MAAM,IAAA,IAAQ,MAAM,KAAA;gBAC1B,IAAI,QAAQ;oBACR,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,OAAA;wBACnB,SAAS,MAAM,KAAA;wBACf,MAAM;wBACN,WAAW,MAAM,SAAA;wBACjB,OAAO;wBACP,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,cAAc;gBAClC,IAAI,mBAAmB,MAAM,IAAA,EAAM,MAAM,KAAK,MAAM,GAAG;oBACnD,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,eAAA;wBACnB,YAAY,MAAM,KAAA;wBAClB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,UAAU;gBAC9B,IAAI,CAAC,OAAO,QAAA,CAAS,MAAM,IAAI,GAAG;oBAC9B,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,UAAA;wBACnB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OACK;gBACD,KAAK,WAAA,CAAY,KAAK;YAC1B;QACJ;QACA,OAAO;YAAE,QAAQ,OAAO,KAAA;YAAO,OAAO,MAAM,IAAA;QAAK;IACrD;IACA,IAAI,KAAA,EAAO,OAAA,EAAS;QAChB,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,MAAM,UAAU,QAAA,CAAS,OAAO,CAAC;IACxE;IACA,GAAG,KAAA,EAAO,OAAA,EAAS;QACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,OAAO,UAAU,QAAA,CAAS,OAAO,CAAC;IACzE;IACA,IAAI,KAAA,EAAO,OAAA,EAAS;QAChB,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,MAAM,UAAU,QAAA,CAAS,OAAO,CAAC;IACxE;IACA,GAAG,KAAA,EAAO,OAAA,EAAS;QACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,OAAO,UAAU,QAAA,CAAS,OAAO,CAAC;IACzE;IACA,SAAS,IAAA,EAAM,KAAA,EAAO,SAAA,EAAW,OAAA,EAAS;QACtC,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ;mBACD,IAAA,CAAK,IAAA,CAAK,MAAA;gBACb;oBACI;oBACA;oBACA;oBACA,SAAS,UAAU,QAAA,CAAS,OAAO;gBACvC;aACJ;QACJ,CAAC;IACL;IACA,UAAU,KAAA,EAAO;QACb,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ,KAAK;aAAA;QACvC,CAAC;IACL;IACA,IAAI,OAAA,EAAS;QACT,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,YAAY,OAAA,EAAS;QACjB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,YAAY,OAAA,EAAS;QACjB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO;YACP,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,WAAW,KAAA,EAAO,OAAA,EAAS;QACvB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,OAAO,OAAA,EAAS;QACZ,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,KAAK,OAAA,EAAS;QACV,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,WAAW;YACX,OAAO,OAAO,gBAAA;YACd,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC,EAAE,SAAA,CAAU;YACT,MAAM;YACN,WAAW;YACX,OAAO,OAAO,gBAAA;YACd,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;IACA,IAAI,QAAQ;QACR,OAAO,CAAC,CAAC,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,KAAO,GAAG,IAAA,KAAS,SAC9C,GAAG,IAAA,KAAS,gBAAgB,KAAK,SAAA,CAAU,GAAG,KAAK,CAAE;IAC9D;IACA,IAAI,WAAW;QACX,IAAI,MAAM,MAAM,MAAM;QACtB,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,YACZ,GAAG,IAAA,KAAS,SACZ,GAAG,IAAA,KAAS,cAAc;gBAC1B,OAAO;YACX,OAAA,IACS,GAAG,IAAA,KAAS,OAAO;gBACxB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB,OAAA,IACS,GAAG,IAAA,KAAS,OAAO;gBACxB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO,OAAO,QAAA,CAAS,GAAG,KAAK,OAAO,QAAA,CAAS,GAAG;IACtD;AACJ;AACA,UAAU,MAAA,GAAS,CAAC,WAAW;IAC3B,OAAO,IAAI,UAAU;QACjB,QAAQ,CAAC,CAAA;QACT,UAAU,sBAAsB,SAAA;QAChC,QAAA,CAAS,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,MAAA,KAAW;QAC3E,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,YAAN,MAAM,mBAAkB,QAAQ;IAC5B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA;QAChB,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,GAAA;IACpB;IACA,OAAO,KAAA,EAAO;QACV,IAAI,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ;YAClB,IAAI;gBACA,MAAM,IAAA,GAAO,OAAO,MAAM,IAAI;YAClC,EAAA,OACO,IAAI;gBACP,OAAO,IAAA,CAAK,gBAAA,CAAiB,KAAK;YACtC;QACJ;QACA,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,MAAA,EAAQ;YACrC,OAAO,IAAA,CAAK,gBAAA,CAAiB,KAAK;QACtC;QACA,IAAI,MAAM,KAAA;QACV,MAAM,SAAS,IAAI,YAAY;QAC/B,KAAA,MAAW,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAClC,IAAI,MAAM,IAAA,KAAS,OAAO;gBACtB,MAAM,WAAW,MAAM,SAAA,GACjB,MAAM,IAAA,GAAO,MAAM,KAAA,GACnB,MAAM,IAAA,IAAQ,MAAM,KAAA;gBAC1B,IAAI,UAAU;oBACV,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,SAAA;wBACnB,MAAM;wBACN,SAAS,MAAM,KAAA;wBACf,WAAW,MAAM,SAAA;wBACjB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,MAAM,SAAS,MAAM,SAAA,GACf,MAAM,IAAA,GAAO,MAAM,KAAA,GACnB,MAAM,IAAA,IAAQ,MAAM,KAAA;gBAC1B,IAAI,QAAQ;oBACR,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,OAAA;wBACnB,MAAM;wBACN,SAAS,MAAM,KAAA;wBACf,WAAW,MAAM,SAAA;wBACjB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,cAAc;gBAClC,IAAI,MAAM,IAAA,GAAO,MAAM,KAAA,KAAU,OAAO,CAAC,GAAG;oBACxC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,eAAA;wBACnB,YAAY,MAAM,KAAA;wBAClB,SAAS,MAAM,OAAA;oBACnB,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OACK;gBACD,KAAK,WAAA,CAAY,KAAK;YAC1B;QACJ;QACA,OAAO;YAAE,QAAQ,OAAO,KAAA;YAAO,OAAO,MAAM,IAAA;QAAK;IACrD;IACA,iBAAiB,KAAA,EAAO;QACpB,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;QACtC,kBAAkB,KAAK;YACnB,MAAM,aAAa,YAAA;YACnB,UAAU,cAAc,MAAA;YACxB,UAAU,IAAI,UAAA;QAClB,CAAC;QACD,OAAO;IACX;IACA,IAAI,KAAA,EAAO,OAAA,EAAS;QAChB,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,MAAM,UAAU,QAAA,CAAS,OAAO,CAAC;IACxE;IACA,GAAG,KAAA,EAAO,OAAA,EAAS;QACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,OAAO,UAAU,QAAA,CAAS,OAAO,CAAC;IACzE;IACA,IAAI,KAAA,EAAO,OAAA,EAAS;QAChB,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,MAAM,UAAU,QAAA,CAAS,OAAO,CAAC;IACxE;IACA,GAAG,KAAA,EAAO,OAAA,EAAS;QACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAO,OAAO,OAAO,UAAU,QAAA,CAAS,OAAO,CAAC;IACzE;IACA,SAAS,IAAA,EAAM,KAAA,EAAO,SAAA,EAAW,OAAA,EAAS;QACtC,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ;mBACD,IAAA,CAAK,IAAA,CAAK,MAAA;gBACb;oBACI;oBACA;oBACA;oBACA,SAAS,UAAU,QAAA,CAAS,OAAO;gBACvC;aACJ;QACJ,CAAC;IACL;IACA,UAAU,KAAA,EAAO;QACb,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ,KAAK;aAAA;QACvC,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,OAAO,CAAC;YACf,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,OAAO,CAAC;YACf,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,YAAY,OAAA,EAAS;QACjB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,OAAO,CAAC;YACf,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,YAAY,OAAA,EAAS;QACjB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,OAAO,CAAC;YACf,WAAW;YACX,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,WAAW,KAAA,EAAO,OAAA,EAAS;QACvB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN;YACA,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO;IACX;AACJ;AACA,UAAU,MAAA,GAAS,CAAC,WAAW;IAC3B,IAAI;IACJ,OAAO,IAAI,UAAU;QACjB,QAAQ,CAAC,CAAA;QACT,UAAU,sBAAsB,SAAA;QAChC,QAAA,CAAS,KAAK,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,MAAA,MAAY,QAAQ,OAAO,KAAA,IAAS,KAAK;QAC9G,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,OAAO,KAAA,EAAO;QACV,IAAI,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ;YAClB,MAAM,IAAA,GAAO,QAAQ,MAAM,IAAI;QACnC;QACA,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,OAAA,EAAS;YACtC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,OAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,WAAW;IAC5B,OAAO,IAAI,WAAW;QAClB,UAAU,sBAAsB,UAAA;QAChC,QAAA,CAAS,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,MAAA,KAAW;QAC3E,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,UAAN,MAAM,iBAAgB,QAAQ;IAC1B,OAAO,KAAA,EAAO;QACV,IAAI,IAAA,CAAK,IAAA,CAAK,MAAA,EAAQ;YAClB,MAAM,IAAA,GAAO,IAAI,KAAK,MAAM,IAAI;QACpC;QACA,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,IAAA,EAAM;YACnC,MAAMA,OAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkBA,MAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,IAAA;gBACxB,UAAUA,KAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,IAAI,MAAM,MAAM,IAAA,CAAK,OAAA,CAAQ,CAAC,GAAG;YAC7B,MAAMA,OAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkBA,MAAK;gBACnB,MAAM,aAAa,YAAA;YACvB,CAAC;YACD,OAAO;QACX;QACA,MAAM,SAAS,IAAI,YAAY;QAC/B,IAAI,MAAM,KAAA;QACV,KAAA,MAAW,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAClC,IAAI,MAAM,IAAA,KAAS,OAAO;gBACtB,IAAI,MAAM,IAAA,CAAK,OAAA,CAAQ,IAAI,MAAM,KAAA,EAAO;oBACpC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,SAAA;wBACnB,SAAS,MAAM,OAAA;wBACf,WAAW;wBACX,OAAO;wBACP,SAAS,MAAM,KAAA;wBACf,MAAM;oBACV,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,MAAM,IAAA,KAAS,OAAO;gBAC3B,IAAI,MAAM,IAAA,CAAK,OAAA,CAAQ,IAAI,MAAM,KAAA,EAAO;oBACpC,MAAM,IAAA,CAAK,eAAA,CAAgB,OAAO,GAAG;oBACrC,kBAAkB,KAAK;wBACnB,MAAM,aAAa,OAAA;wBACnB,SAAS,MAAM,OAAA;wBACf,WAAW;wBACX,OAAO;wBACP,SAAS,MAAM,KAAA;wBACf,MAAM;oBACV,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OACK;gBACD,KAAK,WAAA,CAAY,KAAK;YAC1B;QACJ;QACA,OAAO;YACH,QAAQ,OAAO,KAAA;YACf,OAAO,IAAI,KAAK,MAAM,IAAA,CAAK,OAAA,CAAQ,CAAC;QACxC;IACJ;IACA,UAAU,KAAA,EAAO;QACb,OAAO,IAAI,SAAQ;YACf,GAAG,IAAA,CAAK,IAAA;YACR,QAAQ,CAAC;mBAAG,IAAA,CAAK,IAAA,CAAK,MAAA;gBAAQ,KAAK;aAAA;QACvC,CAAC;IACL;IACA,IAAI,OAAA,EAAS,OAAA,EAAS;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,QAAQ,OAAA,CAAQ;YACvB,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,IAAI,OAAA,EAAS,OAAA,EAAS;QAClB,OAAO,IAAA,CAAK,SAAA,CAAU;YAClB,MAAM;YACN,OAAO,QAAQ,OAAA,CAAQ;YACvB,SAAS,UAAU,QAAA,CAAS,OAAO;QACvC,CAAC;IACL;IACA,IAAI,UAAU;QACV,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO,OAAO,OAAO,IAAI,KAAK,GAAG,IAAI;IACzC;IACA,IAAI,UAAU;QACV,IAAI,MAAM;QACV,KAAA,MAAW,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAC/B,IAAI,GAAG,IAAA,KAAS,OAAO;gBACnB,IAAI,QAAQ,QAAQ,GAAG,KAAA,GAAQ,KAC3B,MAAM,GAAG,KAAA;YACjB;QACJ;QACA,OAAO,OAAO,OAAO,IAAI,KAAK,GAAG,IAAI;IACzC;AACJ;AACA,QAAQ,MAAA,GAAS,CAAC,WAAW;IACzB,OAAO,IAAI,QAAQ;QACf,QAAQ,CAAC,CAAA;QACT,QAAA,CAAS,WAAW,QAAQ,WAAW,KAAA,IAAS,KAAA,IAAS,OAAO,MAAA,KAAW;QAC3E,UAAU,sBAAsB,OAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,YAAN,cAAwB,QAAQ;IAC5B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,MAAA,EAAQ;YACrC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,UAAU,MAAA,GAAS,CAAC,WAAW;IAC3B,OAAO,IAAI,UAAU;QACjB,UAAU,sBAAsB,SAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,eAAN,cAA2B,QAAQ;IAC/B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,SAAA,EAAW;YACxC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,SAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,aAAa,MAAA,GAAS,CAAC,WAAW;IAC9B,OAAO,IAAI,aAAa;QACpB,UAAU,sBAAsB,YAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,UAAN,cAAsB,QAAQ;IAC1B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,IAAA,EAAM;YACnC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,IAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,QAAQ,MAAA,GAAS,CAAC,WAAW;IACzB,OAAO,IAAI,QAAQ;QACf,UAAU,sBAAsB,OAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,SAAN,cAAqB,QAAQ;IACzB,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAElB,IAAA,CAAK,IAAA,GAAO;IAChB;IACA,OAAO,KAAA,EAAO;QACV,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,OAAO,MAAA,GAAS,CAAC,WAAW;IACxB,OAAO,IAAI,OAAO;QACd,UAAU,sBAAsB,MAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAElB,IAAA,CAAK,QAAA,GAAW;IACpB;IACA,OAAO,KAAA,EAAO;QACV,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,WAAW;IAC5B,OAAO,IAAI,WAAW;QAClB,UAAU,sBAAsB,UAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,WAAN,cAAuB,QAAQ;IAC3B,OAAO,KAAA,EAAO;QACV,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;QACtC,kBAAkB,KAAK;YACnB,MAAM,aAAa,YAAA;YACnB,UAAU,cAAc,KAAA;YACxB,UAAU,IAAI,UAAA;QAClB,CAAC;QACD,OAAO;IACX;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,WAAW;IAC1B,OAAO,IAAI,SAAS;QAChB,UAAU,sBAAsB,QAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,UAAN,cAAsB,QAAQ;IAC1B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,SAAA,EAAW;YACxC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,IAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;AACJ;AACA,QAAQ,MAAA,GAAS,CAAC,WAAW;IACzB,OAAO,IAAI,QAAQ;QACf,UAAU,sBAAsB,OAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,WAAN,MAAM,kBAAiB,QAAQ;IAC3B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,EAAK,MAAA,CAAO,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,MAAM,MAAM,IAAA,CAAK,IAAA;QACjB,IAAI,IAAI,UAAA,KAAe,cAAc,KAAA,EAAO;YACxC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,KAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,IAAI,IAAI,WAAA,KAAgB,MAAM;YAC1B,MAAM,SAAS,IAAI,IAAA,CAAK,MAAA,GAAS,IAAI,WAAA,CAAY,KAAA;YACjD,MAAM,WAAW,IAAI,IAAA,CAAK,MAAA,GAAS,IAAI,WAAA,CAAY,KAAA;YACnD,IAAI,UAAU,UAAU;gBACpB,kBAAkB,KAAK;oBACnB,MAAM,SAAS,aAAa,OAAA,GAAU,aAAa,SAAA;oBACnD,SAAU,WAAW,IAAI,WAAA,CAAY,KAAA,GAAQ,KAAA;oBAC7C,SAAU,SAAS,IAAI,WAAA,CAAY,KAAA,GAAQ,KAAA;oBAC3C,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,SAAS,IAAI,WAAA,CAAY,OAAA;gBAC7B,CAAC;gBACD,OAAO,KAAA,CAAM;YACjB;QACJ;QACA,IAAI,IAAI,SAAA,KAAc,MAAM;YACxB,IAAI,IAAI,IAAA,CAAK,MAAA,GAAS,IAAI,SAAA,CAAU,KAAA,EAAO;gBACvC,kBAAkB,KAAK;oBACnB,MAAM,aAAa,SAAA;oBACnB,SAAS,IAAI,SAAA,CAAU,KAAA;oBACvB,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,SAAS,IAAI,SAAA,CAAU,OAAA;gBAC3B,CAAC;gBACD,OAAO,KAAA,CAAM;YACjB;QACJ;QACA,IAAI,IAAI,SAAA,KAAc,MAAM;YACxB,IAAI,IAAI,IAAA,CAAK,MAAA,GAAS,IAAI,SAAA,CAAU,KAAA,EAAO;gBACvC,kBAAkB,KAAK;oBACnB,MAAM,aAAa,OAAA;oBACnB,SAAS,IAAI,SAAA,CAAU,KAAA;oBACvB,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,SAAS,IAAI,SAAA,CAAU,OAAA;gBAC3B,CAAC;gBACD,OAAO,KAAA,CAAM;YACjB;QACJ;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,GAAA,CAAI,CAAC;mBAAG,IAAI,IAAI;aAAA,CAAE,GAAA,CAAI,CAAC,MAAM,MAAM;gBAC9C,OAAO,IAAI,IAAA,CAAK,WAAA,CAAY,IAAI,mBAAmB,KAAK,MAAM,IAAI,IAAA,EAAM,CAAC,CAAC;YAC9E,CAAC,CAAC,EAAE,IAAA,CAAK,CAACC,YAAW;gBACjB,OAAO,YAAY,UAAA,CAAW,QAAQA,OAAM;YAChD,CAAC;QACL;QACA,MAAM,SAAS,CAAC;eAAG,IAAI,IAAI;SAAA,CAAE,GAAA,CAAI,CAAC,MAAM,MAAM;YAC1C,OAAO,IAAI,IAAA,CAAK,UAAA,CAAW,IAAI,mBAAmB,KAAK,MAAM,IAAI,IAAA,EAAM,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,YAAY,UAAA,CAAW,QAAQ,MAAM;IAChD;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACrB;IACA,IAAI,SAAA,EAAW,OAAA,EAAS;QACpB,OAAO,IAAI,UAAS;YAChB,GAAG,IAAA,CAAK,IAAA;YACR,WAAW;gBAAE,OAAO;gBAAW,SAAS,UAAU,QAAA,CAAS,OAAO;YAAE;QACxE,CAAC;IACL;IACA,IAAI,SAAA,EAAW,OAAA,EAAS;QACpB,OAAO,IAAI,UAAS;YAChB,GAAG,IAAA,CAAK,IAAA;YACR,WAAW;gBAAE,OAAO;gBAAW,SAAS,UAAU,QAAA,CAAS,OAAO;YAAE;QACxE,CAAC;IACL;IACA,OAAO,GAAA,EAAK,OAAA,EAAS;QACjB,OAAO,IAAI,UAAS;YAChB,GAAG,IAAA,CAAK,IAAA;YACR,aAAa;gBAAE,OAAO;gBAAK,SAAS,UAAU,QAAA,CAAS,OAAO;YAAE;QACpE,CAAC;IACL;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG,OAAO;IAC9B;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,QAAQ,WAAW;IAClC,OAAO,IAAI,SAAS;QAChB,MAAM;QACN,WAAW;QACX,WAAW;QACX,aAAa;QACb,UAAU,sBAAsB,QAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,SAAS,eAAe,MAAA,EAAQ;IAC5B,IAAI,kBAAkB,WAAW;QAC7B,MAAM,WAAW,CAAC;QAClB,IAAA,MAAW,OAAO,OAAO,KAAA,CAAO;YAC5B,MAAM,cAAc,OAAO,KAAA,CAAM,GAAG,CAAA;YACpC,QAAA,CAAS,GAAG,CAAA,GAAI,YAAY,MAAA,CAAO,eAAe,WAAW,CAAC;QAClE;QACA,OAAO,IAAI,UAAU;YACjB,GAAG,OAAO,IAAA;YACV,OAAO,IAAM;QACjB,CAAC;IACL,OAAA,IACS,kBAAkB,UAAU;QACjC,OAAO,IAAI,SAAS;YAChB,GAAG,OAAO,IAAA;YACV,MAAM,eAAe,OAAO,OAAO;QACvC,CAAC;IACL,OAAA,IACS,kBAAkB,aAAa;QACpC,OAAO,YAAY,MAAA,CAAO,eAAe,OAAO,MAAA,CAAO,CAAC,CAAC;IAC7D,OAAA,IACS,kBAAkB,aAAa;QACpC,OAAO,YAAY,MAAA,CAAO,eAAe,OAAO,MAAA,CAAO,CAAC,CAAC;IAC7D,OAAA,IACS,kBAAkB,UAAU;QACjC,OAAO,SAAS,MAAA,CAAO,OAAO,KAAA,CAAM,GAAA,CAAI,CAAC,OAAS,eAAe,IAAI,CAAC,CAAC;IAC3E,OACK;QACD,OAAO;IACX;AACJ;AACA,IAAM,YAAN,MAAM,mBAAkB,QAAQ;IAC5B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,IAAA,CAAK,OAAA,GAAU;QAKf,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,WAAA;QAqCtB,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,MAAA;IACxB;IACA,aAAa;QACT,IAAI,IAAA,CAAK,OAAA,KAAY,MACjB,OAAO,IAAA,CAAK,OAAA;QAChB,MAAM,QAAQ,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM;QAC9B,MAAM,OAAO,KAAK,UAAA,CAAW,KAAK;QAClC,OAAQ,IAAA,CAAK,OAAA,GAAU;YAAE;YAAO;QAAK;IACzC;IACA,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,MAAA,EAAQ;YACrC,MAAMD,OAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkBA,MAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAUA,KAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,MAAM,EAAE,KAAA,EAAO,MAAM,SAAA,CAAU,CAAA,GAAI,IAAA,CAAK,UAAA,CAAW;QACnD,MAAM,YAAY,CAAC,CAAA;QACnB,IAAI,CAAA,CAAE,IAAA,CAAK,IAAA,CAAK,QAAA,YAAoB,YAChC,IAAA,CAAK,IAAA,CAAK,WAAA,KAAgB,OAAA,GAAU;YACpC,IAAA,MAAW,OAAO,IAAI,IAAA,CAAM;gBACxB,IAAI,CAAC,UAAU,QAAA,CAAS,GAAG,GAAG;oBAC1B,UAAU,IAAA,CAAK,GAAG;gBACtB;YACJ;QACJ;QACA,MAAM,QAAQ,CAAC,CAAA;QACf,KAAA,MAAW,OAAO,UAAW;YACzB,MAAM,eAAe,KAAA,CAAM,GAAG,CAAA;YAC9B,MAAM,QAAQ,IAAI,IAAA,CAAK,GAAG,CAAA;YAC1B,MAAM,IAAA,CAAK;gBACP,KAAK;oBAAE,QAAQ;oBAAS,OAAO;gBAAI;gBACnC,OAAO,aAAa,MAAA,CAAO,IAAI,mBAAmB,KAAK,OAAO,IAAI,IAAA,EAAM,GAAG,CAAC;gBAC5E,WAAW,OAAO,IAAI,IAAA;YAC1B,CAAC;QACL;QACA,IAAI,IAAA,CAAK,IAAA,CAAK,QAAA,YAAoB,UAAU;YACxC,MAAM,cAAc,IAAA,CAAK,IAAA,CAAK,WAAA;YAC9B,IAAI,gBAAgB,eAAe;gBAC/B,KAAA,MAAW,OAAO,UAAW;oBACzB,MAAM,IAAA,CAAK;wBACP,KAAK;4BAAE,QAAQ;4BAAS,OAAO;wBAAI;wBACnC,OAAO;4BAAE,QAAQ;4BAAS,OAAO,IAAI,IAAA,CAAK,GAAG,CAAA;wBAAE;oBACnD,CAAC;gBACL;YACJ,OAAA,IACS,gBAAgB,UAAU;gBAC/B,IAAI,UAAU,MAAA,GAAS,GAAG;oBACtB,kBAAkB,KAAK;wBACnB,MAAM,aAAa,iBAAA;wBACnB,MAAM;oBACV,CAAC;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ,OAAA,IACS,gBAAgB,QAAS;iBAC7B;gBACD,MAAM,IAAI,MAAM,CAAA,oDAAA,CAAsD;YAC1E;QACJ,OACK;YAED,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,QAAA;YAC3B,KAAA,MAAW,OAAO,UAAW;gBACzB,MAAM,QAAQ,IAAI,IAAA,CAAK,GAAG,CAAA;gBAC1B,MAAM,IAAA,CAAK;oBACP,KAAK;wBAAE,QAAQ;wBAAS,OAAO;oBAAI;oBACnC,OAAO,SAAS,MAAA,CAAO,IAAI,mBAAmB,KAAK,OAAO,IAAI,IAAA,EAAM,GAAG;oBAEvE,WAAW,OAAO,IAAI,IAAA;gBAC1B,CAAC;YACL;QACJ;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,OAAA,CAAQ,EAClB,IAAA,CAAK,YAAY;gBAClB,MAAM,YAAY,CAAC,CAAA;gBACnB,KAAA,MAAW,QAAQ,MAAO;oBACtB,MAAM,MAAM,MAAM,KAAK,GAAA;oBACvB,MAAM,QAAQ,MAAM,KAAK,KAAA;oBACzB,UAAU,IAAA,CAAK;wBACX;wBACA;wBACA,WAAW,KAAK,SAAA;oBACpB,CAAC;gBACL;gBACA,OAAO;YACX,CAAC,EACI,IAAA,CAAK,CAAC,cAAc;gBACrB,OAAO,YAAY,eAAA,CAAgB,QAAQ,SAAS;YACxD,CAAC;QACL,OACK;YACD,OAAO,YAAY,eAAA,CAAgB,QAAQ,KAAK;QACpD;IACJ;IACA,IAAI,QAAQ;QACR,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM;IAC3B;IACA,OAAO,OAAA,EAAS;QACZ,UAAU,QAAA;QACV,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,aAAa;YACb,GAAI,YAAY,KAAA,IACV;gBACE,UAAU,CAAC,OAAO,QAAQ;oBACtB,IAAI,IAAI,IAAI,IAAI;oBAChB,MAAM,eAAA,CAAgB,KAAA,CAAM,KAAA,CAAM,KAAK,IAAA,CAAK,IAAA,EAAM,QAAA,MAAc,QAAQ,OAAO,KAAA,IAAS,KAAA,IAAS,GAAG,IAAA,CAAK,IAAI,OAAO,GAAG,EAAE,OAAA,MAAa,QAAQ,OAAO,KAAA,IAAS,KAAK,IAAI,YAAA;oBACvK,IAAI,MAAM,IAAA,KAAS,qBACf,OAAO;wBACH,SAAA,CAAU,KAAK,UAAU,QAAA,CAAS,OAAO,EAAE,OAAA,MAAa,QAAQ,OAAO,KAAA,IAAS,KAAK;oBACzF;oBACJ,OAAO;wBACH,SAAS;oBACb;gBACJ;YACJ,IACE,CAAC,CAAA;QACX,CAAC;IACL;IACA,QAAQ;QACJ,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,aAAa;QACjB,CAAC;IACL;IACA,cAAc;QACV,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,aAAa;QACjB,CAAC;IACL;IAAA,yBAAA;IAAA,4CAAA;IAAA,wCAAA;IAAA,iCAAA;IAAA,kBAAA;IAAA,2DAAA;IAAA,0BAAA;IAAA,sBAAA;IAAA,WAAA;IAAA,6BAAA;IAAA,gBAAA;IAAA,wBAAA;IAAA,0BAAA;IAAA,2BAAA;IAAA,YAAA;IAAA,iBAAA;IAAA,OAAA;IAkBA,OAAO,YAAA,EAAc;QACjB,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,OAAO,IAAA,CAAO;oBACV,GAAG,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,CAAA;oBACnB,GAAG,YAAA;gBACP,CAAA;QACJ,CAAC;IACL;IAAA;;;;GAAA,GAMA,MAAM,OAAA,EAAS;QACX,MAAM,SAAS,IAAI,WAAU;YACzB,aAAa,QAAQ,IAAA,CAAK,WAAA;YAC1B,UAAU,QAAQ,IAAA,CAAK,QAAA;YACvB,OAAO,IAAA,CAAO;oBACV,GAAG,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,CAAA;oBACnB,GAAG,QAAQ,IAAA,CAAK,KAAA,CAAM,CAAA;gBAC1B,CAAA;YACA,UAAU,sBAAsB,SAAA;QACpC,CAAC;QACD,OAAO;IACX;IAAA,SAAA;IAAA,mCAAA;IAAA,4CAAA;IAAA,wBAAA;IAAA,6EAAA;IAAA,qCAAA;IAAA,iCAAA;IAAA,oBAAA;IAAA,iBAAA;IAAA,OAAA;IAAA,uBAAA;IAAA,4EAAA;IAAA,oCAAA;IAAA,gCAAA;IAAA,mBAAA;IAAA,iBAAA;IAAA,MAAA;IAAA,KAAA;IAAA,sBAAA;IAAA,gBAAA;IAAA,2DAAA;IAAA,qCAAA;IAAA,kCAAA;IAAA,eAAA;IAAA,aAAA;IAAA,MAAA;IAAA,wCAAA;IAAA,6CAAA;IAAA,uCAAA;IAAA,mBAAA;IAAA,yEAAA;IAAA,iDAAA;IAAA,eAAA;IAAA,mBAAA;IAAA,IAAA;IAoCA,OAAO,GAAA,EAAK,MAAA,EAAQ;QAChB,OAAO,IAAA,CAAK,OAAA,CAAQ;YAAE,CAAC,GAAG,CAAA,EAAG;QAAO,CAAC;IACzC;IAAA,wCAAA;IAAA,sBAAA;IAAA,iFAAA;IAAA,aAAA;IAAA,2DAAA;IAAA,qCAAA;IAAA,iCAAA;IAAA,MAAA;IAAA,mDAAA;IAAA,4BAAA;IAAA,8BAAA;IAAA,UAAA;IAAA,wCAAA;IAAA,6CAAA;IAAA,uCAAA;IAAA,mBAAA;IAAA,yEAAA;IAAA,iDAAA;IAAA,eAAA;IAAA,mBAAA;IAAA,IAAA;IAsBA,SAAS,KAAA,EAAO;QACZ,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,UAAU;QACd,CAAC;IACL;IACA,KAAK,IAAA,EAAM;QACP,MAAM,QAAQ,CAAC;QACf,KAAK,UAAA,CAAW,IAAI,EAAE,OAAA,CAAQ,CAAC,QAAQ;YACnC,IAAI,IAAA,CAAK,GAAG,CAAA,IAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG;gBAC9B,KAAA,CAAM,GAAG,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA;YAC/B;QACJ,CAAC;QACD,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,OAAO,IAAM;QACjB,CAAC;IACL;IACA,KAAK,IAAA,EAAM;QACP,MAAM,QAAQ,CAAC;QACf,KAAK,UAAA,CAAW,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAC,QAAQ;YACzC,IAAI,CAAC,IAAA,CAAK,GAAG,CAAA,EAAG;gBACZ,KAAA,CAAM,GAAG,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA;YAC/B;QACJ,CAAC;QACD,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,OAAO,IAAM;QACjB,CAAC;IACL;IAAA;;GAAA,GAIA,cAAc;QACV,OAAO,eAAe,IAAI;IAC9B;IACA,QAAQ,IAAA,EAAM;QACV,MAAM,WAAW,CAAC;QAClB,KAAK,UAAA,CAAW,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAC,QAAQ;YACzC,MAAM,cAAc,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA;YAClC,IAAI,QAAQ,CAAC,IAAA,CAAK,GAAG,CAAA,EAAG;gBACpB,QAAA,CAAS,GAAG,CAAA,GAAI;YACpB,OACK;gBACD,QAAA,CAAS,GAAG,CAAA,GAAI,YAAY,QAAA,CAAS;YACzC;QACJ,CAAC;QACD,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,OAAO,IAAM;QACjB,CAAC;IACL;IACA,SAAS,IAAA,EAAM;QACX,MAAM,WAAW,CAAC;QAClB,KAAK,UAAA,CAAW,IAAA,CAAK,KAAK,EAAE,OAAA,CAAQ,CAAC,QAAQ;YACzC,IAAI,QAAQ,CAAC,IAAA,CAAK,GAAG,CAAA,EAAG;gBACpB,QAAA,CAAS,GAAG,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA;YAClC,OACK;gBACD,MAAM,cAAc,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA;gBAClC,IAAI,WAAW;gBACf,MAAO,oBAAoB,YAAa;oBACpC,WAAW,SAAS,IAAA,CAAK,SAAA;gBAC7B;gBACA,QAAA,CAAS,GAAG,CAAA,GAAI;YACpB;QACJ,CAAC;QACD,OAAO,IAAI,WAAU;YACjB,GAAG,IAAA,CAAK,IAAA;YACR,OAAO,IAAM;QACjB,CAAC;IACL;IACA,QAAQ;QACJ,OAAO,cAAc,KAAK,UAAA,CAAW,IAAA,CAAK,KAAK,CAAC;IACpD;AACJ;AACA,UAAU,MAAA,GAAS,CAAC,OAAO,WAAW;IAClC,OAAO,IAAI,UAAU;QACjB,OAAO,IAAM;QACb,aAAa;QACb,UAAU,SAAS,MAAA,CAAO;QAC1B,UAAU,sBAAsB,SAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,UAAU,YAAA,GAAe,CAAC,OAAO,WAAW;IACxC,OAAO,IAAI,UAAU;QACjB,OAAO,IAAM;QACb,aAAa;QACb,UAAU,SAAS,MAAA,CAAO;QAC1B,UAAU,sBAAsB,SAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,UAAU,UAAA,GAAa,CAAC,OAAO,WAAW;IACtC,OAAO,IAAI,UAAU;QACjB;QACA,aAAa;QACb,UAAU,SAAS,MAAA,CAAO;QAC1B,UAAU,sBAAsB,SAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,WAAN,cAAuB,QAAQ;IAC3B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,MAAM,UAAU,IAAA,CAAK,IAAA,CAAK,OAAA;QAC1B,SAAS,cAAc,OAAA,EAAS;YAE5B,KAAA,MAAW,UAAU,QAAS;gBAC1B,IAAI,OAAO,MAAA,CAAO,MAAA,KAAW,SAAS;oBAClC,OAAO,OAAO,MAAA;gBAClB;YACJ;YACA,KAAA,MAAW,UAAU,QAAS;gBAC1B,IAAI,OAAO,MAAA,CAAO,MAAA,KAAW,SAAS;oBAElC,IAAI,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,GAAG,OAAO,GAAA,CAAI,MAAA,CAAO,MAAM;oBAClD,OAAO,OAAO,MAAA;gBAClB;YACJ;YAEA,MAAM,cAAc,QAAQ,GAAA,CAAI,CAAC,SAAW,IAAI,SAAS,OAAO,GAAA,CAAI,MAAA,CAAO,MAAM,CAAC;YAClF,kBAAkB,KAAK;gBACnB,MAAM,aAAa,aAAA;gBACnB;YACJ,CAAC;YACD,OAAO;QACX;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,GAAA,CAAI,QAAQ,GAAA,CAAI,OAAO,WAAW;gBAC7C,MAAM,WAAW;oBACb,GAAG,GAAA;oBACH,QAAQ;wBACJ,GAAG,IAAI,MAAA;wBACP,QAAQ,CAAC,CAAA;oBACb;oBACA,QAAQ;gBACZ;gBACA,OAAO;oBACH,QAAQ,MAAM,OAAO,WAAA,CAAY;wBAC7B,MAAM,IAAI,IAAA;wBACV,MAAM,IAAI,IAAA;wBACV,QAAQ;oBACZ,CAAC;oBACD,KAAK;gBACT;YACJ,CAAC,CAAC,EAAE,IAAA,CAAK,aAAa;QAC1B,OACK;YACD,IAAI,QAAQ,KAAA;YACZ,MAAM,SAAS,CAAC,CAAA;YAChB,KAAA,MAAW,UAAU,QAAS;gBAC1B,MAAM,WAAW;oBACb,GAAG,GAAA;oBACH,QAAQ;wBACJ,GAAG,IAAI,MAAA;wBACP,QAAQ,CAAC,CAAA;oBACb;oBACA,QAAQ;gBACZ;gBACA,MAAM,SAAS,OAAO,UAAA,CAAW;oBAC7B,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAI,OAAO,MAAA,KAAW,SAAS;oBAC3B,OAAO;gBACX,OAAA,IACS,OAAO,MAAA,KAAW,WAAW,CAAC,OAAO;oBAC1C,QAAQ;wBAAE;wBAAQ,KAAK;oBAAS;gBACpC;gBACA,IAAI,SAAS,MAAA,CAAO,MAAA,CAAO,MAAA,EAAQ;oBAC/B,OAAO,IAAA,CAAK,SAAS,MAAA,CAAO,MAAM;gBACtC;YACJ;YACA,IAAI,OAAO;gBACP,IAAI,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,GAAG,MAAM,GAAA,CAAI,MAAA,CAAO,MAAM;gBACjD,OAAO,MAAM,MAAA;YACjB;YACA,MAAM,cAAc,OAAO,GAAA,CAAI,CAACE,UAAW,IAAI,SAASA,OAAM,CAAC;YAC/D,kBAAkB,KAAK;gBACnB,MAAM,aAAa,aAAA;gBACnB;YACJ,CAAC;YACD,OAAO;QACX;IACJ;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACrB;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,OAAO,WAAW;IACjC,OAAO,IAAI,SAAS;QAChB,SAAS;QACT,UAAU,sBAAsB,QAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AAQA,IAAM,mBAAmB,CAAC,SAAS;IAC/B,IAAI,gBAAgB,SAAS;QACzB,OAAO,iBAAiB,KAAK,MAAM;IACvC,OAAA,IACS,gBAAgB,YAAY;QACjC,OAAO,iBAAiB,KAAK,SAAA,CAAU,CAAC;IAC5C,OAAA,IACS,gBAAgB,YAAY;QACjC,OAAO;YAAC,KAAK,KAAK;SAAA;IACtB,OAAA,IACS,gBAAgB,SAAS;QAC9B,OAAO,KAAK,OAAA;IAChB,OAAA,IACS,gBAAgB,eAAe;QAEpC,OAAO,KAAK,YAAA,CAAa,KAAK,IAAI;IACtC,OAAA,IACS,gBAAgB,YAAY;QACjC,OAAO,iBAAiB,KAAK,IAAA,CAAK,SAAS;IAC/C,OAAA,IACS,gBAAgB,cAAc;QACnC,OAAO;YAAC,KAAA,CAAS;SAAA;IACrB,OAAA,IACS,gBAAgB,SAAS;QAC9B,OAAO;YAAC,IAAI;SAAA;IAChB,OAAA,IACS,gBAAgB,aAAa;QAClC,OAAO;YAAC,KAAA,GAAW;eAAG,iBAAiB,KAAK,MAAA,CAAO,CAAC,CAAC;SAAA;IACzD,OAAA,IACS,gBAAgB,aAAa;QAClC,OAAO;YAAC,MAAM;eAAG,iBAAiB,KAAK,MAAA,CAAO,CAAC,CAAC;SAAA;IACpD,OAAA,IACS,gBAAgB,YAAY;QACjC,OAAO,iBAAiB,KAAK,MAAA,CAAO,CAAC;IACzC,OAAA,IACS,gBAAgB,aAAa;QAClC,OAAO,iBAAiB,KAAK,MAAA,CAAO,CAAC;IACzC,OAAA,IACS,gBAAgB,UAAU;QAC/B,OAAO,iBAAiB,KAAK,IAAA,CAAK,SAAS;IAC/C,OACK;QACD,OAAO,CAAC,CAAA;IACZ;AACJ;AACA,IAAM,wBAAN,MAAM,+BAA8B,QAAQ;IACxC,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,IAAI,IAAI,UAAA,KAAe,cAAc,MAAA,EAAQ;YACzC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,gBAAgB,IAAA,CAAK,aAAA;QAC3B,MAAM,qBAAqB,IAAI,IAAA,CAAK,aAAa,CAAA;QACjD,MAAM,SAAS,IAAA,CAAK,UAAA,CAAW,GAAA,CAAI,kBAAkB;QACrD,IAAI,CAAC,QAAQ;YACT,kBAAkB,KAAK;gBACnB,MAAM,aAAa,2BAAA;gBACnB,SAAS,MAAM,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,CAAC;gBAC1C,MAAM;oBAAC,aAAa;iBAAA;YACxB,CAAC;YACD,OAAO;QACX;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,OAAO,WAAA,CAAY;gBACtB,MAAM,IAAI,IAAA;gBACV,MAAM,IAAI,IAAA;gBACV,QAAQ;YACZ,CAAC;QACL,OACK;YACD,OAAO,OAAO,UAAA,CAAW;gBACrB,MAAM,IAAI,IAAA;gBACV,MAAM,IAAI,IAAA;gBACV,QAAQ;YACZ,CAAC;QACL;IACJ;IACA,IAAI,gBAAgB;QAChB,OAAO,IAAA,CAAK,IAAA,CAAK,aAAA;IACrB;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACrB;IACA,IAAI,aAAa;QACb,OAAO,IAAA,CAAK,IAAA,CAAK,UAAA;IACrB;IAAA;;;;;;;GAAA,GASA,OAAO,OAAO,aAAA,EAAe,OAAA,EAAS,MAAA,EAAQ;QAE1C,MAAM,aAAa,aAAA,GAAA,IAAI,IAAI;QAE3B,KAAA,MAAW,QAAQ,QAAS;YACxB,MAAM,sBAAsB,iBAAiB,KAAK,KAAA,CAAM,aAAa,CAAC;YACtE,IAAI,CAAC,oBAAoB,MAAA,EAAQ;gBAC7B,MAAM,IAAI,MAAM,CAAA,gCAAA,EAAmC,aAAa,CAAA,iDAAA,CAAmD;YACvH;YACA,KAAA,MAAW,SAAS,oBAAqB;gBACrC,IAAI,WAAW,GAAA,CAAI,KAAK,GAAG;oBACvB,MAAM,IAAI,MAAM,CAAA,uBAAA,EAA0B,OAAO,aAAa,CAAC,CAAA,qBAAA,EAAwB,OAAO,KAAK,CAAC,EAAE;gBAC1G;gBACA,WAAW,GAAA,CAAI,OAAO,IAAI;YAC9B;QACJ;QACA,OAAO,IAAI,uBAAsB;YAC7B,UAAU,sBAAsB,qBAAA;YAChC;YACA;YACA;YACA,GAAG,oBAAoB,MAAM,CAAA;QACjC,CAAC;IACL;AACJ;AACA,SAAS,YAAY,CAAA,EAAG,CAAA,EAAG;IACvB,MAAM,QAAQ,cAAc,CAAC;IAC7B,MAAM,QAAQ,cAAc,CAAC;IAC7B,IAAI,MAAM,GAAG;QACT,OAAO;YAAE,OAAO;YAAM,MAAM;QAAE;IAClC,OAAA,IACS,UAAU,cAAc,MAAA,IAAU,UAAU,cAAc,MAAA,EAAQ;QACvE,MAAM,QAAQ,KAAK,UAAA,CAAW,CAAC;QAC/B,MAAM,aAAa,KACd,UAAA,CAAW,CAAC,EACZ,MAAA,CAAO,CAAC,MAAQ,MAAM,OAAA,CAAQ,GAAG,MAAM,CAAA,CAAE;QAC9C,MAAM,SAAS;YAAE,GAAG,CAAA;YAAG,GAAG,CAAA;QAAE;QAC5B,KAAA,MAAW,OAAO,WAAY;YAC1B,MAAM,cAAc,YAAY,CAAA,CAAE,GAAG,CAAA,EAAG,CAAA,CAAE,GAAG,CAAC;YAC9C,IAAI,CAAC,YAAY,KAAA,EAAO;gBACpB,OAAO;oBAAE,OAAO;gBAAM;YAC1B;YACA,MAAA,CAAO,GAAG,CAAA,GAAI,YAAY,IAAA;QAC9B;QACA,OAAO;YAAE,OAAO;YAAM,MAAM;QAAO;IACvC,OAAA,IACS,UAAU,cAAc,KAAA,IAAS,UAAU,cAAc,KAAA,EAAO;QACrE,IAAI,EAAE,MAAA,KAAW,EAAE,MAAA,EAAQ;YACvB,OAAO;gBAAE,OAAO;YAAM;QAC1B;QACA,MAAM,WAAW,CAAC,CAAA;QAClB,IAAA,IAAS,QAAQ,GAAG,QAAQ,EAAE,MAAA,EAAQ,QAAS;YAC3C,MAAM,QAAQ,CAAA,CAAE,KAAK,CAAA;YACrB,MAAM,QAAQ,CAAA,CAAE,KAAK,CAAA;YACrB,MAAM,cAAc,YAAY,OAAO,KAAK;YAC5C,IAAI,CAAC,YAAY,KAAA,EAAO;gBACpB,OAAO;oBAAE,OAAO;gBAAM;YAC1B;YACA,SAAS,IAAA,CAAK,YAAY,IAAI;QAClC;QACA,OAAO;YAAE,OAAO;YAAM,MAAM;QAAS;IACzC,OAAA,IACS,UAAU,cAAc,IAAA,IAC7B,UAAU,cAAc,IAAA,IACxB,CAAC,MAAM,CAAC,GAAG;QACX,OAAO;YAAE,OAAO;YAAM,MAAM;QAAE;IAClC,OACK;QACD,OAAO;YAAE,OAAO;QAAM;IAC1B;AACJ;AACA,IAAM,kBAAN,cAA8B,QAAQ;IAClC,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,MAAM,eAAe,CAAC,YAAY,gBAAgB;YAC9C,IAAI,UAAU,UAAU,KAAK,UAAU,WAAW,GAAG;gBACjD,OAAO;YACX;YACA,MAAM,SAAS,YAAY,WAAW,KAAA,EAAO,YAAY,KAAK;YAC9D,IAAI,CAAC,OAAO,KAAA,EAAO;gBACf,kBAAkB,KAAK;oBACnB,MAAM,aAAa,0BAAA;gBACvB,CAAC;gBACD,OAAO;YACX;YACA,IAAI,QAAQ,UAAU,KAAK,QAAQ,WAAW,GAAG;gBAC7C,OAAO,KAAA,CAAM;YACjB;YACA,OAAO;gBAAE,QAAQ,OAAO,KAAA;gBAAO,OAAO,OAAO,IAAA;YAAK;QACtD;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,GAAA,CAAI;gBACf,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,WAAA,CAAY;oBACvB,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY;oBACxB,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;aACJ,EAAE,IAAA,CAAK,CAAC,CAAC,MAAM,KAAK,CAAA,GAAM,aAAa,MAAM,KAAK,CAAC;QACxD,OACK;YACD,OAAO,aAAa,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW;gBAC1C,MAAM,IAAI,IAAA;gBACV,MAAM,IAAI,IAAA;gBACV,QAAQ;YACZ,CAAC,GAAG,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW;gBAC3B,MAAM,IAAI,IAAA;gBACV,MAAM,IAAI,IAAA;gBACV,QAAQ;YACZ,CAAC,CAAC;QACN;IACJ;AACJ;AACA,gBAAgB,MAAA,GAAS,CAAC,MAAM,OAAO,WAAW;IAC9C,OAAO,IAAI,gBAAgB;QACvB;QACA;QACA,UAAU,sBAAsB,eAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,WAAN,MAAM,kBAAiB,QAAQ;IAC3B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,IAAI,IAAI,UAAA,KAAe,cAAc,KAAA,EAAO;YACxC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,KAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,IAAI,IAAI,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAA,EAAQ;YAC1C,kBAAkB,KAAK;gBACnB,MAAM,aAAa,SAAA;gBACnB,SAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAA;gBACzB,WAAW;gBACX,OAAO;gBACP,MAAM;YACV,CAAC;YACD,OAAO;QACX;QACA,MAAM,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;QACvB,IAAI,CAAC,QAAQ,IAAI,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAA,EAAQ;YACnD,kBAAkB,KAAK;gBACnB,MAAM,aAAa,OAAA;gBACnB,SAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAA;gBACzB,WAAW;gBACX,OAAO;gBACP,MAAM;YACV,CAAC;YACD,OAAO,KAAA,CAAM;QACjB;QACA,MAAM,QAAQ,CAAC;eAAG,IAAI,IAAI;SAAA,CACrB,GAAA,CAAI,CAAC,MAAM,cAAc;YAC1B,MAAM,SAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,SAAS,CAAA,IAAK,IAAA,CAAK,IAAA,CAAK,IAAA;YACvD,IAAI,CAAC,QACD,OAAO;YACX,OAAO,OAAO,MAAA,CAAO,IAAI,mBAAmB,KAAK,MAAM,IAAI,IAAA,EAAM,SAAS,CAAC;QAC/E,CAAC,EACI,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,GAAA,CAAI,KAAK,EAAE,IAAA,CAAK,CAAC,YAAY;gBACxC,OAAO,YAAY,UAAA,CAAW,QAAQ,OAAO;YACjD,CAAC;QACL,OACK;YACD,OAAO,YAAY,UAAA,CAAW,QAAQ,KAAK;QAC/C;IACJ;IACA,IAAI,QAAQ;QACR,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA;IACrB;IACA,KAAK,IAAA,EAAM;QACP,OAAO,IAAI,UAAS;YAChB,GAAG,IAAA,CAAK,IAAA;YACR;QACJ,CAAC;IACL;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,SAAS,WAAW;IACnC,IAAI,CAAC,MAAM,OAAA,CAAQ,OAAO,GAAG;QACzB,MAAM,IAAI,MAAM,uDAAuD;IAC3E;IACA,OAAO,IAAI,SAAS;QAChB,OAAO;QACP,UAAU,sBAAsB,QAAA;QAChC,MAAM;QACN,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,YAAN,MAAM,mBAAkB,QAAQ;IAC5B,IAAI,YAAY;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACrB;IACA,IAAI,cAAc;QACd,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,IAAI,IAAI,UAAA,KAAe,cAAc,MAAA,EAAQ;YACzC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,MAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,QAAQ,CAAC,CAAA;QACf,MAAM,UAAU,IAAA,CAAK,IAAA,CAAK,OAAA;QAC1B,MAAM,YAAY,IAAA,CAAK,IAAA,CAAK,SAAA;QAC5B,IAAA,MAAW,OAAO,IAAI,IAAA,CAAM;YACxB,MAAM,IAAA,CAAK;gBACP,KAAK,QAAQ,MAAA,CAAO,IAAI,mBAAmB,KAAK,KAAK,IAAI,IAAA,EAAM,GAAG,CAAC;gBACnE,OAAO,UAAU,MAAA,CAAO,IAAI,mBAAmB,KAAK,IAAI,IAAA,CAAK,GAAG,CAAA,EAAG,IAAI,IAAA,EAAM,GAAG,CAAC;gBACjF,WAAW,OAAO,IAAI,IAAA;YAC1B,CAAC;QACL;QACA,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,YAAY,gBAAA,CAAiB,QAAQ,KAAK;QACrD,OACK;YACD,OAAO,YAAY,eAAA,CAAgB,QAAQ,KAAK;QACpD;IACJ;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;IACA,OAAO,OAAO,KAAA,EAAO,MAAA,EAAQ,KAAA,EAAO;QAChC,IAAI,kBAAkB,SAAS;YAC3B,OAAO,IAAI,WAAU;gBACjB,SAAS;gBACT,WAAW;gBACX,UAAU,sBAAsB,SAAA;gBAChC,GAAG,oBAAoB,KAAK,CAAA;YAChC,CAAC;QACL;QACA,OAAO,IAAI,WAAU;YACjB,SAAS,UAAU,MAAA,CAAO;YAC1B,WAAW;YACX,UAAU,sBAAsB,SAAA;YAChC,GAAG,oBAAoB,MAAM,CAAA;QACjC,CAAC;IACL;AACJ;AACA,IAAM,SAAN,cAAqB,QAAQ;IACzB,IAAI,YAAY;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACrB;IACA,IAAI,cAAc;QACd,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,IAAI,IAAI,UAAA,KAAe,cAAc,GAAA,EAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,GAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,UAAU,IAAA,CAAK,IAAA,CAAK,OAAA;QAC1B,MAAM,YAAY,IAAA,CAAK,IAAA,CAAK,SAAA;QAC5B,MAAM,QAAQ,CAAC;eAAG,IAAI,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA,CAAE,GAAA,CAAI,CAAC,CAAC,KAAK,KAAK,CAAA,EAAG,UAAU;YAC/D,OAAO;gBACH,KAAK,QAAQ,MAAA,CAAO,IAAI,mBAAmB,KAAK,KAAK,IAAI,IAAA,EAAM;oBAAC;oBAAO,KAAK;iBAAC,CAAC;gBAC9E,OAAO,UAAU,MAAA,CAAO,IAAI,mBAAmB,KAAK,OAAO,IAAI,IAAA,EAAM;oBAAC;oBAAO,OAAO;iBAAC,CAAC;YAC1F;QACJ,CAAC;QACD,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,MAAM,WAAW,aAAA,GAAA,IAAI,IAAI;YACzB,OAAO,QAAQ,OAAA,CAAQ,EAAE,IAAA,CAAK,YAAY;gBACtC,KAAA,MAAW,QAAQ,MAAO;oBACtB,MAAM,MAAM,MAAM,KAAK,GAAA;oBACvB,MAAM,QAAQ,MAAM,KAAK,KAAA;oBACzB,IAAI,IAAI,MAAA,KAAW,aAAa,MAAM,MAAA,KAAW,WAAW;wBACxD,OAAO;oBACX;oBACA,IAAI,IAAI,MAAA,KAAW,WAAW,MAAM,MAAA,KAAW,SAAS;wBACpD,OAAO,KAAA,CAAM;oBACjB;oBACA,SAAS,GAAA,CAAI,IAAI,KAAA,EAAO,MAAM,KAAK;gBACvC;gBACA,OAAO;oBAAE,QAAQ,OAAO,KAAA;oBAAO,OAAO;gBAAS;YACnD,CAAC;QACL,OACK;YACD,MAAM,WAAW,aAAA,GAAA,IAAI,IAAI;YACzB,KAAA,MAAW,QAAQ,MAAO;gBACtB,MAAM,MAAM,KAAK,GAAA;gBACjB,MAAM,QAAQ,KAAK,KAAA;gBACnB,IAAI,IAAI,MAAA,KAAW,aAAa,MAAM,MAAA,KAAW,WAAW;oBACxD,OAAO;gBACX;gBACA,IAAI,IAAI,MAAA,KAAW,WAAW,MAAM,MAAA,KAAW,SAAS;oBACpD,OAAO,KAAA,CAAM;gBACjB;gBACA,SAAS,GAAA,CAAI,IAAI,KAAA,EAAO,MAAM,KAAK;YACvC;YACA,OAAO;gBAAE,QAAQ,OAAO,KAAA;gBAAO,OAAO;YAAS;QACnD;IACJ;AACJ;AACA,OAAO,MAAA,GAAS,CAAC,SAAS,WAAW,WAAW;IAC5C,OAAO,IAAI,OAAO;QACd;QACA;QACA,UAAU,sBAAsB,MAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,SAAN,MAAM,gBAAe,QAAQ;IACzB,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,IAAI,IAAI,UAAA,KAAe,cAAc,GAAA,EAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,GAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,MAAM,IAAA,CAAK,IAAA;QACjB,IAAI,IAAI,OAAA,KAAY,MAAM;YACtB,IAAI,IAAI,IAAA,CAAK,IAAA,GAAO,IAAI,OAAA,CAAQ,KAAA,EAAO;gBACnC,kBAAkB,KAAK;oBACnB,MAAM,aAAa,SAAA;oBACnB,SAAS,IAAI,OAAA,CAAQ,KAAA;oBACrB,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,SAAS,IAAI,OAAA,CAAQ,OAAA;gBACzB,CAAC;gBACD,OAAO,KAAA,CAAM;YACjB;QACJ;QACA,IAAI,IAAI,OAAA,KAAY,MAAM;YACtB,IAAI,IAAI,IAAA,CAAK,IAAA,GAAO,IAAI,OAAA,CAAQ,KAAA,EAAO;gBACnC,kBAAkB,KAAK;oBACnB,MAAM,aAAa,OAAA;oBACnB,SAAS,IAAI,OAAA,CAAQ,KAAA;oBACrB,MAAM;oBACN,WAAW;oBACX,OAAO;oBACP,SAAS,IAAI,OAAA,CAAQ,OAAA;gBACzB,CAAC;gBACD,OAAO,KAAA,CAAM;YACjB;QACJ;QACA,MAAM,YAAY,IAAA,CAAK,IAAA,CAAK,SAAA;QAC5B,SAAS,YAAYC,SAAAA,EAAU;YAC3B,MAAM,YAAY,aAAA,GAAA,IAAI,IAAI;YAC1B,KAAA,MAAW,WAAWA,UAAU;gBAC5B,IAAI,QAAQ,MAAA,KAAW,WACnB,OAAO;gBACX,IAAI,QAAQ,MAAA,KAAW,SACnB,OAAO,KAAA,CAAM;gBACjB,UAAU,GAAA,CAAI,QAAQ,KAAK;YAC/B;YACA,OAAO;gBAAE,QAAQ,OAAO,KAAA;gBAAO,OAAO;YAAU;QACpD;QACA,MAAM,WAAW,CAAC;eAAG,IAAI,IAAA,CAAK,MAAA,CAAO,CAAC;SAAA,CAAE,GAAA,CAAI,CAAC,MAAM,IAAM,UAAU,MAAA,CAAO,IAAI,mBAAmB,KAAK,MAAM,IAAI,IAAA,EAAM,CAAC,CAAC,CAAC;QACzH,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,OAAO,QAAQ,GAAA,CAAI,QAAQ,EAAE,IAAA,CAAK,CAACA,YAAa,YAAYA,SAAQ,CAAC;QACzE,OACK;YACD,OAAO,YAAY,QAAQ;QAC/B;IACJ;IACA,IAAI,OAAA,EAAS,OAAA,EAAS;QAClB,OAAO,IAAI,QAAO;YACd,GAAG,IAAA,CAAK,IAAA;YACR,SAAS;gBAAE,OAAO;gBAAS,SAAS,UAAU,QAAA,CAAS,OAAO;YAAE;QACpE,CAAC;IACL;IACA,IAAI,OAAA,EAAS,OAAA,EAAS;QAClB,OAAO,IAAI,QAAO;YACd,GAAG,IAAA,CAAK,IAAA;YACR,SAAS;gBAAE,OAAO;gBAAS,SAAS,UAAU,QAAA,CAAS,OAAO;YAAE;QACpE,CAAC;IACL;IACA,KAAK,IAAA,EAAM,OAAA,EAAS;QAChB,OAAO,IAAA,CAAK,GAAA,CAAI,MAAM,OAAO,EAAE,GAAA,CAAI,MAAM,OAAO;IACpD;IACA,SAAS,OAAA,EAAS;QACd,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG,OAAO;IAC9B;AACJ;AACA,OAAO,MAAA,GAAS,CAAC,WAAW,WAAW;IACnC,OAAO,IAAI,OAAO;QACd;QACA,SAAS;QACT,SAAS;QACT,UAAU,sBAAsB,MAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,cAAN,MAAM,qBAAoB,QAAQ;IAC9B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,SAAA;IACzB;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,IAAI,IAAI,UAAA,KAAe,cAAc,QAAA,EAAU;YAC3C,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,QAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,SAAS,cAAc,IAAA,EAAM,KAAA,EAAO;YAChC,OAAO,UAAU;gBACb,MAAM;gBACN,MAAM,IAAI,IAAA;gBACV,WAAW;oBACP,IAAI,MAAA,CAAO,kBAAA;oBACX,IAAI,cAAA;oBACJ,YAAY;oBACZ;iBACJ,CAAE,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC;gBACnB,WAAW;oBACP,MAAM,aAAa,iBAAA;oBACnB,gBAAgB;gBACpB;YACJ,CAAC;QACL;QACA,SAAS,iBAAiB,OAAA,EAAS,KAAA,EAAO;YACtC,OAAO,UAAU;gBACb,MAAM;gBACN,MAAM,IAAI,IAAA;gBACV,WAAW;oBACP,IAAI,MAAA,CAAO,kBAAA;oBACX,IAAI,cAAA;oBACJ,YAAY;oBACZ;iBACJ,CAAE,MAAA,CAAO,CAAC,IAAM,CAAC,CAAC,CAAC;gBACnB,WAAW;oBACP,MAAM,aAAa,mBAAA;oBACnB,iBAAiB;gBACrB;YACJ,CAAC;QACL;QACA,MAAM,SAAS;YAAE,UAAU,IAAI,MAAA,CAAO,kBAAA;QAAmB;QACzD,MAAM,KAAK,IAAI,IAAA;QACf,IAAI,IAAA,CAAK,IAAA,CAAK,OAAA,YAAmB,YAAY;YAIzC,MAAM,KAAK,IAAA;YACX,OAAO,GAAG,eAAA,GAAmB,IAAA,EAAM;gBAC/B,MAAM,QAAQ,IAAI,SAAS,CAAC,CAAC;gBAC7B,MAAM,aAAa,MAAM,GAAG,IAAA,CAAK,IAAA,CAC5B,UAAA,CAAW,MAAM,MAAM,EACvB,KAAA,CAAM,CAAC,MAAM;oBACd,MAAM,QAAA,CAAS,cAAc,MAAM,CAAC,CAAC;oBACrC,MAAM;gBACV,CAAC;gBACD,MAAM,SAAS,MAAM,QAAQ,KAAA,CAAM,IAAI,IAAA,EAAM,UAAU;gBACvD,MAAM,gBAAgB,MAAM,GAAG,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAA,CAC5C,UAAA,CAAW,QAAQ,MAAM,EACzB,KAAA,CAAM,CAAC,MAAM;oBACd,MAAM,QAAA,CAAS,iBAAiB,QAAQ,CAAC,CAAC;oBAC1C,MAAM;gBACV,CAAC;gBACD,OAAO;YACX,CAAC;QACL,OACK;YAID,MAAM,KAAK,IAAA;YACX,OAAO,GAAG,SAAA,GAAa,IAAA,EAAM;gBACzB,MAAM,aAAa,GAAG,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAM,MAAM;gBACtD,IAAI,CAAC,WAAW,OAAA,EAAS;oBACrB,MAAM,IAAI,SAAS;wBAAC,cAAc,MAAM,WAAW,KAAK,CAAC;qBAAC;gBAC9D;gBACA,MAAM,SAAS,QAAQ,KAAA,CAAM,IAAI,IAAA,EAAM,WAAW,IAAI;gBACtD,MAAM,gBAAgB,GAAG,IAAA,CAAK,OAAA,CAAQ,SAAA,CAAU,QAAQ,MAAM;gBAC9D,IAAI,CAAC,cAAc,OAAA,EAAS;oBACxB,MAAM,IAAI,SAAS;wBAAC,iBAAiB,QAAQ,cAAc,KAAK,CAAC;qBAAC;gBACtE;gBACA,OAAO,cAAc,IAAA;YACzB,CAAC;QACL;IACJ;IACA,aAAa;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACrB;IACA,aAAa;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,OAAA;IACrB;IACA,KAAA,GAAQ,KAAA,EAAO;QACX,OAAO,IAAI,aAAY;YACnB,GAAG,IAAA,CAAK,IAAA;YACR,MAAM,SAAS,MAAA,CAAO,KAAK,EAAE,IAAA,CAAK,WAAW,MAAA,CAAO,CAAC;QACzD,CAAC;IACL;IACA,QAAQ,UAAA,EAAY;QAChB,OAAO,IAAI,aAAY;YACnB,GAAG,IAAA,CAAK,IAAA;YACR,SAAS;QACb,CAAC;IACL;IACA,UAAU,IAAA,EAAM;QACZ,MAAM,gBAAgB,IAAA,CAAK,KAAA,CAAM,IAAI;QACrC,OAAO;IACX;IACA,gBAAgB,IAAA,EAAM;QAClB,MAAM,gBAAgB,IAAA,CAAK,KAAA,CAAM,IAAI;QACrC,OAAO;IACX;IACA,OAAO,OAAO,IAAA,EAAM,OAAA,EAAS,MAAA,EAAQ;QACjC,OAAO,IAAI,aAAY;YACnB,MAAO,OACD,OACA,SAAS,MAAA,CAAO,CAAC,CAAC,EAAE,IAAA,CAAK,WAAW,MAAA,CAAO,CAAC;YAClD,SAAS,WAAW,WAAW,MAAA,CAAO;YACtC,UAAU,sBAAsB,WAAA;YAChC,GAAG,oBAAoB,MAAM,CAAA;QACjC,CAAC;IACL;AACJ;AACA,IAAM,UAAN,cAAsB,QAAQ;IAC1B,IAAI,SAAS;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO;IAC5B;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,MAAM,aAAa,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO;QACpC,OAAO,WAAW,MAAA,CAAO;YAAE,MAAM,IAAI,IAAA;YAAM,MAAM,IAAI,IAAA;YAAM,QAAQ;QAAI,CAAC;IAC5E;AACJ;AACA,QAAQ,MAAA,GAAS,CAAC,QAAQ,WAAW;IACjC,OAAO,IAAI,QAAQ;QACf;QACA,UAAU,sBAAsB,OAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,OAAO,KAAA,EAAO;QACV,IAAI,MAAM,IAAA,KAAS,IAAA,CAAK,IAAA,CAAK,KAAA,EAAO;YAChC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,UAAU,IAAI,IAAA;gBACd,MAAM,aAAa,eAAA;gBACnB,UAAU,IAAA,CAAK,IAAA,CAAK,KAAA;YACxB,CAAC;YACD,OAAO;QACX;QACA,OAAO;YAAE,QAAQ;YAAS,OAAO,MAAM,IAAA;QAAK;IAChD;IACA,IAAI,QAAQ;QACR,OAAO,IAAA,CAAK,IAAA,CAAK,KAAA;IACrB;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,OAAO,WAAW;IACnC,OAAO,IAAI,WAAW;QAClB;QACA,UAAU,sBAAsB,UAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,SAAS,cAAc,MAAA,EAAQ,MAAA,EAAQ;IACnC,OAAO,IAAI,QAAQ;QACf;QACA,UAAU,sBAAsB,OAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,UAAN,MAAM,iBAAgB,QAAQ;IAC1B,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,eAAe,GAAA,CAAI,IAAA,EAAM,KAAA,CAAM;IACnC;IACA,OAAO,KAAA,EAAO;QACV,IAAI,OAAO,MAAM,IAAA,KAAS,UAAU;YAChC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,MAAM,iBAAiB,IAAA,CAAK,IAAA,CAAK,MAAA;YACjC,kBAAkB,KAAK;gBACnB,UAAU,KAAK,UAAA,CAAW,cAAc;gBACxC,UAAU,IAAI,UAAA;gBACd,MAAM,aAAa,YAAA;YACvB,CAAC;YACD,OAAO;QACX;QACA,IAAI,CAAC,uBAAuB,IAAA,EAAM,gBAAgB,GAAG,GAAG;YACpD,uBAAuB,IAAA,EAAM,gBAAgB,IAAI,IAAI,IAAA,CAAK,IAAA,CAAK,MAAM,GAAG,GAAG;QAC/E;QACA,IAAI,CAAC,uBAAuB,IAAA,EAAM,gBAAgB,GAAG,EAAE,GAAA,CAAI,MAAM,IAAI,GAAG;YACpE,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,MAAM,iBAAiB,IAAA,CAAK,IAAA,CAAK,MAAA;YACjC,kBAAkB,KAAK;gBACnB,UAAU,IAAI,IAAA;gBACd,MAAM,aAAa,kBAAA;gBACnB,SAAS;YACb,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;IACA,IAAI,UAAU;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA;IACrB;IACA,IAAI,OAAO;QACP,MAAM,aAAa,CAAC;QACpB,KAAA,MAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAChC,UAAA,CAAW,GAAG,CAAA,GAAI;QACtB;QACA,OAAO;IACX;IACA,IAAI,SAAS;QACT,MAAM,aAAa,CAAC;QACpB,KAAA,MAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAChC,UAAA,CAAW,GAAG,CAAA,GAAI;QACtB;QACA,OAAO;IACX;IACA,IAAI,OAAO;QACP,MAAM,aAAa,CAAC;QACpB,KAAA,MAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAQ;YAChC,UAAA,CAAW,GAAG,CAAA,GAAI;QACtB;QACA,OAAO;IACX;IACA,QAAQ,MAAA,EAAQ,SAAS,IAAA,CAAK,IAAA,EAAM;QAChC,OAAO,SAAQ,MAAA,CAAO,QAAQ;YAC1B,GAAG,IAAA,CAAK,IAAA;YACR,GAAG,MAAA;QACP,CAAC;IACL;IACA,QAAQ,MAAA,EAAQ,SAAS,IAAA,CAAK,IAAA,EAAM;QAChC,OAAO,SAAQ,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,CAAC,MAAQ,CAAC,OAAO,QAAA,CAAS,GAAG,CAAC,GAAG;YACvE,GAAG,IAAA,CAAK,IAAA;YACR,GAAG,MAAA;QACP,CAAC;IACL;AACJ;AACA,iBAAiB,aAAA,GAAA,IAAI,QAAQ;AAC7B,QAAQ,MAAA,GAAS;AACjB,IAAM,gBAAN,cAA4B,QAAQ;IAChC,aAAc;QACV,KAAA,CAAM,GAAG,SAAS;QAClB,qBAAqB,GAAA,CAAI,IAAA,EAAM,KAAA,CAAM;IACzC;IACA,OAAO,KAAA,EAAO;QACV,MAAM,mBAAmB,KAAK,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,MAAM;QACjE,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;QACtC,IAAI,IAAI,UAAA,KAAe,cAAc,MAAA,IACjC,IAAI,UAAA,KAAe,cAAc,MAAA,EAAQ;YACzC,MAAM,iBAAiB,KAAK,YAAA,CAAa,gBAAgB;YACzD,kBAAkB,KAAK;gBACnB,UAAU,KAAK,UAAA,CAAW,cAAc;gBACxC,UAAU,IAAI,UAAA;gBACd,MAAM,aAAa,YAAA;YACvB,CAAC;YACD,OAAO;QACX;QACA,IAAI,CAAC,uBAAuB,IAAA,EAAM,sBAAsB,GAAG,GAAG;YAC1D,uBAAuB,IAAA,EAAM,sBAAsB,IAAI,IAAI,KAAK,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,MAAM,CAAC,GAAG,GAAG;QAC9G;QACA,IAAI,CAAC,uBAAuB,IAAA,EAAM,sBAAsB,GAAG,EAAE,GAAA,CAAI,MAAM,IAAI,GAAG;YAC1E,MAAM,iBAAiB,KAAK,YAAA,CAAa,gBAAgB;YACzD,kBAAkB,KAAK;gBACnB,UAAU,IAAI,IAAA;gBACd,MAAM,aAAa,kBAAA;gBACnB,SAAS;YACb,CAAC;YACD,OAAO;QACX;QACA,OAAO,GAAG,MAAM,IAAI;IACxB;IACA,IAAI,OAAO;QACP,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA;IACrB;AACJ;AACA,uBAAuB,aAAA,GAAA,IAAI,QAAQ;AACnC,cAAc,MAAA,GAAS,CAAC,QAAQ,WAAW;IACvC,OAAO,IAAI,cAAc;QACrB;QACA,UAAU,sBAAsB,aAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,SAAS;QACL,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACrB;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,IAAI,IAAI,UAAA,KAAe,cAAc,OAAA,IACjC,IAAI,MAAA,CAAO,KAAA,KAAU,OAAO;YAC5B,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,OAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,MAAM,cAAc,IAAI,UAAA,KAAe,cAAc,OAAA,GAC/C,IAAI,IAAA,GACJ,QAAQ,OAAA,CAAQ,IAAI,IAAI;QAC9B,OAAO,GAAG,YAAY,IAAA,CAAK,CAAC,SAAS;YACjC,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW,MAAM;gBACnC,MAAM,IAAI,IAAA;gBACV,UAAU,IAAI,MAAA,CAAO,kBAAA;YACzB,CAAC;QACL,CAAC,CAAC;IACN;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,QAAQ,WAAW;IACpC,OAAO,IAAI,WAAW;QAClB,MAAM;QACN,UAAU,sBAAsB,UAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,YAAY;QACR,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA;IACrB;IACA,aAAa;QACT,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,QAAA,KAAa,sBAAsB,UAAA,GAC1D,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAC5B,IAAA,CAAK,IAAA,CAAK,MAAA;IACpB;IACA,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,MAAM,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,IAAU;QACnC,MAAM,WAAW;YACb,UAAU,CAAC,QAAQ;gBACf,kBAAkB,KAAK,GAAG;gBAC1B,IAAI,IAAI,KAAA,EAAO;oBACX,OAAO,KAAA,CAAM;gBACjB,OACK;oBACD,OAAO,KAAA,CAAM;gBACjB;YACJ;YACA,IAAI,QAAO;gBACP,OAAO,IAAI,IAAA;YACf;QACJ;QACA,SAAS,QAAA,GAAW,SAAS,QAAA,CAAS,IAAA,CAAK,QAAQ;QACnD,IAAI,OAAO,IAAA,KAAS,cAAc;YAC9B,MAAM,YAAY,OAAO,SAAA,CAAU,IAAI,IAAA,EAAM,QAAQ;YACrD,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;gBAClB,OAAO,QAAQ,OAAA,CAAQ,SAAS,EAAE,IAAA,CAAK,OAAOC,eAAc;oBACxD,IAAI,OAAO,KAAA,KAAU,WACjB,OAAO;oBACX,MAAM,SAAS,MAAM,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;wBAC9C,MAAMA;wBACN,MAAM,IAAI,IAAA;wBACV,QAAQ;oBACZ,CAAC;oBACD,IAAI,OAAO,MAAA,KAAW,WAClB,OAAO;oBACX,IAAI,OAAO,MAAA,KAAW,SAClB,OAAO,MAAM,OAAO,KAAK;oBAC7B,IAAI,OAAO,KAAA,KAAU,SACjB,OAAO,MAAM,OAAO,KAAK;oBAC7B,OAAO;gBACX,CAAC;YACL,OACK;gBACD,IAAI,OAAO,KAAA,KAAU,WACjB,OAAO;gBACX,MAAM,SAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW;oBACvC,MAAM;oBACN,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAI,OAAO,MAAA,KAAW,WAClB,OAAO;gBACX,IAAI,OAAO,MAAA,KAAW,SAClB,OAAO,MAAM,OAAO,KAAK;gBAC7B,IAAI,OAAO,KAAA,KAAU,SACjB,OAAO,MAAM,OAAO,KAAK;gBAC7B,OAAO;YACX;QACJ;QACA,IAAI,OAAO,IAAA,KAAS,cAAc;YAC9B,MAAM,oBAAoB,CAAC,QAAQ;gBAC/B,MAAM,SAAS,OAAO,UAAA,CAAW,KAAK,QAAQ;gBAC9C,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;oBAClB,OAAO,QAAQ,OAAA,CAAQ,MAAM;gBACjC;gBACA,IAAI,kBAAkB,SAAS;oBAC3B,MAAM,IAAI,MAAM,2FAA2F;gBAC/G;gBACA,OAAO;YACX;YACA,IAAI,IAAI,MAAA,CAAO,KAAA,KAAU,OAAO;gBAC5B,MAAM,QAAQ,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW;oBACtC,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAI,MAAM,MAAA,KAAW,WACjB,OAAO;gBACX,IAAI,MAAM,MAAA,KAAW,SACjB,OAAO,KAAA,CAAM;gBAEjB,kBAAkB,MAAM,KAAK;gBAC7B,OAAO;oBAAE,QAAQ,OAAO,KAAA;oBAAO,OAAO,MAAM,KAAA;gBAAM;YACtD,OACK;gBACD,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CACZ,WAAA,CAAY;oBAAE,MAAM,IAAI,IAAA;oBAAM,MAAM,IAAI,IAAA;oBAAM,QAAQ;gBAAI,CAAC,EAC3D,IAAA,CAAK,CAAC,UAAU;oBACjB,IAAI,MAAM,MAAA,KAAW,WACjB,OAAO;oBACX,IAAI,MAAM,MAAA,KAAW,SACjB,OAAO,KAAA,CAAM;oBACjB,OAAO,kBAAkB,MAAM,KAAK,EAAE,IAAA,CAAK,MAAM;wBAC7C,OAAO;4BAAE,QAAQ,OAAO,KAAA;4BAAO,OAAO,MAAM,KAAA;wBAAM;oBACtD,CAAC;gBACL,CAAC;YACL;QACJ;QACA,IAAI,OAAO,IAAA,KAAS,aAAa;YAC7B,IAAI,IAAI,MAAA,CAAO,KAAA,KAAU,OAAO;gBAC5B,MAAM,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW;oBACrC,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAI,CAAC,QAAQ,IAAI,GACb,OAAO;gBACX,MAAM,SAAS,OAAO,SAAA,CAAU,KAAK,KAAA,EAAO,QAAQ;gBACpD,IAAI,kBAAkB,SAAS;oBAC3B,MAAM,IAAI,MAAM,CAAA,+FAAA,CAAiG;gBACrH;gBACA,OAAO;oBAAE,QAAQ,OAAO,KAAA;oBAAO,OAAO;gBAAO;YACjD,OACK;gBACD,OAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CACZ,WAAA,CAAY;oBAAE,MAAM,IAAI,IAAA;oBAAM,MAAM,IAAI,IAAA;oBAAM,QAAQ;gBAAI,CAAC,EAC3D,IAAA,CAAK,CAAC,SAAS;oBAChB,IAAI,CAAC,QAAQ,IAAI,GACb,OAAO;oBACX,OAAO,QAAQ,OAAA,CAAQ,OAAO,SAAA,CAAU,KAAK,KAAA,EAAO,QAAQ,CAAC,EAAE,IAAA,CAAK,CAAC,SAAA,CAAY;4BAAE,QAAQ,OAAO,KAAA;4BAAO,OAAO;wBAAO,CAAA,CAAE;gBAC7H,CAAC;YACL;QACJ;QACA,KAAK,WAAA,CAAY,MAAM;IAC3B;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,QAAQ,QAAQ,WAAW;IAC5C,OAAO,IAAI,WAAW;QAClB;QACA,UAAU,sBAAsB,UAAA;QAChC;QACA,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,WAAW,oBAAA,GAAuB,CAAC,YAAY,QAAQ,WAAW;IAC9D,OAAO,IAAI,WAAW;QAClB;QACA,QAAQ;YAAE,MAAM;YAAc,WAAW;QAAW;QACpD,UAAU,sBAAsB,UAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,cAAN,cAA0B,QAAQ;IAC9B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,SAAA,EAAW;YACxC,OAAO,GAAG,KAAA,CAAS;QACvB;QACA,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,KAAK;IAC3C;IACA,SAAS;QACL,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;AACJ;AACA,YAAY,MAAA,GAAS,CAAC,MAAM,WAAW;IACnC,OAAO,IAAI,YAAY;QACnB,WAAW;QACX,UAAU,sBAAsB,WAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,cAAN,cAA0B,QAAQ;IAC9B,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,IAAA,EAAM;YACnC,OAAO,GAAG,IAAI;QAClB;QACA,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,KAAK;IAC3C;IACA,SAAS;QACL,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;AACJ;AACA,YAAY,MAAA,GAAS,CAAC,MAAM,WAAW;IACnC,OAAO,IAAI,YAAY;QACnB,WAAW;QACX,UAAU,sBAAsB,WAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,aAAN,cAAyB,QAAQ;IAC7B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,IAAI,OAAO,IAAI,IAAA;QACf,IAAI,IAAI,UAAA,KAAe,cAAc,SAAA,EAAW;YAC5C,OAAO,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa;QAClC;QACA,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO;YAC9B;YACA,MAAM,IAAI,IAAA;YACV,QAAQ;QACZ,CAAC;IACL;IACA,gBAAgB;QACZ,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;AACJ;AACA,WAAW,MAAA,GAAS,CAAC,MAAM,WAAW;IAClC,OAAO,IAAI,WAAW;QAClB,WAAW;QACX,UAAU,sBAAsB,UAAA;QAChC,cAAc,OAAO,OAAO,OAAA,KAAY,aAClC,OAAO,OAAA,GACP,IAAM,OAAO,OAAA;QACnB,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,WAAN,cAAuB,QAAQ;IAC3B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAE9C,MAAM,SAAS;YACX,GAAG,GAAA;YACH,QAAQ;gBACJ,GAAG,IAAI,MAAA;gBACP,QAAQ,CAAC,CAAA;YACb;QACJ;QACA,MAAM,SAAS,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO;YACtC,MAAM,OAAO,IAAA;YACb,MAAM,OAAO,IAAA;YACb,QAAQ;gBACJ,GAAG,MAAA;YACP;QACJ,CAAC;QACD,IAAI,QAAQ,MAAM,GAAG;YACjB,OAAO,OAAO,IAAA,CAAK,CAACH,YAAW;gBAC3B,OAAO;oBACH,QAAQ;oBACR,OAAOA,QAAO,MAAA,KAAW,UACnBA,QAAO,KAAA,GACP,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW;wBACnB,IAAI,SAAQ;4BACR,OAAO,IAAI,SAAS,OAAO,MAAA,CAAO,MAAM;wBAC5C;wBACA,OAAO,OAAO,IAAA;oBAClB,CAAC;gBACT;YACJ,CAAC;QACL,OACK;YACD,OAAO;gBACH,QAAQ;gBACR,OAAO,OAAO,MAAA,KAAW,UACnB,OAAO,KAAA,GACP,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW;oBACnB,IAAI,SAAQ;wBACR,OAAO,IAAI,SAAS,OAAO,MAAA,CAAO,MAAM;oBAC5C;oBACA,OAAO,OAAO,IAAA;gBAClB,CAAC;YACT;QACJ;IACJ;IACA,cAAc;QACV,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;AACJ;AACA,SAAS,MAAA,GAAS,CAAC,MAAM,WAAW;IAChC,OAAO,IAAI,SAAS;QAChB,WAAW;QACX,UAAU,sBAAsB,QAAA;QAChC,YAAY,OAAO,OAAO,KAAA,KAAU,aAAa,OAAO,KAAA,GAAQ,IAAM,OAAO,KAAA;QAC7E,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,SAAN,cAAqB,QAAQ;IACzB,OAAO,KAAA,EAAO;QACV,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,KAAK;QACtC,IAAI,eAAe,cAAc,GAAA,EAAK;YAClC,MAAM,MAAM,IAAA,CAAK,eAAA,CAAgB,KAAK;YACtC,kBAAkB,KAAK;gBACnB,MAAM,aAAa,YAAA;gBACnB,UAAU,cAAc,GAAA;gBACxB,UAAU,IAAI,UAAA;YAClB,CAAC;YACD,OAAO;QACX;QACA,OAAO;YAAE,QAAQ;YAAS,OAAO,MAAM,IAAA;QAAK;IAChD;AACJ;AACA,OAAO,MAAA,GAAS,CAAC,WAAW;IACxB,OAAO,IAAI,OAAO;QACd,UAAU,sBAAsB,MAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AACA,IAAM,QAAQ,OAAO,WAAW;AAChC,IAAM,aAAN,cAAyB,QAAQ;IAC7B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QAC9C,MAAM,OAAO,IAAI,IAAA;QACjB,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO;YACzB;YACA,MAAM,IAAI,IAAA;YACV,QAAQ;QACZ,CAAC;IACL;IACA,SAAS;QACL,OAAO,IAAA,CAAK,IAAA,CAAK,IAAA;IACrB;AACJ;AACA,IAAM,cAAN,MAAM,qBAAoB,QAAQ;IAC9B,OAAO,KAAA,EAAO;QACV,MAAM,EAAE,MAAA,EAAQ,GAAA,CAAI,CAAA,GAAI,IAAA,CAAK,mBAAA,CAAoB,KAAK;QACtD,IAAI,IAAI,MAAA,CAAO,KAAA,EAAO;YAClB,MAAM,cAAc,YAAY;gBAC5B,MAAM,WAAW,MAAM,IAAA,CAAK,IAAA,CAAK,EAAA,CAAG,WAAA,CAAY;oBAC5C,MAAM,IAAI,IAAA;oBACV,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;gBACD,IAAI,SAAS,MAAA,KAAW,WACpB,OAAO;gBACX,IAAI,SAAS,MAAA,KAAW,SAAS;oBAC7B,OAAO,KAAA,CAAM;oBACb,OAAO,MAAM,SAAS,KAAK;gBAC/B,OACK;oBACD,OAAO,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,WAAA,CAAY;wBAC7B,MAAM,SAAS,KAAA;wBACf,MAAM,IAAI,IAAA;wBACV,QAAQ;oBACZ,CAAC;gBACL;YACJ;YACA,OAAO,YAAY;QACvB,OACK;YACD,MAAM,WAAW,IAAA,CAAK,IAAA,CAAK,EAAA,CAAG,UAAA,CAAW;gBACrC,MAAM,IAAI,IAAA;gBACV,MAAM,IAAI,IAAA;gBACV,QAAQ;YACZ,CAAC;YACD,IAAI,SAAS,MAAA,KAAW,WACpB,OAAO;YACX,IAAI,SAAS,MAAA,KAAW,SAAS;gBAC7B,OAAO,KAAA,CAAM;gBACb,OAAO;oBACH,QAAQ;oBACR,OAAO,SAAS,KAAA;gBACpB;YACJ,OACK;gBACD,OAAO,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,UAAA,CAAW;oBAC5B,MAAM,SAAS,KAAA;oBACf,MAAM,IAAI,IAAA;oBACV,QAAQ;gBACZ,CAAC;YACL;QACJ;IACJ;IACA,OAAO,OAAO,CAAA,EAAG,CAAA,EAAG;QAChB,OAAO,IAAI,aAAY;YACnB,IAAI;YACJ,KAAK;YACL,UAAU,sBAAsB,WAAA;QACpC,CAAC;IACL;AACJ;AACA,IAAM,cAAN,cAA0B,QAAQ;IAC9B,OAAO,KAAA,EAAO;QACV,MAAM,SAAS,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,KAAK;QAC/C,MAAM,SAAS,CAAC,SAAS;YACrB,IAAI,QAAQ,IAAI,GAAG;gBACf,KAAK,KAAA,GAAQ,OAAO,MAAA,CAAO,KAAK,KAAK;YACzC;YACA,OAAO;QACX;QACA,OAAO,QAAQ,MAAM,IACf,OAAO,IAAA,CAAK,CAAC,OAAS,OAAO,IAAI,CAAC,IAClC,OAAO,MAAM;IACvB;IACA,SAAS;QACL,OAAO,IAAA,CAAK,IAAA,CAAK,SAAA;IACrB;AACJ;AACA,YAAY,MAAA,GAAS,CAAC,MAAM,WAAW;IACnC,OAAO,IAAI,YAAY;QACnB,WAAW;QACX,UAAU,sBAAsB,WAAA;QAChC,GAAG,oBAAoB,MAAM,CAAA;IACjC,CAAC;AACL;AA6BA,IAAM,OAAO;IACT,QAAQ,UAAU,UAAA;AACtB;AACA,IAAI;AAAA,CACH,SAAUI,sBAAAA,EAAuB;IAC9BA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,QAAQ,CAAA,GAAI;IAClCA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,SAAS,CAAA,GAAI;IACnCA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,cAAc,CAAA,GAAI;IACxCA,sBAAAA,CAAsB,SAAS,CAAA,GAAI;IACnCA,sBAAAA,CAAsB,QAAQ,CAAA,GAAI;IAClCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,UAAU,CAAA,GAAI;IACpCA,sBAAAA,CAAsB,SAAS,CAAA,GAAI;IACnCA,sBAAAA,CAAsB,UAAU,CAAA,GAAI;IACpCA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,UAAU,CAAA,GAAI;IACpCA,sBAAAA,CAAsB,uBAAuB,CAAA,GAAI;IACjDA,sBAAAA,CAAsB,iBAAiB,CAAA,GAAI;IAC3CA,sBAAAA,CAAsB,UAAU,CAAA,GAAI;IACpCA,sBAAAA,CAAsB,WAAW,CAAA,GAAI;IACrCA,sBAAAA,CAAsB,QAAQ,CAAA,GAAI;IAClCA,sBAAAA,CAAsB,QAAQ,CAAA,GAAI;IAClCA,sBAAAA,CAAsB,aAAa,CAAA,GAAI;IACvCA,sBAAAA,CAAsB,SAAS,CAAA,GAAI;IACnCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,SAAS,CAAA,GAAI;IACnCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,eAAe,CAAA,GAAI;IACzCA,sBAAAA,CAAsB,aAAa,CAAA,GAAI;IACvCA,sBAAAA,CAAsB,aAAa,CAAA,GAAI;IACvCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,UAAU,CAAA,GAAI;IACpCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,YAAY,CAAA,GAAI;IACtCA,sBAAAA,CAAsB,aAAa,CAAA,GAAI;IACvCA,sBAAAA,CAAsB,aAAa,CAAA,GAAI;AAC3C,CAAA,EAAG,yBAAA,CAA0B,wBAAwB,CAAC,CAAA,CAAE;AAMxD,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,UAAU,OAAO,MAAA;AACvB,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,cAAc,WAAW,MAAA;AAC/B,IAAM,WAAW,QAAQ,MAAA;AACzB,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,gBAAgB,aAAa,MAAA;AACnC,IAAM,WAAW,QAAQ,MAAA;AACzB,IAAM,UAAU,OAAO,MAAA;AACvB,IAAM,cAAc,WAAW,MAAA;AAC/B,IAAM,YAAY,SAAS,MAAA;AAC3B,IAAM,WAAW,QAAQ,MAAA;AACzB,IAAM,YAAY,SAAS,MAAA;AAC3B,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,mBAAmB,UAAU,YAAA;AACnC,IAAM,YAAY,SAAS,MAAA;AAC3B,IAAM,yBAAyB,sBAAsB,MAAA;AACrD,IAAM,mBAAmB,gBAAgB,MAAA;AACzC,IAAM,YAAY,SAAS,MAAA;AAC3B,IAAM,aAAa,UAAU,MAAA;AAC7B,IAAM,UAAU,OAAO,MAAA;AACvB,IAAM,UAAU,OAAO,MAAA;AACvB,IAAM,eAAe,YAAY,MAAA;AACjC,IAAM,WAAW,QAAQ,MAAA;AACzB,IAAM,cAAc,WAAW,MAAA;AAC/B,IAAM,WAAW,QAAQ,MAAA;AACzB,IAAM,iBAAiB,cAAc,MAAA;AACrC,IAAM,cAAc,WAAW,MAAA;AAC/B,IAAM,cAAc,WAAW,MAAA;AAC/B,IAAM,eAAe,YAAY,MAAA;AACjC,IAAM,eAAe,YAAY,MAAA;AACjC,IAAM,iBAAiB,WAAW,oBAAA;AAClC,IAAM,eAAe,YAAY,MAAA;;AC5kIjC,IAAM,QAA8B,CAAC;AAErC,SAAS,mBAAmB,OAAA,EAAoB;IAC/C,OAAQ,QAAQ,WAAA,CAAY,IAAA,EAAM;QACjC,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR,KAAK;YACJ,OAAO;QACR;YACC,OAAO;IACT;AACD;AAEA,SAAS,cAAc,OAAA,EAA0B;IAChD,MAAM,aAAiC,CAAC,CAAA;IACxC,IAAI,QAAQ,QAAA,EAAU,SAAS,YAAY;QAC1C,WAAW,IAAA,CAAK,GAAG,QAAQ,QAAA,CAAS,OAAA,CAAQ,UAAU;QACtD,OAAO;IACR;IACA,IAAI,QAAQ,KAAA,YAAiB,WAAW;QACvC,OAAO,OAAA,CAAQ,QAAQ,KAAA,CAAM,KAAK,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;YAC7D,IAAI,iBAAiB,SAAW;gBAC/B,WAAW,IAAA,CAAK;oBACf,MAAM;oBACN,IAAI;oBACJ,QAAQ;wBACP,MAAM,mBAAmB,KAAK;wBAC9B,GAAI,eAAe,SAAS,MAAM,SAAA,GAC/B;4BACA,WAAW,MAAM,SAAA;wBAClB,IACC,CAAC,CAAA;wBACJ,aAAa,MAAM,WAAA;oBACpB;gBACD,CAAC;YACF;QACD,CAAC;IACF;IACA,OAAO;AACR;AAEA,SAAS,eAAe,OAAA,EAA+B;IACtD,IAAI,QAAQ,QAAA,EAAU,SAAS,aAAa;QAC3C,OAAO,QAAQ,QAAA,CAAS,OAAA,CAAQ,WAAA;IACjC;IACA,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,OAAO,KAAA;IAC1B,IAAI,QAAQ,IAAA,YAAgB,aAAa,QAAQ,IAAA,YAAgB,aAAa;QAE7E,MAAM,QAAQ,QAAQ,IAAA,CAAK,KAAA;QAC3B,IAAI,CAAC,MAAO,CAAA,OAAO,KAAA;QACnB,MAAM,aAAkC,CAAC;QACzC,MAAM,WAAqB,CAAC,CAAA;QAC5B,OAAO,OAAA,CAAQ,KAAK,EAAE,OAAA,CAAQ,CAAC,CAAC,KAAK,KAAK,CAAA,KAAM;YAC/C,IAAI,iBAAiB,SAAW;gBAC/B,UAAA,CAAW,GAAG,CAAA,GAAI;oBACjB,MAAM,mBAAmB,KAAK;oBAC9B,aAAa,MAAM,WAAA;gBACpB;gBACA,IAAI,CAAA,CAAE,iBAAiB,WAAA,GAAc;oBACpC,SAAS,IAAA,CAAK,GAAG;gBAClB;YACD;QACD,CAAC;QACD,OAAO;YACN,UAAU,QAAQ,IAAA,YAAgB,cAAc,QAAQ,QAAQ,IAAA,GAAO,OAAO;YAC9E,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN;wBACA;oBACD;gBACD;YACD;QACD;IACD;IACA,OAAO,KAAA;AACR;AAEA,SAAS,YAAY,SAAA,EAAiC;IACrD,OAAO;QACN,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;wBACA,UAAU;4BAAC,SAAS;yBAAA;oBACrB;gBACD;YACD;YACA,aAAa;QACd;QACA,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;wBACA,UAAU;4BAAC,SAAS;yBAAA;oBACrB;gBACD;YACD;YACA,aAAa;QACd;QACA,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;oBACD;gBACD;YACD;YACA,aACC;QACF;QACA,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;oBACD;gBACD;YACD;YACA,aAAa;QACd;QACA,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;oBACD;gBACD;YACD;YACA,aAAa;QACd;QACA,OAAO;YACN,SAAS;gBACR,oBAAoB;oBACnB,QAAQ;wBACP,MAAM;wBACN,YAAY;4BACX,SAAS;gCACR,MAAM;4BACP;wBACD;oBACD;gBACD;YACD;YACA,aACC;QACF;QACA,GAAG,SAAA;IACJ;AACD;AAEA,eAAsB,UACrB,SAAA,EACA,MAAA,EAGC;IACD,MAAM,aAAa;QAClB,SAAS,CAAC;IACX;IAEA,OAAO,OAAA,CAAQ,SAAS,EAAE,OAAA,CAAQ,CAAC,CAAC,GAAG,KAAK,CAAA,KAAM;QACjD,MAAM,UAAU,MAAM,OAAA;QACtB,IAAI,QAAQ,QAAA,EAAU,YAAa,CAAA;QACnC,IAAI,QAAQ,MAAA,KAAW,OAAO;YAC7B,KAAA,CAAM,MAAM,IAAI,CAAA,GAAI;gBACnB,KAAK;oBACJ,MAAM;wBAAC,WAAW;2BAAI,QAAQ,QAAA,EAAU,SAAS,QAAQ,CAAC,CAAE;qBAAA;oBAC5D,aAAa,QAAQ,QAAA,EAAU,SAAS;oBACxC,aAAa,QAAQ,QAAA,EAAU,SAAS;oBACxC,UAAU;wBACT;4BACC,YAAY,CAAC,CAAA;wBACd;qBACD;oBACA,YAAY,cAAc,OAAO;oBACjC,WAAW,YAAY,QAAQ,QAAA,EAAU,SAAS,SAAS;gBAC5D;YACD;QACD;QAEA,IAAI,QAAQ,MAAA,KAAW,QAAQ;YAC9B,MAAM,OAAO,eAAe,OAAO;YACnC,KAAA,CAAM,MAAM,IAAI,CAAA,GAAI;gBACnB,MAAM;oBACL,MAAM;wBAAC,WAAW;2BAAI,QAAQ,QAAA,EAAU,SAAS,QAAQ,CAAC,CAAE;qBAAA;oBAC5D,aAAa,QAAQ,QAAA,EAAU,SAAS;oBACxC,aAAa,QAAQ,QAAA,EAAU,SAAS;oBACxC,UAAU;wBACT;4BACC,YAAY,CAAC,CAAA;wBACd;qBACD;oBACA,YAAY,cAAc,OAAO;oBACjC,GAAI,OACD;wBAAE,aAAa;oBAAK,IACpB;wBACA,aAAa;4BAAA,eAAA;4BAEZ,SAAS;gCACR,oBAAoB;oCACnB,QAAQ;wCACP,MAAM;wCACN,YAAY,CAAC;oCACd;gCACD;4BACD;wBACD;oBACD,CAAA;oBACF,WAAW,YAAY,QAAQ,QAAA,EAAU,SAAS,SAAS;gBAC5D;YACD;QACD;IACD,CAAC;IAED,MAAM,MAAM;QACX,SAAS;QACT,MAAM;YACL,OAAO;YACP,aAAa;YACb,SAAS;QACV;QACA;QACA,UAAU;YACT;gBACC,cAAc,CAAC,CAAA;YAChB;SACD;QACA,SAAS;YACR;gBACC,KAAK,QAAQ;YACd;SACD;QACA,MAAM;YACL;gBACC,MAAM;gBACN,aACC;YACF;SACD;QACA;IACD;IACA,OAAO;AACR;AAEO,IAAM,UAAU,CACtB,cACA,SAMI,CAAA;;;;;;;;;;;;;IAAA,EAaC,KAAK,SAAA,CAAU,YAAY,CAAC,CAAA;;;;aAAA,EAInB,QAAQ,OAAO,CAAA,wBAAA,EAA2B,mBAAmB,OAAO,IAAI,CAAC,EAAA,GAAK,KAAA,CAAS,CAAA;YAAA,EACxF,QAAQ,SAAS,QAAQ,CAAA;;UAAA,EAE3B,QAAQ,SAAS,oBAAoB,CAAA;gBAAA,EAC/B,QAAQ,eAAe,sBAAsB,CAAA;;;;;;;;OAAA,CAAA;;AF5TxD,IAAM,eAAe,CAC3B,WACA,WACI;IACJ,IAAI,CAAC,QAAQ,SAAS,UAAU;QAC/B,MAAM,UAAU;YACf,MAAM;YACN,GAAG,QAAQ,OAAA;QACZ;QAEA,SAAA,CAAU,SAAS,CAAA,GAAIC,gBACtB,QAAQ,IAAA,EACR;YACC,QAAQ;QACT,GACA,OAAO,MAAM;YACZ,MAAM,SAAS,MAAM,UAAU,SAAS;YACxC,OAAO,IAAI,SAAS,QAAQ,QAAQ,QAAQ,MAAM,GAAG;gBACpD,SAAS;oBACR,gBAAgB;gBACjB;YACD,CAAC;QACF;IAEF;IACA,MAAM,SAAS,6MAAA,CAAiB;IAChC,MAAM,iNAAmB,eAAA,CAAiB;IAE1C,KAAA,MAAW,YAAY,OAAO,MAAA,CAAO,SAAS,EAAG;QAChD,IAAI,CAAC,SAAS,OAAA,EAAS;YACtB;QACD;QACA,IAAI,SAAS,OAAA,EAAS,UAAU,YAAa,CAAA;QAE7C,MAAM,UAAU,MAAM,OAAA,CAAQ,SAAS,OAAA,EAAS,MAAM,IACnD,SAAS,OAAA,CAAQ,MAAA,GACjB;YAAC,SAAS,OAAA,EAAS,MAAM;SAAA;QAE5B,KAAA,MAAW,UAAU,QAAS;YAC7B,CAAA,GAAA,yLAAA,CAAA,WAAA,EAAS,QAAQ,QAAQ,SAAS,IAAA,EAAM,QAAQ;QACjD;IACD;IAEA,IAAI,QAAQ,kBAAkB,QAAQ;QACrC,KAAA,MAAW,EAAE,IAAA,EAAM,UAAA,CAAW,CAAA,IAAK,OAAO,gBAAA,CAAkB;YAC3D,CAAA,GAAA,yLAAA,CAAA,WAAA,EAAS,kBAAkB,KAAK,MAAM,UAAU;QACjD;IACD;IAEA,MAAM,iBAAiB,OAAO,YAAqB;QAClD,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;QAC/B,MAAM,OAAO,QAAQ,WAClB,IAAI,QAAA,CACH,KAAA,CAAM,OAAO,QAAQ,EACrB,MAAA,CAAO,CAAC,KAAK,MAAM,UAAU;YAC7B,IAAI,UAAU,GAAG;gBAChB,IAAI,QAAQ,GAAG;oBACd,IAAI,IAAA,CAAK,GAAG,OAAO,QAAQ,GAAG,IAAI,EAAE;gBACrC,OAAO;oBACN,IAAI,IAAA,CAAK,IAAI;gBACd;YACD;YACA,OAAO;QACR,GAAG,CAAC,CAAa,EAChB,IAAA,CAAK,EAAE,IACR,IAAI,QAAA;QAEP,IAAI,CAAC,MAAM,QAAQ;YAClB,OAAO,IAAI,SAAS,MAAM;gBAAE,QAAQ;gBAAK,YAAY;YAAY,CAAC;QACnE;QAEA,MAAM,sMAAQ,YAAA,EAAU,QAAQ,QAAQ,MAAA,EAAQ,IAAI;QACpD,IAAI,CAAC,OAAO,MAAM;YACjB,OAAO,IAAI,SAAS,MAAM;gBAAE,QAAQ;gBAAK,YAAY;YAAY,CAAC;QACnE;QAEA,MAAM,QAA2C,CAAC;QAClD,IAAI,YAAA,CAAa,OAAA,CAAQ,CAAC,OAAO,QAAQ;YACxC,IAAI,OAAO,OAAO;gBACjB,IAAI,MAAM,OAAA,CAAQ,KAAA,CAAM,GAAG,CAAC,GAAG;oBAC7B,KAAA,CAAM,GAAG,CAAA,CAAe,IAAA,CAAK,KAAK;gBACpC,OAAO;oBACN,KAAA,CAAM,GAAG,CAAA,GAAI;wBAAC,KAAA,CAAM,GAAG,CAAA;wBAAa,KAAK;qBAAA;gBAC1C;YACD,OAAO;gBACN,KAAA,CAAM,GAAG,CAAA,GAAI;YACd;QACD,CAAC;QAED,MAAM,UAAU,MAAM,IAAA;QACtB,MAAM,UAAU;YACf;YACA,QAAQ,QAAQ,MAAA;YAChB,SAAS,QAAQ,OAAA;YACjB,QAAQ,MAAM,MAAA,GAAU,KAAK,KAAA,CAAM,KAAK,SAAA,CAAU,MAAM,MAAM,CAAC,IAAY,CAAC;YAC5E;YACA,MAAM,QAAQ,OAAA,CAAQ,WAAA,GACnB,KAAA,IACA,MAAM,QAAQ,QAAQ,OAAA,CAAQ,YAAA,GAAe,QAAQ,KAAA,CAAM,IAAI,OAAO;YACzE;YACA,OAAO;YACP,YAAY;YACZ,SAAS,QAAQ;QAClB;QAEA,IAAI;YACH,MAAM,iNAAmB,gBAAA,EAAc,kBAAkB,KAAK,IAAI;YAClE,IAAI,kBAAkB,QAAQ;gBAC7B,KAAA,MAAW,EAAE,MAAM,UAAA,EAAY,MAAA,CAAO,CAAA,IAAK,iBAAkB;oBAC5D,MAAM,MAAM,MAAO,WAAwB;wBAC1C,GAAG,OAAA;wBACH;wBACA,YAAY;oBACb,CAAC;oBAED,IAAI,eAAe,SAAU,CAAA,OAAO;gBACrC;YACD;YAEA,MAAM,WAAY,MAAM,QAAQ,OAAO;YACvC,OAAO;QACR,EAAA,OAAS,OAAO;YACf,IAAI,WAAW,KAAK,GAAG;gBACtB,OAAO,WAAW,KAAK;YACxB;YACA,QAAQ,KAAA,CAAM,CAAA,gBAAA,CAAA,EAAoB,KAAK;YACvC,OAAO,IAAI,SAAS,MAAM;gBACzB,QAAQ;gBACR,YAAY;YACb,CAAC;QACF;IACD;IAEA,OAAO;QACN,SAAS,OAAO,YAAqB;YACpC,MAAM,QAAQ,MAAM,QAAQ,YAAY,OAAO;YAC/C,IAAI,iBAAiB,UAAU;gBAC9B,OAAO;YACR;YACA,MAAM,MAAM,iBAAiB,UAAU,QAAQ;YAC/C,MAAM,MAAM,MAAM,eAAe,GAAG;YACpC,MAAM,QAAQ,MAAM,QAAQ,aAAa,GAAG;YAC5C,IAAI,iBAAiB,UAAU;gBAC9B,OAAO;YACR;YACA,OAAO;QACR;QACA;IACD;AACD", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "debugId": null}}]}