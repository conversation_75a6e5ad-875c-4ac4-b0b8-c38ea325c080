module.exports = {

"[project]/node_modules/.pnpm/better-auth@1.2.12/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/82e04_better-auth_dist_chunks_bun-sqlite-dialect_mjs_428628bf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/better-auth@1.2.12/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/headers.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__6d43daf2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/headers.js [app-route] (ecmascript)");
    });
});
}}),

};