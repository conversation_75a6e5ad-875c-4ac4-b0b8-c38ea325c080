{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/server/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\n\nexport function middleware() {\n  const res = NextResponse.next()\n\n  res.headers.append('Access-Control-Allow-Credentials', \"true\")\n  res.headers.append('Access-Control-Allow-Origin', process.env.CORS_ORIGIN || \"\")\n  res.headers.append('Access-Control-Allow-Methods', 'GET,POST,OPTIONS')\n  res.headers.append(\n    'Access-Control-Allow-Headers',\n    'Content-Type, Authorization'\n  )\n\n  return res\n}\n\nexport const config = {\n  matcher: '/:path*',\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS;IACd,MAAM,MAAM,4TAAA,CAAA,eAAY,CAAC,IAAI;IAE7B,IAAI,OAAO,CAAC,MAAM,CAAC,oCAAoC;IACvD,IAAI,OAAO,CAAC,MAAM,CAAC,+BAA+B,QAAQ,GAAG,CAAC,WAAW,IAAI;IAC7E,IAAI,OAAO,CAAC,MAAM,CAAC,gCAAgC;IACnD,IAAI,OAAO,CAAC,MAAM,CAChB,gCACA;IAGF,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;AACX"}}]}