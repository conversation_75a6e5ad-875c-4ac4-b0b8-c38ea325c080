{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_ce275110._.js", "server/edge/chunks/[root-of-the-server]__2cba886a._.js", "server/edge/chunks/apps_server_edge-wrapper_b54f1cb6.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "d/en2HyMGnAYfs+EOw2b9fpBDem9Js5Pf0OSmzmOpBA=", "__NEXT_PREVIEW_MODE_ID": "848cd234d318796547c9ef49ce8e5de6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c944ec1a5f16a53dfecf9eea1d5e3ef92f1a38d217539cd7140f28a21d5a85a2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc9daedb73c933fb52b2a6b1dc1b0a307a95814c9e0a0c5867420a5cddb2a411"}}}, "sortedMiddleware": ["/"], "functions": {}}