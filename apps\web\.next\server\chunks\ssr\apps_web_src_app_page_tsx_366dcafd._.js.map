{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/web/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nconst TITLE_TEXT = `\n ██████╗ ███████╗████████╗████████╗███████╗██████╗\n ██╔══██╗██╔════╝╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗\n ██████╔╝█████╗     ██║      ██║   █████╗  ██████╔╝\n ██╔══██╗██╔══╝     ██║      ██║   ██╔══╝  ██╔══██╗\n ██████╔╝███████╗   ██║      ██║   ███████╗██║  ██║\n ╚═════╝ ╚══════╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝\n\n ████████╗    ███████╗████████╗ █████╗  ██████╗██╗  ██╗\n ╚══██╔══╝    ██╔════╝╚══██╔══╝██╔══██╗██╔════╝██║ ██╔╝\n    ██║       ███████╗   ██║   ███████║██║     █████╔╝\n    ██║       ╚════██║   ██║   ██╔══██║██║     ██╔═██╗\n    ██║       ███████║   ██║   ██║  ██║╚██████╗██║  ██╗\n    ╚═╝       ╚══════╝   ╚═╝   ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝\n `;\n\nexport default function Home() {\n\n  return (\n    <div className=\"container mx-auto max-w-3xl px-4 py-2\">\n      <pre className=\"overflow-x-auto font-mono text-sm\">{TITLE_TEXT}</pre>\n      <div className=\"grid gap-6\">\n        <section className=\"rounded-lg border p-4\">\n          <h2 className=\"mb-2 font-medium\">API Status</h2>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,aAAa,CAAC;;;;;;;;;;;;;;CAcnB,CAAC;AAEa,SAAS;IAEtB,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;0BAAqC;;;;;;0BACpD,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAQ,WAAU;8BACjB,cAAA,6WAAC;wBAAG,WAAU;kCAAmB;;;;;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}]}