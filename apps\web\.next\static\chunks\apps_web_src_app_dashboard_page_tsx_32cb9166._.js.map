{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/prj/my-erp/apps/web/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\nimport { authClient } from \"@/lib/auth-client\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const { data: session, isPending } = authClient.useSession();\n\n\n  useEffect(() => {\n    if (!session && !isPending) {\n      router.push(\"/login\");\n    }\n  }, [session, isPending]);\n\n  if (isPending) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div>\n      <h1>Dashboard</h1>\n      <p>Welcome {session?.user.name}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,8IAAA,CAAA,aAAU,CAAC,UAAU;IAG1D,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,WAAW,CAAC,WAAW;gBAC1B,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAS;KAAU;IAEvB,IAAI,WAAW;QACb,qBAAO,4TAAC;sBAAI;;;;;;IACd;IAEA,qBACE,4TAAC;;0BACC,4TAAC;0BAAG;;;;;;0BACJ,4TAAC;;oBAAE;oBAAS,SAAS,KAAK;;;;;;;;;;;;;AAGhC;GArBwB;;QACP,oQAAA,CAAA,YAAS;QACa,8IAAA,CAAA,aAAU,CAAC;;;KAF1B", "debugId": null}}]}