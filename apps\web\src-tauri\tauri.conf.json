{"$schema": "https://schema.tauri.app/config/2", "productName": "my-erp", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../.next", "devUrl": "http://localhost:3001", "beforeDevCommand": "pnpm run dev", "beforeBuildCommand": "pnpm run build"}, "app": {"windows": [{"title": "my-erp", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}